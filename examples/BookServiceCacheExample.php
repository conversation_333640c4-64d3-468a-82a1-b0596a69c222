<?php

/**
 * Example demonstrating how to use the BookService with caching control
 */

use App\Services\BookService;

// Example 1: Default behavior - caching is automatically disabled in debug mode
$bookService = new BookService();

// In debug mode (APP_DEBUG=true): caching is disabled
// In production mode (APP_DEBUG=false): caching is enabled
echo "Caching enabled: " . ($bookService->isCachingEnabled() ? 'Yes' : 'No') . "\n";

// Example 2: Force enable caching even in debug mode
$bookServiceWithCache = new BookService(true);
echo "Force enabled caching: " . ($bookServiceWithCache->isCachingEnabled() ? 'Yes' : 'No') . "\n";

// Example 3: Force disable caching even in production mode
$bookServiceWithoutCache = new BookService(false);
echo "Force disabled caching: " . ($bookServiceWithoutCache->isCachingEnabled() ? 'Yes' : 'No') . "\n";

// Example 4: Using the service methods - they work the same regardless of caching
$latestBooks = $bookService->getLatestBooks(10);
$mostReadBooks = $bookService->getMostReadBooksThisMonth(5);
$selectedBooks = $bookService->getSelectedBooks(8);
$categories = $bookService->getMainCategoriesWithBookCounts();

// When caching is disabled:
// - All queries execute directly without caching
// - Useful for development and debugging
// - Ensures you always get fresh data

// When caching is enabled:
// - Results are cached for the specified duration
// - Improves performance in production
// - Reduces database load

echo "Retrieved " . count($latestBooks) . " latest books\n";
echo "Retrieved " . count($mostReadBooks) . " most read books\n";
echo "Retrieved " . count($selectedBooks) . " selected books\n";
echo "Retrieved " . count($categories) . " categories\n";
