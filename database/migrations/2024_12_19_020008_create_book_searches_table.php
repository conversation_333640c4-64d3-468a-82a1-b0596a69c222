<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $connection = 'sqlite_search';
        $doseFileExist = file_exists( storage_path('app/private/database/search.sqlite'));
        //remove the file if it exists
        if ($doseFileExist) {
            unlink(storage_path('app/private/database/search.sqlite'));
        }
        //create the directory if it does not exist
        if (!file_exists(storage_path('app/private/database'))) {
            mkdir(storage_path('app/private/database'), 0777, true);
        }
        //copy empty.sqlite to search.sqlite
        copy(database_path('empty.sqlite'),  storage_path('app/private/database/search.sqlite'));

        // $doseTableExist = DB::connection($connection)->getSchemaBuilder()->hasTable('book_searches');
        // if ($doseTableExist) {
        //     // Drop the table if it exists
        //     DB::connection($connection)->statement('DROP TABLE IF EXISTS book_searches;');
        // }
        DB::connection($connection)
            ->getSchemaBuilder()
            ->create('book_searches', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('book_id');
                $table->text('book_title');
                $table->unsignedBigInteger('page_id');
                $table->unsignedBigInteger('page');
                $table->text('page_title')->nullable();
                $table->unsignedBigInteger('part')->nullable();
                $table->string('author_die_date')->nullable();
                $table->text('search');
                $table->text('search_footer')->nullable();
                $table->text('search_root')->nullable();
                $table->text('search_footer_root')->nullable();
                $table->text('hno')->nullable();
                $table->text('mno')->nullable();
                $table->timestamps();
            });

        DB::connection($connection)->statement('CREATE VIRTUAL TABLE books_search_fts USING fts5(id,book_id,search,search_footer,search_root,search_footer_root ,hno ,mno,tokenize="trigram");');

        DB::connection($connection)->statement('CREATE TRIGGER books_search_ai AFTER INSERT ON book_searches BEGIN
            INSERT INTO books_search_fts(id, book_id, search,search_footer,search_root,search_footer_root, hno ,mno) VALUES(new.id, new.book_id, new.search, new.search_footer, new.search_root, new.search_footer_root, new.hno, new.mno);
          END;');
        // DB::connection($connection)->statement('CREATE TRIGGER books_search_ad AFTER DELETE ON book_searches BEGIN
        //     INSERT INTO books_search_fts(id, search, search_footer, search_root, search_footer_root, hno ,mno) VALUES(old.id,old.search, old.search_footer, old.search_root, old.search_footer_root, old.hno, old.mno);
        //   END;');
        DB::connection($connection)->statement('CREATE TRIGGER books_search_ad AFTER DELETE ON book_searches BEGIN
        DELETE FROM books_search_fts WHERE id = old.id;
        END;');

        DB::connection($connection)->statement('CREATE TRIGGER books_search_au AFTER UPDATE ON book_searches BEGIN
            INSERT INTO books_search_fts(id,book_id, search, search_footer, search_root, search_footer_root, hno ,mno) VALUES(old.id,old.book_id,old.search, old.search_footer, old.search_root, old.search_footer_root, old.hno, old.mno);
            INSERT INTO books_search_fts(id, book_id, search,search_footer,search_root,search_footer_root ,hno ,mno) VALUES(new.id, new.book_id, new.search, new.search_footer, new.search_root, new.search_footer_root, new.hno, new.mno);
          END;');
    }

    // --           END;
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $connection = 'sqlite_search';
        Log::info("delete");
        DB::connection($connection)->statement('DROP TABLE IF EXISTS book_searches;');
    }
};
