<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->id();
            $table->string('hashId')->nullable();
            $table->string('title');
            $table->string('image')->nullable();
            $table->foreignId('author_id')->constrained('authors')->restrictOnDelete();
            $table->foreignId('added_by')->constrained('users')->restrictOnDelete();
            $table->text('summary')->nullable();
            $table->string('sqlite')->nullable();
            $table->string('sqlite_size')->nullable();
            $table->integer('download_count')->default(0); //! should be removed
            $table->integer('website')->default(1);
            $table->integer('application')->default(0);
            $table->integer('display_on_home')->default(0);
            $table->string('private_users')->nullable(); //* private users who can show the book
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
