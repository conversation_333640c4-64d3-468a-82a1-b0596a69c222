<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('seos', function (Blueprint $table) {
            $table->id();
            $table->string('page')->nullable();
            $table->string('name')->nullable();
            $table->string('num')->default(0);
            $table->string('title')->nullable();
            $table->string('ar_title')->nullable();
            $table->text('text')->nullable();
            $table->text('ar_text')->nullable();
            $table->string('keywords')->nullable();
            $table->string('ar_keywords')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seos');
    }
};
