<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fatawies', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id'); // Telegram User ID
            $table->string('username')->nullable();
            $table->text('question');
            $table->text('answer')->nullable();
            $table->boolean('answered')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fatawies');
    }
};
