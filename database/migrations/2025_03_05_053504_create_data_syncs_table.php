<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('data_syncs', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->longText('data');
            $table->unsignedInteger('user_id')->nullable()->constrained();
            $table->bigInteger('created_at_app')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_syncs');
    }
};
