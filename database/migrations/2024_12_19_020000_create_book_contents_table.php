<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_contents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('page_id');
            $table->unsignedBigInteger('book_id');
            $table->unsignedBigInteger('part')->nullable();
            $table->unsignedBigInteger('page');
            $table->unsignedBigInteger('pPage')->nullable();
            $table->unsignedBigInteger('pPart')->nullable();
            $table->text('nass');
            $table->text('hno')->nullable();
            $table->text('mno')->nullable();
            $table->text('html')->nullable();
            $table->foreign('book_id')->references('id')->on('books')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_contents');
    }
};
