<?php

namespace Database\Seeders;

use App\Models\User;
use App\RolesEnum;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        
        $user =  User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('ZiYdi@2025'),
        ]);
        $this->call(PermissionsSeeder::class);
        $user->assignRole(RolesEnum::SUPER_ADMIN);
        $this->call(\Database\Seeders\Models\BookSeeder::class);
    }
}
