<?php

use App\Events\NotificationsEvent;
use App\Http\Controllers\AppNotificationController;
use App\Http\Controllers\AppUpdateController;
use App\Http\Controllers\AuthorController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ContactMessageController;
use App\Http\Controllers\FaqsController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\Front\BookPageController;
use App\Http\Controllers\Front\FronV2Controller;
use App\Http\Controllers\PopularSearchController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SearchHistoryController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\pages\DashboardController;
use App\Http\Controllers\QueueController;
use App\Http\Middleware\CommonData;
use App\Http\Middleware\FrontMiddleware;
use App\Models\Book;
use App\Models\BookFiles;
use Illuminate\Support\Facades\Route;
use Modules\TrafficLogs\controller\TrafficLogsController;
use Modules\Blog\Controller\BlogCategoryController;
use Modules\Blog\Controller\BlogController;
use Illuminate\Support\Facades\Cache;
use Modules\Blog\Models\Blog;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

// Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
// https://ziydia.com/uploads/books_db/1719280515.zip
Route::get('/uploads/books_db/{file}', function ($file) {
    $dbPath = database_path('books_old.sqlite');
    $db = new SQLite3($dbPath);

    $rows = $db->query('SELECT * FROM books_old WHERE sqlite LIKE "%' . $file . '%"');
    if (!$rows) {
        return response('File not found', 404);
    }
    $books = [];
    while ($row = $rows->fetchArray(SQLITE3_ASSOC)) {
        $books[] = $row;
    }
    if (count($books) == 0) {
        return response('File not found', 404);
    }
    $db->close();
    $book = Book::find($books[0]['id']);
    if (!$book) {
        return response('Book not found', 404);
    }
    return redirect(config('filesystems.disks.spaces.url') . '/books/' . $book->id . '/' . $book->hashId . '.zip');
});
// https://ziydia.com/uploads/books_files/0-1656968036.pdf
Route::get('/uploads/books_files/{file}', function ($file) {
    //redirect to the file
    // $bookFiles = DB::select("SELECT * FROM book_files WHERE file_path LIKE '%1613784647%'");
    $bookFiles = BookFiles::where('file_path', 'like', '%' . $file . '%')->get();
    if (count($bookFiles) == 0) {
        return response('File not found', 404);
    }
    $encodedFile = rawurlencode($file);
    return redirect(config('filesystems.disks.spaces.url') . '/books/' . $bookFiles[0]->book->id . '/' . $encodedFile);
});
Route::get('/uploads/books/{file}', function ($file) {
    //redirect to the file
    // $bookFiles = DB::select("SELECT * FROM book_files WHERE file_path LIKE '%1613784647%'");
    $bookFiles = BookFiles::where('file_path', 'like', '%' . $file . '%')->get();
    if (count($bookFiles) == 0) {
        if (str_contains($file, '1718463935')) {
            return redirect('https://s3.ziydia.com/books/743/KdPRJpPa.jpg');
        }
        return response('File not found', 404);
    }
    $encodedFile = rawurlencode($file);
    return redirect(config('filesystems.disks.spaces.url') . '/books/' . $bookFiles[0]->book->id . '/' . $encodedFile);
});

Route::group(['namespace' => 'App\Http\Controllers\Front', 'middleware' => CommonData::class], function () {
    Route::post('/Reset_Pass', 'ResetPasswordController@change_password')->name('oldpassword.update');
    Route::get('/Reset-Pass/{code}', 'ResetPasswordController@index');

    // Use the new V3 home page as main homepage
    Route::get('/', 'HomeController@index')->name('home');

    Route::get('/Contact-Us', 'ContactController@index');
    Route::post('/Get-In-Touch', 'ContactController@send_message');

    // New contact page v2
    Route::get('/contact-us', 'ContactController@contact_us')->name('contact-us');
    Route::post('/contact-submit', 'ContactController@submit_contact')->name('contact.submit');

    Route::get('/About-Us', 'AboutController@index');

    Route::get('/Terms-of-Service', 'AboutController@policy');
    Route::get('/Terms-of-Service-app', 'AboutController@policyApp');

    Route::get('/Password-Changed', 'ResetPasswordController@password_changed');

    Route::get('/Authors', 'AuthorController@index');
    Route::get('/Author/{id}', 'AuthorController@show');
    Route::get('/Authors-Books/{id}', 'AuthorController@books')->name('author.show');

    Route::get('/All-Books', 'BookController@all_books');
    Route::get('/Books-Cat/{id}', 'BookController@Books_Cat');
    Route::get('/category', 'BookController@Books_Cat')->name('category.show');
    // Add new AuthorsFilterComponent route
    Route::get('/authors', 'FronV2Controller@authorsLivewire')->name('front.authors.livewire');

    Route::get('/book_search', 'BookController@book_search');
    Route::get('/Book/{id}', 'BookController@book');
    Route::get('/book/{id}', 'BookController@book')->name('book.show');
    Route::get('/add-download/{id}', 'BookController@download_book');

    // Add new autocomplete route
    Route::get('/autocomplete', 'BookController@autocomplete');

    Route::get('/ComprehensiveLibrary', 'SiteController@Comprehensiv_Library');
    Route::get('/sort_by', 'SiteController@sort_books');
    Route::get('/sort_by_author', 'SiteController@sort_others');
});

Route::prefix('admin')
    ->middleware([
        FrontMiddleware::class
    ])
    ->group(function () {
        Route::get('/v2', [FronV2Controller::class, 'index']);
        Route::get('/v2/books', [FronV2Controller::class, 'books']);
        Route::get('/v2/book/{id}', [FronV2Controller::class, 'book'])->name('front.book.show');
        Route::get('/v2/authors', [FronV2Controller::class, 'authors'])->name('front.authors');
        Route::get('/v2/categories', [FronV2Controller::class, 'categories'])->name('front.categories');
        Route::get('/v2/categories/{id}', [FronV2Controller::class, 'category'])->name('front.categories.show');
        Route::get('/v2/authors/{id}', [FronV2Controller::class, 'author'])->name('front.authors.show');
        Route::get('/v2/about-us', [FronV2Controller::class, 'aboutUs'])->name('front.about-us');
        Route::get('/v2/terms-of-service', [FronV2Controller::class, 'termsOfService'])->name('front.terms-of-service');
        Route::get('/v2/contact-us', [FronV2Controller::class, 'contactUs'])->name('front.contact-us');
        Route::get('/v2/search', [FronV2Controller::class, 'advancedSearch'])->name('front.advanced-search');
    });

/* -------------------------------- FRONT-END ------------------------------- */
Route::group(['middleware' => CommonData::class], function () {
    Route::get('/book/{id}/page/{pageId}/{part?}', [BookPageController::class, 'pageId'])->name('front.book.page');
    Route::get('/book/{id}/p/{page}/{part?}', [BookPageController::class, 'page'])->name('front.book.page');

    Route::get('/book/{id}/title/{titleId}', [BookPageController::class, 'title'])->name('front.book.title');
    Route::get('/book/{book}/read', [BookPageController::class, 'show'])->name('front.book.read');
    Route::get('/book/{book}/pdf/{part}', [BookController::class, 'pdfByPart'])->name('front.book.pdf.part');
    Route::get('/book/{id}/search/{term}', [BookPageController::class, 'search'])
        ->name('front.book.search')
        ->where('term', '[\w\s]+');

    Route::get('/admin/test/page', [BookPageController::class, 'test']);
    Route::get('/book/{id}/clear-cache', [BookPageController::class, 'clearSpecificBookCache']);
});

Route::middleware(['auth'])
    ->get('/dashboard', [DashboardController::class, 'index'])
    ->name('dashboard');
Route::prefix('admin')
    ->middleware('auth')
    ->group(function () {
        Route::get('/', [DashboardController::class, 'index'])->name('home');
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::get('/deleteAllFiles', [DashboardController::class, 'deleteAllFiles'])->name('deleteAllFiles');
        Route::get('/homeCreateBook', [DashboardController::class, 'testCreateBook'])->name('homeCreateBook');
        Route::get('/homeTestButton', [DashboardController::class, 'homeTestButton'])->name('homeTestButton');
        Route::resource('users', UserController::class);
        Route::post('users/{user}', [UserController::class, 'sendNotificationToUser'])->name('users.send.notification');
        Route::resource('roles', RoleController::class);
        Route::resource('categories', CategoryController::class);
        Route::resource('app-updates', AppUpdateController::class);
        Route::resource('app-notifications', AppNotificationController::class);
        Route::resource('contact-messages', ContactMessageController::class);
        Route::post('contact-messages/{contactMessage}/reply', [ContactMessageController::class, 'reply'])->name('contact-messages.reply');
        Route::resource('search-histories', SearchHistoryController::class);
        Route::resource('popular-searches', PopularSearchController::class);
        Route::resource('pages', PageController::class);
        Route::resource('faqs', FaqsController::class);

        // File Manager Routes
        Route::prefix('files')
            ->name('files.')
            ->group(function () {
                Route::get('/search', [FileManagerController::class, 'search'])->name('search');
                Route::get('/{path?}', [FileManagerController::class, 'index'])
                    ->name('index')
                    ->where('path', '.*');

                Route::post('/store', [FileManagerController::class, 'store'])->name('store');
                Route::post('/store2', [FileManagerController::class, 'store2'])->name('store2');
                Route::delete('/destroy', [FileManagerController::class, 'destroy'])->name('destroy');
                Route::post('/bulk-delete', [FileManagerController::class, 'bulkDelete'])->name('bulk-delete');
                Route::post('/download', [FileManagerController::class, 'download'])->name('download');
                Route::post('/download-directory', [FileManagerController::class, 'downloadDirectory'])->name('download-directory');
                Route::post('/file-details', [FileManagerController::class, 'getFileDetails'])->name('file-details');
                Route::post('/rename', [FileManagerController::class, 'rename'])->name('rename');
                Route::post('/paste/{filename}', [FileManagerController::class, 'paste'])->name('paste');
            });

        Route::prefix('file-manager')
            ->controller(FileManagerController::class)
            ->group(function () {
                Route::get('/files/{path?}', 'index')->where('path', '.*')->name('files.index');
                Route::post('/files/store/{disk?}', 'store')->name('files.store');
                Route::post('/files', 'store2')->name('files.store2');
                // Route::delete('/files/{filename}', 'destroy')->name('files.destroy');
                Route::post('/download/file', 'download')->name('files.download');
            });

        Route::resource('authors', AuthorController::class);

        Route::post('/books/home/<USER>', [BookController::class, 'updateHomeBooks'])->name('books.home.update');
        Route::post('/books/{book}/toggle-website', [BookController::class, 'toggleWebsite'])->name('books.toggle-website');
        Route::post('/books/{book}/toggle-application', [BookController::class, 'toggleApplication'])->name('books.toggle-application');
        Route::post('/books/{book}/private-users', [BookController::class, 'updatePrivateUsers'])->name('books.private-users');
        Route::get('/books/clear-statistics-cache', [BookController::class, 'clearStatisticsCache'])->name('books.clear-statistics-cache');
        Route::resource('books', BookController::class);
        Route::get('/book/{book}/report', [App\Http\Controllers\BookReportController::class, 'show'])->name('books.report');
        Route::get('/book/{book}/pdf', [BookController::class, 'pdfs'])->name('book.pdfs');
        Route::post('/book/pdf/store', [BookController::class, 'storePdf'])->name('book.pdfs.store');
        Route::get('/book/pdf/download/{id}', [BookController::class, 'downloadPdf'])->name('book.pdf.download');
        Route::put('/book/pdf/update/{bookFile}', [BookController::class, 'updatePdf'])->name('book.pdf.update');
        Route::delete('/book/pdf/delete/{bookFile}', [BookController::class, 'deletePdf'])->name('book.pdf.delete');
        Route::put('/books/{book}/update-date', [BookController::class, 'updateDate'])->name('books.update-date');
        Route::put('/books/{book}/update-created-date', [BookController::class, 'updateCreatedDate'])->name('books.update-created-date');

        Route::get('/page/{slug}', [PageController::class, 'open'])->name('page');

        Route::resource('settings', SettingsController::class);

        Route::get('/traffic', [TrafficLogsController::class, 'index'])->name('traffic.index');
        Route::get('/traffic/logs/{id}', [TrafficLogsController::class, 'logs'])->name('traffic.logs');

        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
        Route::get('/profile/notifications', [ProfileController::class, 'notifications'])->name('profile.notifications');
        Route::delete('/profile/notifications/{notification}', [ProfileController::class, 'deleteNotification'])->name('profile.notifications.delete');

        //* Short Urls
        Route::get('/short-urls', [ShortUrlController::class, 'index'])->name('short-urls.index');
        Route::post('/short-urls', [ShortUrlController::class, 'create'])->name('short-urls.store');
        Route::put('/short-urls/{shortUrl}', [ShortUrlController::class, 'update'])->name('short-urls.update');
        Route::delete('/ /{shortUrl}', [ShortUrlController::class, 'destroy'])->name('short-urls.destroy');

        //*Blogs
        Route::resource('blog-categories', BlogCategoryController::class);
        Route::resource('blogs', BlogController::class);
        Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog');

        //* Queue

        Route::get('/queue/cancel/{id}', [QueueController::class, 'cancelJob'])->name('queue.cancel');
        Route::get('/queue/retry-all', [QueueController::class, 'retryAllFailedJobs']);
        Route::get('/queue/retry/{id}', [QueueController::class, 'retryFailedJob'])->name('queue.retry');
        Route::get('/queue/failed-jobs', [QueueController::class, 'listFailedJobs']);
        Route::get('/queue/flush', [QueueController::class, 'flushFailedJobs']);

        Route::get('/debug/merge', [DashboardController::class, 'debugMerge'])->name('debug.merge');
    });

Route::get('/srt/{shortCode}', [ShortUrlController::class, 'redirect']);

Route::get('/admin/test', [DashboardController::class, 'test']);
Route::get('/test-cache', function () {
    Cache::put('test_key', 'test_value', 10); // Cache for 10 minutes
    $value = Cache::get('test_key');
    return $value;
});

Route::get('/test-notification', function () {
    NotificationsEvent::dispatch('Hello World');
    return 'Notification sent';
});

Route::get('/robots.txt', function () {
    return response("User-agent: *\nDisallow: /", 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

require __DIR__ . '/auth.php';

// prevent robots to navigate to admin , telescope , horizon and log-viewer routes

Route::get('/robots.txt', function () {
    $content = <<<EOT
    User-agent: *
    Disallow: /admin
    Disallow: /telescope
    Disallow: /horizon
    Disallow: /log-viewer
    Disallow: /queue
    Disallow: /profile
    Disallow: /short-urls
    Disallow: /uploads

    # Allow public routes
    Allow: /book
    Allow: /blog
    Allow: /Authors
    Allow: /All-Books
    Allow: /Contact-Us
    Allow: /About-Us

    Sitemap: https://ziydia.com/sitemap.xml
    EOT;

    return response($content, 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create()->add(Url::create('/'))->add(Url::create('/Contact-Us'))->add(Url::create('/About-Us'))->add(Url::create('/Authors'))->add(Url::create('/All-Books'))->add(Url::create('/blog'))->add(Url::create('/Terms-of-Service'));

    // Add dynamic routes (e.g., books and blogs)
    $books = Book::all();
    foreach ($books as $book) {
        $sitemap->add(Url::create("/book/{$book->id}"));
    }

    $blogs = Blog::all();
    foreach ($blogs as $blog) {
        $sitemap->add(Url::create("/blog/{$blog->slug}"));
    }

    return $sitemap->toResponse(request());
});

Route::get('/dashboard/refresh', [DashboardController::class, 'refreshDashboard'])->name('dashboard.refresh');

// Artisan Commands
Route::post('/artisan/command', [App\Http\Controllers\ArtisanController::class, 'executeCommand'])->name('artisan.command');
