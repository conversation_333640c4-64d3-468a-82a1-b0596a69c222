@import 'tailwindcss';

@plugin "daisyui";

/* supported colors daisyui

primary
primary-content
secondary
secondary-content
accent
accent-content
neutral
neutral-content
base-100
base-200
base-300
base-content
info
info-content
success
success-content
warning
warning-content
error
error-content

*/

.text-onsearch {color: #6D7179; }
.text-primaryDark {color: #0061A4 ;}
.text-onprimary {color: #FFFFFF ;}
.text-primaryColorLight {color: #9ECBFF ;}
.text-primarycontainer {color: #515E7B ;}
.text-secondary {color: #535F70 ;}
.text-onsecondary {color: #FFFFFF ;}
.text-surface {color: #F0F3F8 ;}
.text-divider {color: #D5D8DF ;}
.text-background {color: #F8F9FF ;}
.text-backgroundV2 {color: #F8F9FF ;}
.text-iconsV2 {color: #1E3050 ;}
.text-appBarColor {color: #FDFCFF ;}
.text-icons {color: #1E3050 ;}
.text-onIcon {color: #A5ACB9 ;}
.text-iconsColorDark {color: #43474E ;}
.text-error {color: #BA1A1A ;}
.text-onerror {color: #FFFFFF ;}
.text-textPrimary {color: #43474E ;}
.text-white {color: #FFFFFF ;}
.text-black {color: #191C20 ;}
.text-onbackground {color: #191C20 ;}
.text-search {color: #E6E8EE ;}
.text-filled {color: #DDDEE4 ;}
.text-onFilled {color: #A6A8AE ;}
.text-shadow {color: #00000040 ;}
.text-marker {color: #FEDD00 ;}
.text-onprimarycontainer {color: #F0F1F3 ;}

.dark .text-onsearch { color: #A5A8AF; }
.dark .text-white { color: #000000; }
.dark .text-black { color: #FFFFFF; }
.dark .text-icons { color: #FFFFFF; }


/* Accent color used folr icons */





@plugin "daisyui/theme" {
  name: "ziydia-light";

  default: true;
  color-scheme: light;

  --color-primary: #146EBE;  
  --color-primary-content: #FFFFFF;
  --color-onprimary-container: #003258;

  --color-neutral: #000000;

  --color-filled: #DDDEE4;
  --color-on-filled: #A6A8AE;

  --color-background-v2: #F8F9FF;

  --color-surface: #F0F3F8;
  --color-divider: #D5D8DF;

  --color-secondary: #535F70;
  --color-secondary-focus: #3A4450;
  --color-secondary-content: #FFFFFF;

  /* icons color */
  --color-accent: #1E3050;
  --color-accent-content: #FFFFFF;

  --color-base-100: #F8F9FF;
  --color-base-200: #F0F3F8;
  --color-base-300: #E6E8EE;
  --color-base-content: #43474E;


  --color-info: #1E3050;
  --color-info-content: #FFFFFF;

  --color-success: #4CAF50;
  --color-success-content: #FFFFFF;

  --color-warning: #FEDD00;
  --color-warning-content: #191C20;

  --color-error: #BA1A1A;
  --color-error-content: #FFFFFF;


  --color-text-aya: #00AA00;
  --color-text-hash: #464646;
  --color-text-matn: #0000fa;
  --color-text-title: #800000;
  --color-text-number: #be0000;
  --color-text-base: #000000;

  --rounded-box: 0.5rem;
  --rounded-btn: 0.25rem;
  --rounded-badge: 0.125rem;
  --animation-btn: 0.25s;
  --animation-input: 0.2s;
  --btn-focus-scale: 0.95;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.25rem;
}

@plugin "daisyui/theme" {
  name: "ziydia-dark";
  color-scheme: dark;

  --color-primary: #A0CAFD;
  --color-primary-focus: #9ECBFF;
  --color-primary-content: #003258;

  --color-neutral: #ffffff;

  --color-primary-container: #146EBE;
  --color-onprimary-container: #A0CAFD;

  --color-filled: #2A2D31;
  --color-on-filled: #A6A8AE;

  --color-netural: #111418;
  --color-background-v2: #333333;

  --color-surface: #202529;
  --color-divider: #33363C;

  --color-secondary: #BBC7DB;
  --color-secondary-focus: #A6B2C6;
  --color-secondary-content: #000000;

  --color-accent: #FFFFFF;
  --color-accent-content: #003258;

  --color-base-100: #111418;
  --color-base-200: #202529;
  --color-base-300: #272A2F;
  --color-base-content: #E2E2E6;


  --color-info: #FFFFFF;
  --color-info-content: #1D2024;

  --color-success: #86EFAC;
  --color-success-content: #052e16;

  --color-warning: #FEDD00;
  --color-warning-content: #1A1712;

  --color-error: #FFB4AB;
  --color-error-content: #690005;


  --color-text-aya: #00D100;
  --color-text-hash: #B3B3B3;
  --color-text-matn: #ADADFF;
  --color-text-title: #FF9999;
  --color-text-number: #DCA7A7;
  --color-text-base: #000000;
  
  --rounded-box: 0.5rem;
  --rounded-btn: 0.25rem;
  --rounded-badge: 0.125rem;
  --animation-btn: 0.25s;
  --animation-input: 0.2s;
  --btn-focus-scale: 0.95;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.25rem;
}