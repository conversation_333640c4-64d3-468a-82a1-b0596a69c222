@extends('front_v2.layouts.layout')
@section('title', $book->title)

@section('page-meta')
@endsection

@section('page-style')
@endsection


@section('content')

    <main class="flex-grow">

        @include('front_v2.page_partials.hero', [
            'title' => $book->title,
            'description' =>
                $book->author->name .
                ' ' .
                $book->author->formattedDeathDate2() .
                '</br ></br >' .
                $book->author->books_count .
                ' كتاب',
        ])

        <!-- Main Content -->
        <div class="container mx-auto px-4 py-8">


            <div class="p-2">
                    <!-- First column - Book info and author -->
                    <div class="flex flex-wrap gap-4 col-span-1">
                        <div class="hidden sm:block">
                            <img src="{{ $book->image }}" alt="{{ $book->name }}"
                                class="h-90 rounded-sm mx-auto md:mx-0" />
                        </div>
                        <div class="block sm:hidden items-center justify-center mx-auto">
                            <img src="{{ $book->image }}" alt="{{ $book->name }}"
                                class="h-[500px] rounded-sm mx-auto md:mx-0" />
                        </div>
                        <div class="flex flex-col gap-1 w-full sm:w-auto">
                            <div class="flex items-center">
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($book->categories as $category)
                                        <a href="/category/{{ $category->id }}"
                                            class="badge badge-primary hover:badge-secondary transition-colors duration-300">
                                            {{ $category->title }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>

                            <!-- عدد التحميلات -->
                            <h2 class>عدد التحميلات: {{ $book->sqliteDownloadsCount() }}</h2>
                            <h2 class>تاريخ الاضافة: {{ date('d-m-Y ', strtotime($book->created_at)) }}</h2>
                            <h2 class>عدد المشاهدات: {{ $book->webViewCount() }}</h2>

                            <!-- add spacer -->
                            <div class="book_share_btns">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ url('Book/' . $book->id) }}"
                                    class="btn btn-default noor-btn " target="_blank"><i
                                        class="fab fa-facebook-square"></i></a>
                                <a href="https://twitter.com/share?url={{ url('Book/' . $book->id) }}"
                                    class="btn btn-default noor-btn " target="_blank"><i class="fab fa-twitter"></i> </a>
                                <a href="https://api.whatsapp.com/send?text={{ url('Book/' . $book->id) }}"
                                    class="btn btn-default noor-btn " target="_blank"><i class="fab fa-whatsapp"></i></a>
                                <a href="https://telegram.me/share/url?url={{ url('Book/' . $book->id) }}"
                                    class="btn btn-default noor-btn" target="_blank"><i class="fab fa-telegram"></i></a>
                            </div>
                            @if($book->canView())
                            <div class="book_share_btns">
                                <a href="{{ url('book/' . $book->id . '/read') }}" class="btn btn-default noor-btn w-full">
                                    ابدأ القراءة</a>
                            </div>
                            @endif

                            @if ($book->files->where('file_number', 1)->count() > 0)
                                @if (count($book->files->where('file_number', 1)) == 1)
                                    <div class="book_share_btns">
                                        <a href="{{ url('add-download/' . $book->files[0]->id) }}?v={{ strtotime('now') }}"
                                            target="_blank" class="btn btn-default noor-btn w-full"><i
                                                class="fa fa-file-pdf text-accent"></i>
                                            تحميل </a>
                                    </div>
                                @else
                                    <div class="dropdown">
                                        @php

                                        @endphp
                                        <div class="book_share_btns">
                                            <div tabindex="0"
                                                class="h-10 btn btn-default noor-btn w-full justify-between">
                                                تحميل
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50"
                                                    aria-hidden="true">
                                                    <path d="m7 15 5 5 5-5"></path>
                                                    <path d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                            <ul tabindex="0"
                                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[140px] p-2 mt-1 border-base-300 border w-full">

                                                @foreach ($book->files->where('file_number', 1) as $file)
                                                    <li><a href="{{ url('add-download/' . $file->id) }}?v={{ strtotime('now') }}"
                                                            target="_blank" class="btn btn-default noor-btn m-1"><i
                                                                class="fa fa-file-pdf text-accent"></i>
                                                            الجزء <span>{{ $loop->iteration }}</span></a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>

                                @endif
                            @endif




                        </div>
                           <!-- Second column - Download and read buttons -->
                    <div class="flex items-start  col-span-2 lg:mr-4 lg:flex-grow lg:w-[50px] w-full">
                        <!-- Refined Card-Based Tabs Design -->
                        <div class="w-full">
                            <!-- Centered, Smaller Card-Style Tab Navigation -->

                            <!-- Content Container with Fade Animation -->
                            <div class="bg-base-100 rounded-lg overflow-hidden border border-base-300 mx-auto w-full">
                                <!-- Animated Content Wrapper -->
                                <div class="relative">
                                    <!-- Book Info Content -->
                                    <div  class="p-2 transition-opacity duration-300 ease-in-out">
                                        <div class="mb-4 flex items-center">
                                            <div id="book-tab" class="flex items-center mb-4 cursor-pointer" onclick="switchTab('book')">
                                                <div id="book-tab-indicator" class="h-6 w-1 bg-primary rounded-full mr-1"></div>
                                                <h3 class="text-lg font-bold mr-2">معلومات الكتاب</h3>
                                            </div>
                                            <div id="author-tab" class="flex items-center mb-4 mr-8 cursor-pointer" onclick="switchTab('author')">
                                                <div id="author-tab-indicator" class="h-6 w-1 rounded-full mr-1"></div>
                                                <h3 class="text-lg font-bold mr-2">نبذة عن المؤلف</h3>
                                            </div>
                                        </div>
                                        <div class="w-full relative" id="book-content">
                                            <p
                                                class="text-base-content leading-relaxed h-[280px] overflow-y-auto pr-4 select-text cursor-text text-justify">
                                                {!! nl2br(e($book->summary)) !!}</p>
                                        </div>
                                        <div class="w-full absolute opacity-0" id="author-content">
                                            <p
                                                class="text-base-content leading-relaxed h-[280px] overflow-y-auto pr-4 select-text cursor-text text-justify">
                                                {!! nl2br(e($book->author->description)) !!}</p>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    </div>

                 







                <!-- Simplified Tab Switching Script with Fade Animation -->
                <script>
                    // Initialize tabs on page load
                    document.addEventListener('DOMContentLoaded', function() {
                        // Set default tab to 'book'
                        switchTab('book');
                    });
                    
                    function switchTab(tabName) {
                        const bookTab = document.getElementById('book-tab');
                        const authorTab = document.getElementById('author-tab');
                        const bookContent = document.getElementById('book-content');
                        const authorContent = document.getElementById('author-content');
                        const bookTabIndicator = document.getElementById('book-tab-indicator');
                        const authorTabIndicator = document.getElementById('author-tab-indicator');

                        if (tabName === 'book') {
                            // Activate Book Tab
                            bookTabIndicator.classList.add('bg-primary');
                            bookTab.querySelector('h3').classList.add('text-primary');
                            
                            // Deactivate Author Tab
                            authorTabIndicator.classList.remove('bg-primary');
                            authorTab.querySelector('h3').classList.remove('text-primary');

                            // Fade out Author Content
                            authorContent.classList.add('opacity-0');

                            // After short delay, reposition and fade in Book Content
                            setTimeout(() => {
                                authorContent.style.position = 'absolute';
                                bookContent.style.position = 'relative';
                                bookContent.classList.remove('opacity-0');
                            }, 300);

                        } else {
                            // Activate Author Tab
                            authorTabIndicator.classList.add('bg-primary');
                            authorTab.querySelector('h3').classList.add('text-primary');
                            
                            // Deactivate Book Tab
                            bookTabIndicator.classList.remove('bg-primary');
                            bookTab.querySelector('h3').classList.remove('text-primary');

                            // Fade out Book Content
                            bookContent.classList.add('opacity-0');

                            // After short delay, reposition and fade in Author Content
                            setTimeout(() => {
                                bookContent.style.position = 'absolute';
                                authorContent.style.position = 'relative';
                                authorContent.classList.remove('opacity-0');
                            }, 300);
                        }
                    }
                </script>

                <!-- Author's books section -->
                @if ($book->author->books()->where('id', '!=', $book->id)->take(6)->get()->count() > 0)
                    <!-- احدث الكتب -->
                    <x-book-slider :title="'كتب أخرى للمؤلف'" :books="$book->author->books" sectionId="author" :emptyMessage="'لا توجد كتب لعرضها حالياً'" />
                @endif

                <!-- similar books -->
                @if ($similarBooks->count() > 0)
                    <!-- احدث الكتب -->
                    <x-book-slider :title="'كتب مشابهه'" :books="$similarBooks" sectionId="similar" :emptyMessage="'لا توجد كتب لعرضها حالياً'" />
                @endif







            </div>
    </main>
@endsection


@section('page-script')

@endsection
