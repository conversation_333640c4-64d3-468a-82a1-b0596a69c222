@extends('front_v2.layouts.layout')
@section('title', 'عن الموقع')

@section('page-meta')
@endsection

@section('page-style')
<style>
    @font-face {
        font-family: 'Ziydia';
        src: url("{{asset('assets/fonts/Ziydia.ttf') }}") format('truetype');
    }
    @font-face {
        font-family: 'Ziydia-Bold';
        src: url("{{asset('assets/fonts/Ziydia-B.ttf') }}") format('truetype');
    }
    @font-face {
        font-family: 'Ziydia-Q';
        src: url("{{asset('assets/fonts/Ziydia-Q.ttf') }}") format('truetype');
    }
    /* applay font ziydia to all text */
    body {
        font-family: 'Ziydia', sans-serif;
    }
    p {
        font-family: 'Ziydia', sans-serif;
    }
    span {
        font-family: 'Ziydia', sans-serif;
    }
   
</style>
@endsection


@section('content')


<main class="flex-grow">

    @include('front_v2.page_partials.hero', [
    'title' => 'عن الموقع',
    'description' => 'المكتبة الزيدية هي مكتبة إلكترونية متخصصة في علوم أهل البيت عليهم السلام، تهدف إلى نشر المعرفة وتسهيل الوصول إلى المصادر العلمية.'
    ])

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col" id="bookContent">
            <!-- Main Content Section -->
             {!! $about->value !!}
        </div>
    </div>
</main>

@endsection

@section('page-script')
<script>
      document.addEventListener('DOMContentLoaded', function() {
        initThemeTogglez();
        });

        function initThemeTogglez() {
            const html = document.documentElement;
            const themeToggle = document.getElementById('theme-toggle');
            const themeToggleMobile = document.getElementById('theme-toggle-mobile');
            const currentTheme = localStorage.getItem('theme') || 'light';
     
            themeToggle?.addEventListener('click', function() {
                updateContentColors(html.getAttribute('data-theme') === 'ziydia-light' ?  'light' : 'dark');

            });
            themeToggleMobile?.addEventListener('click', function() {
                updateContentColors(html.getAttribute('data-theme') === 'ziydia-light' ? 'dark' : 'light');
            });
        }


      function updateContentColors(theme) {
        const content = document.getElementById('bookContent');


        if (content) {
            if (theme === 'dark') {
            content.innerHTML = content.innerHTML.split('#00AA00').join('#00D100');
            content.innerHTML = content.innerHTML.split('#464646').join('#B3B3B3');
            content.innerHTML = content.innerHTML.split('#0000fa').join('#ADADFF');
            content.innerHTML = content.innerHTML.split('#800000').join('#FF9999');
            content.innerHTML = content.innerHTML.split('#be0000').join('#DCA7A7');
            content.innerHTML = content.innerHTML.split('#000000').join('#B3B3B3');
            content.innerHTML = content.innerHTML.split('#0000D6').join('#ADADFF');
            content.innerHTML = content.innerHTML.split('rgba(0, 0, 0, 0.2)').join('rgba(255, 255, 255, 0.2)');
            } else {
            content.innerHTML = content.innerHTML.split('#00D100').join('#00AA00');
            content.innerHTML = content.innerHTML.split('#B3B3B3').join('#464646');
            content.innerHTML = content.innerHTML.split('#ADADFF').join('#0000fa');
            content.innerHTML = content.innerHTML.split('#FF9999').join('#800000');
            content.innerHTML = content.innerHTML.split('#DCA7A7').join('#be0000');
            content.innerHTML = content.innerHTML.split('#B3B3B3').join('#000000');
            content.innerHTML = content.innerHTML.split('#ADADFF').join('#0000D6');
            }
        }
    }
</script>
@endsection