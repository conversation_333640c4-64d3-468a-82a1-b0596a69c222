<!-- Authors Grid -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    @forelse($authors as $author)
        <livewire:author-card :author="$author" :key="$author->id" />
    @empty
        <div class="col-span-full">
            <div class="flex flex-col items-center justify-center py-12 text-base-content/70">
                <i class="fas fa-users-slash text-4xl mb-4"></i>
                <p class="text-lg font-medium">لم يتم العثور على مؤلفين</p>
                <p class="text-sm mt-2">حاول البحث بكلمات مختلفة</p>
            </div>
        </div>
    @endforelse
</div>


    <!-- Pagination -->
    <div class="mt-8 flex justify-center" dir="rtl">
        <div class="join gap-1">
            @if($authors->onFirstPage())
            @else
                <a href="{{ $authors->previousPageUrl() }}" class="join-item btn btn-ghost ">‹ السابق</a>
            @endif
            
            @php
                $start = max($authors->currentPage() - 2, 1);
                $end = min($start + 2, $authors->lastPage());
                if ($end - $start < 2) {
                    $start = max($end - 2, 1);
                }
                
                function toArabicNumbers($number) {
                    $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                    return str_replace($western, $arabic, $number);
                }
            @endphp

            @if($start > 1)
                <a href="{{ $authors->url(1) }}" class="join-item btn btn-ghost  {{ $authors->currentPage() == 1 ? 'btn-active' : '' }}">{{ toArabicNumbers('1') }}</a>
                @if($start > 2)
                    <button class="join-item btn btn-ghost  btn-disabled">...</button>
                @endif
            @endif

            @for($i = $start; $i <= $end; $i++)
                <a href="{{ $authors->url($i) }}" class="join-item btn btn-ghost  {{ $authors->currentPage() == $i ? 'border border-base-300' : '' }}">{{ toArabicNumbers($i) }}</a>
            @endfor

            @if($end < $authors->lastPage())
                @if($end < $authors->lastPage() - 1)
                    <button class="join-item btn btn-ghost  btn-disabled">...</button>
                @endif
                <a href="{{ $authors->url($authors->lastPage()) }}" class="join-item btn btn-ghost rounded-sm  {{ $authors->currentPage() == $authors->lastPage() ? 'btn-active' : '' }}">{{ toArabicNumbers($authors->lastPage()) }}</a>
            @endif
            
            @if($authors->hasMorePages())
                <a href="{{ $authors->nextPageUrl() }}" class="join-item btn btn-ghost ">التالي ›</a>
            @else
            @endif
        </div>
    </div>