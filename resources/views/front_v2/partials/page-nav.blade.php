<!-- desktop menu start here -->
<header class="bg-base-300 sticky top-0 z-50">
    <div class="py-3">
    <div class="container mx-auto px-4">
    <div class="flex flex-wrap items-center justify-between">
        <!-- Mobile Menu Toggle Button -->

                <!-- LOGO -->
                <div class="w-auto mb-4 md:mb-0 flex items-center space-x-2 rtl:space-x-reverse">
                    <a href="{{ url('/') }}" class="flex items-center">
                        <img src="{{ asset('assets/logo.svg') }}" alt="المكتبة الزيدية" class="img-fluid h-10" />
                        <span class="text-2xl font-bold text-onsearch mr-2 mt-2" style="font-family: 'logo';">المكتبة الزيدية</span>
                    </a>
                </div>


                <div class="flex items-center gap-4 ">


                    <button id="theme-toggle-mobile" class="p-2  text-on-filled focus:outline-none block xl:hidden">
                        <span id="theme-icon-mobile"><i class="fas fa-moon hidden"></i></span>
                    </button>


                    <!-- Mobile Search Icon Button -->
                    <div class="block xl:hidden">
                        <button @click="showSearchModal = true" class="text-on-filled focus:outline-none p-2">
                            <i class="fas fa-search text-xl"></i>
                        </button>
                    </div>
                    @if(isset($book) && $book->hasContent())
                    <div class="block xl:hidden">
                        <button @click="showSidebarModal = true" class="text-on-filled focus:outline-none p-2">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                    </div>
                    @endif
                </div>

                <!-- Desktop Search Bar Livewire Component -->
                <div class=" hidden xl:block w-[calc(90%-30rem)]">
                    @if (isset($book))
                    <livewire:search-bar :currentBookId="$book->id" />
                    @else
                    <livewire:search-bar />
                    @endif
                </div>


                <div class="w-auto hidden xl:flex  items-center justify-between mt-4 md:mt-0">
                    <div class=" " style="font-family: 'tajawal';font-weight: 300;">{{ $hijri_date }} هـ</div>
                    <div class="flex items-center">
                        <button id="theme-toggle" class=" pr-8 text-accent focus:outline-none">
                            <span id="theme-icon"><i class="fas fa-moon hidden "></i></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Full Screen Search Modal -->
    <div x-show="showSearchModal" x-cloak class="fixed inset-0 z-50 flex flex-col p-4 md:hidden bg-white"
        @keydown.escape.window="showSearchModal = false">



        <!-- Search Bar Component -->
        @if (isset($book))
        <livewire:search-bar :currentBookId="$book->id" :startOpen="true" />
        @else
        <livewire:search-bar :startOpen="true" />
        @endif

    </div>

</header>

<!-- Mobile Full Screen Sidebar Modal -->
<div x-show="showSidebarModal" x-cloak class="fixed inset-0 bg-base-100 z-50 flex flex-col xl:hidden"
    @keydown.escape.window="showSidebarModal = false">
    <!-- The included aside should now have its own close button logic -->
    @if (isset($book) && $book->hasContent() && isset($titles))
    @include('front_v2.book_read.page-aside-mobile')
    @endif
</div>

@if (isset($book) && $book->hasContent() && isset($titles))
@include('front_v2.book_read.partials.scripts')
@endif

<!-- Mobile Navigation Scripts -->
<script>
    // Removed old sidebar toggle script as it's now handled by Alpine.js
</script>