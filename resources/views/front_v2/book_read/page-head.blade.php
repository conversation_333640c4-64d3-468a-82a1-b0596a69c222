<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="ar" />
    <meta http-equiv="Content-Type" content="text/html" charset="utf-8" />
    <meta name="google-site-verification" content="qwlAL9ujxQRf-igS0mWPndJeqf9wW07A4KlWL_NkjQI" />

    <!-- *TODO handle below properly -->
    @if (isset($book_page) && $book_page)
    <meta property="og:title" content="{{ $book->title }} - Page {{ $pagination['currentPage'] }}">
    <meta property="og:description" content="{{ $book->description }}">
    <meta property="og:type" content="book">
    <meta property="og:url" content="{{ url()->current() }}">


    <title>{{ $book->title }}</title>
    <meta name="description" content="{{ $book->description ?? 'Read ' . $book->title }}">
    <meta name="keywords" content="book, {{ $book->title }}, reading">
    <meta name="author" content="{{ $book->author }}">

    <meta name="og:image" content="{{ asset($book_image) }}" />
    <meta name="facebook:image" content="{{ asset($book_image) }}" />
    <meta name="twitter:image" content="{{ asset($book_image) }}" />
    @else
    <meta name="og:image" content="{{ asset('site/images/logo/share_image.png') }}" />
    <meta name="facebook:image" content="{{ asset('site/images/logo/share_image.png') }}" />
    <meta name="twitter:image" content="{{ asset('site/images/logo/share_image.png') }}" />
    @endif
    <meta property="og:url" content="{{ url()->current() }}">

    <meta name="twitter:card" content="summary_large_image">

    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicons/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicons/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicons/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('favicons/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ asset('favicons/safari-pinned-tab.svg') }}" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    <!-- google fonts -->
    <!-- *TODO handle below properly -->
    @if (isset($book_page) && $book_page)
    <title>{{ $book->title }} - Page {{ $pagination['currentPage'] }}</title>
    @endif

    <!-- CSS -->
    @vite(['resources/front/app.css', 'resources/front/app.js'], 'build/front')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Custom Fonts -->
    <style type="text/css">
        @font-face {
            font-family: 'logo';
            src: url("{{ asset('assets/fonts/motken_k_sina.ttf') }}") format('truetype');
            /* Chrome 4+, Firefox 3.5, Opera 10+, Safari 3—5 */
        }
        @font-face {
            font-family: 'Ziydia';
            src: url("{{ asset('assets/fonts/Ziydia.ttf') }}") format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Ziydia-Q';
            src: url("{{ asset('assets/fonts/Ziydia-Q.ttf') }}") format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Ziydia-Bold';
            src: url("{{ asset('assets/fonts/Ziydia-B.ttf') }}") format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Ziydia-QL';
            src: url("{{ asset('assets/fonts/Ziydia-QL.ttf') }}") format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        .ziydia-font {
            font-family: 'Ziydia', sans-serif;
        }

        .ziydia-q-font {
            font-family: 'Ziydia-Q', sans-serif;
        }

        * {
            font-family: 'Tajawal', sans-serif;
        }

        #sidebarMenu * {
            font-family: 'Ziydia', sans-serif;
        }

        #bookContent *:not(.fas):not(.fab):not(.far):not(.fa) {
            font-family: 'Ziydia', sans-serif;
        }
    </style>


    @livewireStyles

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YTWCYJX0MH"></script>
    @livewireScripts

    <script>
        (function() {
            const theme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', 'ziydia-' + theme);
        })();



        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-YTWCYJX0MH');
    </script>
    <!-- *TODO handle below properly -->
    @if (isset($book_page) && $book_page)
    @include('front_v2.book_read.partials.schema-markup')
    @endif

</head>