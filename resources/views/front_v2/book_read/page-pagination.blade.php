<!-- This is an example component -->
@php
    $prevUrl = $pagination['currentPart'] != null
        ? "/book/{$book->id}/page/{$pagination['previousPage']}/{$pagination['currentPart']}"
        : "/book/{$book->id}/page/{$pagination['previousPage']}";
    $nextUrl = $pagination['currentPart'] != null
        ? "/book/{$book->id}/page/{$pagination['nextPage']}/{$pagination['currentPart']}"
        : "/book/{$book->id}/page/{$pagination['nextPage']}";
@endphp
<div class="mt-8 border-t pt-6 border-gray-200">
    <div class="flex flex-wrap items-center justify-between">
        <!-- Previous Page Button -->
        <div>
            @if ($pagination['previousPage'])
                <a href="{{ $prevUrl }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-circle-chevron-right sm:hidden"></i>
                    <span class="hidden sm:inline">الصفحة السابقة </span>
                </a>
            @else
                <button class="btn btn-sm btn-disabled opacity-50">
                    <i class="fas fa-circle-chevron-right sm:hidden"></i>
                    <span class="hidden sm:inline">الصفحة السابقة</span>
                </button>
            @endif
        </div>

        <!-- Parts and Page Navigation -->
        <div class="flex flex-wrap items-center gap-3 my-3 sm:my-0">
            @if ($pagination['lastPart'] != null && $pagination['lastPart'] > 1)
            <div class="dropdown dropdown-top">
                <div tabindex="0" class="btn btn-sm btn-outline min-w-[100px] justify-between">
                    الجزء: {{ $pagination['currentPart'] }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50" aria-hidden="true">
                        <path d="m7 15 5 5 5-5"></path>
                        <path d="m7 9 5-5 5 5"></path>
                    </svg>
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[100px] max-h-60 overflow-y-auto p-2 mt-1 grid grid-cols-1">
                    @for ($i = 1; $i <= $pagination['lastPart']; $i++)
                        <li>
                        <a href="/book/{{ $book->id }}/p/0/{{ $i }}"
                            class="{{ $i == $pagination['currentPart'] ? 'bg-base-100 text-primary font-medium' : '' }}">
                            الجزء {{ $i }}
                        </a>
                        </li>
                    @endfor
                </ul>
            </div> 
            @endif
            <form action="javascript:void(0)" id="pageForm" class="join">
                <input type="text" name="page" placeholder="{{ $pagination['currentPage'] }}"
                    class="input input-sm w-20 join-item text-center border-neutral"
                    aria-label="رقم الصفحة">
                <button type="submit" class="btn btn-sm btn-outline join-item">
                    <i class="fas fa-arrow-left"></i>
                </button>
            </form>
        </div>

        <!-- Next Page Button -->
        <div>
            @if ($pagination['nextPage'])
                <a href="{{ $nextUrl }}" class="btn btn-sm btn-primary">
                    <span class="hidden sm:inline">الصفحة التالية </span>
                    <i class="fas fa-circle-chevron-left sm:hidden"></i>
                </a>
            @else
                <button class="btn btn-sm btn-disabled opacity-50">
                    <span class="hidden sm:inline">الصفحة التالية </span>
                    <i class="fas fa-circle-chevron-left sm:hidden"></i>
                </button>
            @endif
        </div>
    </div>
</div>