<div class="book-content-container mb-2">
    <div class="card bg-base-100">
        <div class="border-r-4 px-2 py-4 rounded-r-sm">
            <div class="grid grid-cols-[1fr_auto] items-center gap-3">
                <div class="overflow-hidden">
                    <h2 class="book-title" style="font-family: Ziydia-Bold;">
                        {{ $currentTitle }}
                    </h2>
                    <div class="page-info">
                        <span>
                            صفحة {{ $pagination['currentPage'] }}
                            @if ($pagination['currentPart'] != null)
                            - الجزء {{ $pagination['currentPart'] }}
                            @endif
                        </span>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="dropdown dropdown-end">
                        <button tabindex="0" class="btn btn-ghost btn-sm btn-circle text-base-content/70">
                            <i class="fas fa-ellipsis-vertical text-lg"></i>
                        </button>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><button id="increaseFontBtn" class="menu-button"><i class="fas fa-plus"></i>زيادة الخط</button></li>
                            <li><button id="decreaseFontBtn" class="menu-button"><i class="fas fa-minus"></i>تصغير الخط</button></li>
                            <li><button id="increaseLineBtn" class="menu-button"><i class="fas fa-arrows-alt-v"></i>زيادة المسافة</button></li>
                            <li><button id="decreaseLineBtn" class="menu-button"><i class="fas fa-compress-alt"></i>تقليل المسافة</button></li>
                            <li><button id="shareBtn" class="menu-button"><i class="fas fa-share-alt"></i>مشاركة الصفحة</button></li>
                            <li><button id="copyBtn" class="menu-button"><i class="fas fa-copy"></i>نسخ الصفحة</button></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Book Content Section -->
<div id="bookContent" class="book-content text-justify">
    {!! $content !!}
</div>
<div id="pagination-container">
    @include('front_v2.book_read.page-pagination')
</div>

<div id="floating-menu" class="hidden absolute z-50">
    <!-- Main menu container with DaisyUI styling -->
    <div class="card bg-base-100 shadow-lg compact">
        <div class="card-body p-2 flex-row gap-2">
            <button id="copy-selection" class="btn btn-sm btn-ghost normal-case">
                <i class="fas fa-copy mr-1"></i>
                <span>نسخ</span>
            </button>
            <button id="search-selection" class="btn btn-sm btn-ghost normal-case">
                <i class="fas fa-search mr-1"></i>
                <span>بحث</span>
            </button>
        </div>
    </div>
</div>

<script>
</script>