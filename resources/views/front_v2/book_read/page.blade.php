<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light" data-theme="ziydia-light">

@include('front_v2.book_read.page-head')

{{-- Add x-data here to control modals defined in page-nav --}}

<body class="bg-base-100 min-h-screen flex flex-col transition-colors duration-200" x-data="{ showSearchModal: false, showSidebarModal: false }">
    @include('front_v2.book_read.book-info-dialog')

    @include('front_v2.partials.page-nav')

    <!-- error session message -->
    @if (session('error'))
        <div class="alert alert-error mx-auto mt-4 w-full max-w-2xl">
            <div>
                <i class="fa-solid fa-circle-exclamation"></i>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <div
        class="  xl:container px-4 xl:px-0 xl:mx-auto py-2   flex flex-col md:flex-row gap-6 items-center justify-between ">
        <!-- book title -->

        <!-- Desktop/Tablet only -->
        <div class=" hidden xl:block">
            <h1 class="text-2xl font-bold">{{ $book->title }}،
                <a href="/Author/{{ $book->author->id }}" class="text-primary hover:link"
                    style=" font-weight: 300;">{{ $book->author->name }}
                    {{ $book->author->formattedDeathDate2() }}
                </a>

            </h1>
        </div>
        <!-- Mobile only -->
        <div class=" block xl:hidden md:w-[44%]">
            <h1 class="text-2xl font-bold">{{ $book->title }}،</h1>
            <a href="/Author/{{ $book->author->id }}" class="text-primary"
                style=" font-weight: 300;">{{ $book->author->name }}
                {{ $book->author->formattedDeathDate2() }}
            </a>
        </div>
        <!-- tabs -->
        <div class="flex flex-row justify-between w-full md:w-auto items-center ">
            <div class="flex gap-2 items-center ">
                @if ($book->hasContent())
                    <div class="tabs tabs-box ">
                        @if ($book->hasContent())
                            <input type="radio" name="content_tab" class="tab" aria-label="كتاب إلكتروني"
                                checked="checked" />
                        @endif
                        @if ($book->pdfs->count() > 0  && $book->canViewPdf())
                            <input type="radio" name="content_tab" class="tab" aria-label="ملف PDF"
                                @if (!$book->hasContent()) checked="checked" @endif />
                        @endif
                    </div>
                @elseif ($book->pdfs->count() > 1 && $book->canViewPdf())
                    <div class="dropdown dropdown-down">
                        <div tabindex="0"
                            class="btn btn-sm btn-outline w-[120px] py-2 h-9 justify-between items-center ">
                            <span class="text-sm " style="font-weight: 300;">الجزء:
                                {{ $pagination['currentPart'] }}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50"
                                aria-hidden="true">
                                <path d="m7 15 5 5 5-5"></path>
                                <path d="m7 9 5-5 5 5"></path>
                            </svg>
                        </div>
                        <ul tabindex="0"
                            class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box w-[120px] max-h-60 overflow-y-auto p-2 mt-1 grid grid-cols-1">
                            @foreach ($book->pdfs as $index => $pdf)
                                <li>
                                    <a href="/book/{{ $book->id }}/page/1/{{ $index + 1 }}"
                                        class="{{ $index + 1 == $pagination['currentPart'] ? 'bg-base-100 text-primary font-medium' : '' }}">
                                        الجزء {{ $index + 1 }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
            <!-- book info -->
            <div class="">
                <button onclick="book_info_model.showModal()" id="bookInfo"
                    class="mr-4 ring-offset-background placeholder:text-muted-foreground focus:ring-ring  h-9 items-center justify-between rounded-md bg-transparent py-2 text-sm whitespace-nowrap focus:ring-1 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 w-[170px] overflow-hidden px-2 shadow-none transition-colors md:ltr:rounded-l-none md:ltr:border-l-0 border border-[#D5D8DF] dark:border-[#33363C] group-hover:bg-divider hidden sm:flex">
                    <span>معلومات الكتاب</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50"
                        aria-hidden="true">
                        <path d="m7 15 5 5 5-5"></path>
                        <path d="m7 9 5-5 5 5"></path>
                    </svg>
                </button>
                <button onclick="book_info_model.showModal()" id="bookInfo"
                    class="mr-4 ring-offset-background placeholder:text-muted-foreground focus:ring-ring  h-9 items-center justify-between rounded-md bg-transparent py-2 text-sm whitespace-nowrap focus:ring-1 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 px-4 overflow-hidden px-2 shadow-none transition-colors md:ltr:rounded-l-none md:ltr:border-l-0 border border-[#D5D8DF] dark:border-[#33363C] group-hover:bg-divider flex sm:hidden">
                    <i class="fas fa-info-circle"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Main Content -->

    <div id="bookReadContent">
        <div class="flex-grow xl:container mx-auto pb-6 flex flex-col lg:flex-row gap-6">
            <!-- Sidebar Container for Desktop -->
            <div class="hidden xl:block lg:w-1/4 min-w-[300px] max-w-[250px] relative ">
                @include('front_v2.book_read.page-aside')
            </div>
            <!-- vertical divider  -->
            <div class="hidden xl:block w-1 bg-gray-100"></div>
            <!-- Content -->
            <main class="w-full lg:flex-1 bg-base-100  rounded-lg  transition-colors duration-200 px-4 xl:px-0">
                @include('front_v2.book_read.page-content')
            </main>
        </div>
    </div>
    <!-- PDF Content Section -->
    <div id="pdfContent" class="hidden flex-grow container mx-auto px-2 flex-col lg:flex-row gap-6 mb-6">
        <!-- Content -->
        <main class="lg:flex-1 shadow-sm rounded-lg transition-colors duration-200">
            @if (($book->pdfs->count() && $book->canViewPdf()) > 0 && $book->hasContent())
                @php
                    $partIndex = max(0, ($pagination['currentPart'] ?? 1) - 1);
                    $partIndex = min($partIndex, $book->pdfs->count() - 1);
                @endphp
                <iframe id="pdfViewer" width="100%" height="900px"
                    src="https://mozilla.github.io/pdf.js/web/viewer.html?file={{ $book->pdfs[$partIndex]->file_path }}#page={{ $pagination['pPage'] }}"></iframe>
            @endif
        </main>
    </div>


    @if (!$book->hasContent() && $book->pdfs->count() == 1)
        <div id="pdfContent" class=" flex-grow container mx-auto  px-2 flex-col lg:flex-row gap-6 mb-6">
            <main class="lg:flex-1  shadow-sm rounded-lg transition-colors duration-200">
                <iframe id="pdfViewer" width="100%" height="900px"
                    src="https://mozilla.github.io/pdf.js/web/viewer.html?file={{ $book->pdfs[0]->file_path }}"></iframe>
            </main>
        </div>
    @endif

    @if (!$book->hasContent() && ($book->pdfs->count() > 1 && $book->canViewPdf()))
        <div id="pdfContent" class=" flex-grow container mx-auto  px-2 flex-col lg:flex-row gap-6 mb-6">
            @php
                $currentPart = $pagination['currentPart'] ?? 1;
                $pdfPath = $book->pdfs[$currentPart - 1]->file_path;
            @endphp
            <iframe id="pdfViewer" width="100%" height="900px"
                src="https://mozilla.github.io/pdf.js/web/viewer.html?file={{ $pdfPath }}"></iframe>
        </div>
    @endif
    @include('front_v2.partials.footer')
    <!-- JavaScript -->
</body>

</html>
