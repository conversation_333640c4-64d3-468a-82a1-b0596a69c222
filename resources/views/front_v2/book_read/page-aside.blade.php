<!--
    Modern Book Sidebar Navigation Component with Light Theme
    Features:
    - Light and clean design with smooth animations
    - Improved visual hierarchy and spacing
    - Enhanced search experience
    - Better touch targets for mobile/tablet
    - Smooth transitions and hover effects
    - Mobile responsive with toggle functionality (now via modal)
-->

<aside id="sidebarMenu"
    class=" rounded-xl overflow-hidden w-full h-screen lg:max-h-[calc(100vh-10rem)] md:max-h-[calc(100vh-10rem)] sm:max-h-[calc(100vh-1rem)] flex flex-col transition-all duration-300 ease-in-out">
    <div class="h-full flex flex-col px-4"> 
        <!-- Modern Sidebar Header with Close Button for Mobile -->
        <div class="py-2 border-b border-gray-100 ">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-black" style="font-family: tajawal;">محتويات الكتاب</h2>
                <!-- Mobile Close Button (now uses Alpine) -->
                <button @click="showSidebarModal = false" class="text-on-filled focus:outline-none block sm:hidden">
                   <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-gray-600 hover:text-gray-800 transition-colors duration-200"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Enhanced Search Input -->
            <div class="relative group">
                <label class="block relative" >
                    <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        <svg class="w-5 h-5 text-gray-400 group-focus-within:text-primary transition-colors duration-200"
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <g stroke-linejoin="round" stroke-linecap="round" stroke-width="2" fill="none"
                                stroke="currentColor">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </g>
                        </svg>
                    </div>
                    <input type="search" id="sidebarSearch" name="search"
                        class="w-full pl-10 pr-4 py-2.5 text-sm   rounded-lg border border-gray-100 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none transition-all duration-200"
                        placeholder="ابحث في المحتويات..." style="font-family: tajawal;" />
                </label>
            </div>
        </div>

        <!-- Navigation Menu with improved spacing and transitions -->
        <div class="overflow-y-auto flex-grow py-2">
            <ul class="space-y-1">
                @foreach ($titles as $index => $title)
                    @include('front_v2.book_read.partials._sidebar-title', [
                        'title' => $title,
                        'book' => $book,
                        'level' => 1,
                    ])
                @endforeach
            </ul>
        </div>
    </div>
</aside>

<!-- Enhanced Sidebar JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('#sidebarSearch');
        let isResizing = false;
        let lastX = 0;

        // Function to check if parent elements match the search query
        function parentHasMatch(element, query) {
            // Check parent li elements for matches
            let parent = element.closest('li').parentElement;
            while (parent) {
                const parentLi = parent.closest('li');
                if (!parentLi) break;
                
                const parentLink = parentLi.querySelector('a');
                if (parentLink && parentLink.textContent.toLowerCase().includes(query)) {
                    return true;
                }
                parent = parentLi.parentElement;
            }
            return false;
        }

        // Function to highlight matching text in search results
        function highlightText(element, query) {
            if (!query) {
                // Restore original text if no query
                if (element.dataset.originalText) {
                    element.textContent = element.dataset.originalText;
                    delete element.dataset.originalText;
                }
                return;
            }
            
            // Store original text if not already saved
            if (!element.dataset.originalText) {
                element.dataset.originalText = element.textContent;
            }
            
            const text = element.dataset.originalText;
            const lowerText = text.toLowerCase();
            const index = lowerText.indexOf(query.toLowerCase());
            
            if (index >= 0) {
                const before = text.substring(0, index);
                const match = text.substring(index, index + query.length);
                const after = text.substring(index + query.length);
                
                element.innerHTML = before + '<span class="bg-yellow-100 text-primary font-medium">' + match + '</span>' + after;
            }
        }

        // Enhanced search functionality with debouncing
        if (searchInput) {
            let searchTimeout;
            const searchDelay = 150; // Debounce delay in ms
            
            searchInput.addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    const searchQuery = e.target.value.toLowerCase();
                    const allTitles = document.querySelectorAll('#sidebarMenu li a');
                    const allDetails = document.querySelectorAll('#sidebarMenu details');
                    const allListItems = document.querySelectorAll('#sidebarMenu li');
                    
                    // Reset all items before filtering
                    allListItems.forEach(item => item.style.display = '');
                    
                    if (searchQuery) {
                        allTitles.forEach(function(titleElement) {
                            const title = titleElement.textContent.toLowerCase();
                            const listItem = titleElement.closest('li');
                            const parentDetails = titleElement.closest('details');
                            const hasMatch = title.includes(searchQuery);
                            
                            // Highlight matching text
                            highlightText(titleElement, hasMatch ? searchQuery : '');
                            
                            if (hasMatch) {
                                // Show this item
                                listItem.style.display = '';
                                
                                // Open parent details elements
                                let currentDetails = parentDetails;
                                while (currentDetails) {
                                    currentDetails.setAttribute('open', '');
                                    currentDetails = currentDetails.parentElement.closest('details');
                                }
                            } else {
                                // Hide if neither this item nor its children match
                                const childrenMatch = listItem.querySelector('details li a')?.textContent.toLowerCase().includes(searchQuery);
                                
                                if (!childrenMatch && !parentHasMatch(titleElement, searchQuery)) {
                                    listItem.style.display = 'none';
                                }
                            }
                        });
                    } else {
                        // Reset highlights when search is cleared
                        allTitles.forEach(titleElement => highlightText(titleElement, ''));
                        
                        // Collapse details if search is empty, except active ones
                        allDetails.forEach(function(details) {
                            if (!details.querySelector('.text-primary')) {
                                details.removeAttribute('open');
                            }
                        });
                    }
                }, searchDelay);
            });
        }
    });
</script>