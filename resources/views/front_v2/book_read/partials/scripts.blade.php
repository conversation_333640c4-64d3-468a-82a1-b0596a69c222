<script>
    // Immediate theme initialization to prevent flash
    (function() {
        const theme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', 'ziydia-' + theme);
    })();

    document.addEventListener("DOMContentLoaded", function() {
        initThemeToggle();
        initSidebar();
        document.getElementById('pageForm')?.addEventListener('submit', function(e) {
            e.preventDefault();
            const page = this.page.value;
            const url = `/book/{{ $book->id }}/page/${page}`;
            window.location.href = url;
        });

        const shareBtn = document.getElementById('shareBtn');
        const copyBtn = document.getElementById('copyBtn');
        const bookContent = document.getElementById('bookContent');
        
        
        if (shareBtn) {
            shareBtn.onclick = async () => {
                try {
                    if (navigator.share) {
                        await navigator.share({
                            title: `كتاب {{ $book->title }} - صفحة {{ $pagination['currentPage'] }} - المكتبة الزيدية`,
                            url: window.location.href
                        });
                    } else {
                        // Fallback for browsers that don't support navigator.share
                        navigator.clipboard.writeText(window.location.href).then(() => {
                            showToast('تم نسخ رابط الصفحة بنجاح');
                        });
                    }
                } catch (error) {
                    console.error('Error sharing content: ', error);
                }
            };
        }

        if (copyBtn) {
            copyBtn.onclick = async () => {
                try {
                    navigator.clipboard.writeText(bookContent.innerText).then(() => {
                        showToast('تم نسخ رابط الصفحة بنجاح');
                    });
                } catch (error) {
                    console.error('Error copying content: ', error);
                }
            };
        }
    });

    function initThemeToggle() {
        const html = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');
        const themeToggleMobile = document.getElementById('theme-toggle-mobile');
        const currentTheme = localStorage.getItem('theme') || 'light';        
        applyTheme(currentTheme);
        updateContentColors(currentTheme);

        themeToggle?.addEventListener('click', function() {
            const newTheme = html.getAttribute('data-theme') === 'ziydia-light' ? 'dark' : 'light';
            applyTheme(newTheme);
            updateContentColors(newTheme);
            localStorage.theme = newTheme;
        });
        themeToggleMobile?.addEventListener('click', function() {
            alert('asd');
            const newTheme = html.getAttribute('data-theme') === 'ziydia-light' ? 'dark' : 'light';
            applyTheme(newTheme);
            updateContentColors(newTheme);
            localStorage.theme = newTheme;
        });
    }

    function applyTheme(theme) {
        const html = document.documentElement;
        document.documentElement.setAttribute('data-theme', 'ziydia-' + theme);
    }


    function updateContentColors(theme) {
        const content = document.getElementById('bookContent');


        if (content) {
            if (theme === 'dark') {
            content.innerHTML = content.innerHTML.split('#00AA00').join('#00D100');
            content.innerHTML = content.innerHTML.split('#464646').join('#B3B3B3');
            content.innerHTML = content.innerHTML.split('#0000fa').join('#ADADFF');
            content.innerHTML = content.innerHTML.split('#800000').join('#FF9999');
            content.innerHTML = content.innerHTML.split('#be0000').join('#DCA7A7');
            content.innerHTML = content.innerHTML.split('#000000').join('#B3B3B3');
            content.innerHTML = content.innerHTML.split('#0000D6').join('#ADADFF');
            content.innerHTML = content.innerHTML.split('rgba(0, 0, 0, 0.2)').join('rgba(255, 255, 255, 0.2)');
            } else {
            content.innerHTML = content.innerHTML.split('#00D100').join('#00AA00');
            content.innerHTML = content.innerHTML.split('#B3B3B3').join('#464646');
            content.innerHTML = content.innerHTML.split('#ADADFF').join('#0000fa');
            content.innerHTML = content.innerHTML.split('#FF9999').join('#800000');
            content.innerHTML = content.innerHTML.split('#DCA7A7').join('#be0000');
            content.innerHTML = content.innerHTML.split('#B3B3B3').join('#000000');
            content.innerHTML = content.innerHTML.split('#ADADFF').join('#0000D6');
            }
        }
    }

    function initSidebar() {
        const closeSidebarBtn = document.getElementById('closeSidebarBtn');
        const sidebarMenu = document.getElementById('sidebarMenu');
        
        if (closeSidebarBtn && sidebarMenu) {
            closeSidebarBtn.addEventListener('click', function() {
                sidebarMenu.classList.add('hidden');
                sidebarMenu.classList.remove('fixed', 'inset-0', 'z-40');
            });
        }
    }

    // Show toast notification
    function showToast(message) {
        // Create toast element if it doesn't exist
        let toast = document.getElementById('notification-toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'notification-toast';
            toast.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50 opacity-0 transition-opacity duration-300';
            document.body.appendChild(toast);
        }
        
        // Set message and show toast
        toast.textContent = message;
        toast.classList.remove('opacity-0');
        toast.classList.add('opacity-100');
        
        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            toast.classList.add('opacity-0');
        }, 3000);
    }


    document.addEventListener('DOMContentLoaded', function() {
        const floatingMenu = document.getElementById('floating-menu');
        const bookContent = document.getElementById('bookContent');
        const paginationContainer = document.getElementById('pagination-container');
        const pdfContent = document.getElementById('pdfContent');
        const contentTabs = document.querySelectorAll('input[name="content_tab"]');
        // Font size and line height controls
        let fontSize = localStorage.getItem('bookFontSize') ? parseInt(localStorage.getItem('bookFontSize')) : 18;
        let lineHeight = localStorage.getItem('bookLineHeight') ? parseFloat(localStorage.getItem('bookLineHeight')) : 1.8;

        // Apply initial text preferences
        if (bookContent) {
            applyTextPreferences(bookContent, fontSize, lineHeight);
        }

        // Font size controls
        const increaseFontBtn = document.getElementById('increaseFontBtn');
        const decreaseFontBtn = document.getElementById('decreaseFontBtn');
        
        if (increaseFontBtn) {
            increaseFontBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                fontSize = Math.min(32, fontSize + 1);
                applyTextPreferences(bookContent, fontSize, lineHeight);
                localStorage.setItem('bookFontSize', fontSize);
            });
        }
        
        if (decreaseFontBtn) {
            decreaseFontBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                fontSize = Math.max(14, fontSize - 1);
                applyTextPreferences(bookContent, fontSize, lineHeight);
                localStorage.setItem('bookFontSize', fontSize);
            });
        }

        // Line height controls
        const increaseLineBtn = document.getElementById('increaseLineBtn');
        const decreaseLineBtn = document.getElementById('decreaseLineBtn');
        
        if (increaseLineBtn) {
            increaseLineBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                lineHeight = Math.min(3, lineHeight + 0.1);
                applyTextPreferences(bookContent, fontSize, lineHeight);
                localStorage.setItem('bookLineHeight', lineHeight);
            });
        }
        
        if (decreaseLineBtn) {
            decreaseLineBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                lineHeight = Math.max(1.2, lineHeight - 0.1);
                applyTextPreferences(bookContent, fontSize, lineHeight);
                localStorage.setItem('bookLineHeight', lineHeight);
            });
        }


        function applyTextPreferences(element, fontSize, lineHeight) {
            if (element) {
                element.style.fontSize = fontSize + 'px';
                element.style.lineHeight = lineHeight;
            }
        }

        // Initial visibility based on hasContent
        @if(!$book -> hasContent())
        bookReadContent.classList.add('hidden');
        paginationContainer.classList.add('hidden');
        pdfContent.classList.remove('hidden');
        @endif

        // Restore tab state from localStorage
        const savedTab = localStorage.getItem('selectedTab');
        if (savedTab) {
            contentTabs.forEach(tab => {
                if (tab.getAttribute('aria-label') === savedTab) {
                    tab.checked = true;
                    // Apply initial visibility based on saved state
                    if (savedTab === 'ملف PDF') {
                        bookReadContent.classList.add('hidden');
                        paginationContainer.classList.add('hidden');
                        pdfContent.classList.remove('hidden');
                    }
                }
            });
        }

        // Handle tab switching
        contentTabs.forEach(tab => {
            tab.addEventListener('change', function() {
                const tabLabel = this.getAttribute('aria-label');
                // Store selected tab in localStorage
                localStorage.setItem('selectedTab', tabLabel);

                if (tabLabel === 'ملف PDF') {
                    bookReadContent.classList.add('hidden');
                    paginationContainer.classList.add('hidden');
                    pdfContent.classList.remove('hidden');
                } else {
                    bookReadContent.classList.remove('hidden');
                    pdfContent.classList.add('hidden');
                    paginationContainer.classList.remove('hidden');
                }
            });
        });

        // Show menu on text selection for both mouse and touch events
        let selectionTimeout;
        
        function handleSelection(e) {
            clearTimeout(selectionTimeout);
            selectionTimeout = setTimeout(() => {
                const selectedText = window.getSelection().toString().trim();
                const selection = window.getSelection();

                if (selectedText && selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const container = range.commonAncestorContainer;

                    if (bookReadContent.contains(container) || bookReadContent === container) {
                        const rect = range.getBoundingClientRect();
                        const menuX = e.type.includes('touch') ? 
                            e.changedTouches[0].pageX : 
                            rect.left + window.scrollX;
                        const menuY = e.type.includes('touch') ? 
                            e.changedTouches[0].pageY - 40 : 
                            rect.top + window.scrollY - 40;
                        showFloatingMenu(menuX, menuY);
                    }
                }
            }, 200); // Small delay to ensure selection is complete
        }

        // Mouse events
        document.addEventListener('mouseup', handleSelection);
        
        // Touch events
        document.addEventListener('touchend', handleSelection);

        // Copy selected text
        document.getElementById('copy-selection').addEventListener('click', function() {
            const selection = window.getSelection().toString();
            navigator.clipboard.writeText(selection).then(() => {
                showToast('تم نسخ النص المحدد');
                floatingMenu.classList.add('hidden');
            });
        });

        // Search selected text
        document.getElementById('search-selection').addEventListener('click', function() {
            const selection = window.getSelection().toString();
            window.location.href = `/book_search?q=${encodeURIComponent(selection)}`;
        });

        // Hide menu when clicking/touching outside
        function handleOutsideClick(e) {
            if (!floatingMenu.contains(e.target)) {
                hideFloatingMenu();
            }
        }

        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('touchstart', handleOutsideClick);

        function showFloatingMenu(x, y) {
            // Ensure menu stays within viewport bounds
            const menuWidth = floatingMenu.offsetWidth;
            const menuHeight = floatingMenu.offsetHeight;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Adjust X position
            x = Math.min(x, viewportWidth - menuWidth - 10);
            x = Math.max(x, 10);

            // Adjust Y position
            y = Math.min(y, viewportHeight - menuHeight - 10);
            y = Math.max(y, 10);

            floatingMenu.style.left = `${x}px`;
            floatingMenu.style.top = `${y}px`;
            floatingMenu.classList.remove('hidden');
        }

        function hideFloatingMenu() {
            floatingMenu.classList.add('hidden');
        }

        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'toast toast-top toast-center';
            toast.innerHTML = `
                <div class="alert alert-success">
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    });
</script>
