
<script>
        // Theme Toggle
        document.addEventListener('DOMContentLoaded', function() {
            initThemeToggle();
        });

        function initThemeToggle() {
            const html = document.documentElement;
            const themeToggle = document.getElementById('theme-toggle');
            const themeToggleMobile = document.getElementById('theme-toggle-mobile');
            const currentTheme = localStorage.getItem('theme') || 'light';
            applyTheme(currentTheme);
            updateThemeIcon(currentTheme);

            themeToggle?.addEventListener('click', function() {
                const newTheme = html.getAttribute('data-theme') === 'ziydia-light' ? 'dark' : 'light';
                applyTheme(newTheme);
                updateThemeIcon(newTheme);
                localStorage.theme = newTheme;
            });
            themeToggleMobile?.addEventListener('click', function() {
                const newTheme = html.getAttribute('data-theme') === 'ziydia-light' ? 'dark' : 'light';
                applyTheme(newTheme);
                updateThemeIcon(newTheme);
                localStorage.theme = newTheme;
            });
        }

        function applyTheme(theme) {
            const html = document.documentElement;
            // Set data-theme attribute for DaisyUI themes
            document.documentElement.setAttribute('data-theme', 'ziydia-' + theme);

            // Add/remove dark class for Tailwind dark mode
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }

            // Force a CSS repaint to ensure styles are applied immediately
            document.body.style.display = 'none';
            document.body.offsetHeight; // Trigger a reflow
            document.body.style.display = '';
        }

        function updateThemeIcon(theme) {

            const themeIcon = document.getElementById('theme-icon');
            
            if (theme === 'dark') {
                themeIcon.innerHTML = '<i class="fas fa-sun  w-6 text-icons"></i>';
            } else {
                themeIcon.innerHTML = '<i class="fas fa-moon  w-6 text-icons"></i>';
            }
            const themeIcon2 = document.getElementById('theme-icon-mobile');
            if(themeIcon2){
                themeIcon2.innerHTML = '<i class="fas fa-sun  w-6 text-icons"></i>';
                themeIcon2.innerHTML = '<i class="fas fa-moon  w-6 text-icons"></i>';
                
            }
        }
    </script>

    @yield('page-script')