<!DOCTYPE html>
<html lang="ar" dir="rtl" class="dark" data-theme="ziydia-light">


@include('front_v2.layouts.header')

@if(!request()->is('/') && !request()->is('admin/v2') && !request()->is('/search') && !request()->is('admin/v2/search') )

<body class="bg-base-100 min-h-screen flex flex-col transition-colors duration-200" x-data="{ showSearchModal: false, showSidebarModal: false } ">



    @include('front_v2.layouts.navbar')
    @else

    <body class="bg-base-100 min-h-screen flex flex-col transition-colors duration-200">
        @include('front_v2.partials.home-navbar')
        @endif

        <!-- error session message -->
        @if (session('error'))
        <div class="alert alert-error mx-auto  m-4 w-full max-w-1/2">
            <div>
                <i class="fa-solid fa-circle-exclamation"></i>
                <span>{{ session('error') }}</span>
            </div>
        </div>
        @endif
        <!-- error session message -->
        @if (session('success'))
        <div class="alert alert-success mx-auto  m-4 w-full max-w-1/2">
            <div>
                <i class="fa-solid fa-circle-exclamation"></i>
                <span>{{ session('success') }}</span>
            </div>
        </div>
        @endif
        @yield('content')

        <!-- Book Info Dialog Component -->
        @livewire('book-info-dialog')
    </body>

    @include('front_v2.layouts.footer')

    @include('front_v2.layouts.scripts')

</html>