@extends('front_v2.layouts.layout')
@section('title', 'الرئيسية')

@section('page-meta')
@endsection

@section('page-style')
@endsection


@section('content')

<!-- Main Content -->
<main class="flex-grow  ">
    <!-- Hero Section -->
    @include('front_v2.partials.hero')
    <div class="flex-grow container mx-auto px-4">
        <!-- Categories Section -->
        @include('front_v2.partials.categories')

        <!-- الكتب المختارة -->
        @if ($selectedBooks->count() > 0)
        <x-book-slider
            :title="'كتب مختارة'"
            :books="$selectedBooks"
            sectionId="selected"
            :emptyMessage="'لا توجد كتب لعرضها حالياً'" />
        @endif


        <!-- احدث الكتب -->
        <x-book-slider
            :title="'أحدث الكتب'"
            :books="$latestBooks"
            sectionId="latest"
            :emptyMessage="'لا توجد كتب لعرضها حالياً'" />

        <!-- الكتب الاكثر تحميلا -->
        @if ($mostDownloadedBooksThisMonth->count() > 0)
        <x-book-slider
            :title="'الكتب الأكثر تحميلا'"
            :books="$mostDownloadedBooksThisMonth"
            sectionId="downloaded"
            :emptyMessage="'لا توجد كتب لعرضها حالياً'" />
        @endif

        <!--  الكتب الاكثر قرائتا-->
        @if ($mostReadedBooksThisMonth->count() > 0)
        <x-book-slider
            :title="'الكتب الأكثر قراءة'"
            :books="$mostReadedBooksThisMonth"
            sectionId="readed"
            :emptyMessage="'لا توجد كتب لعرضها حالياً'" />
        @endif


        <!-- App Links Section -->
        @include('front_v2.partials.app-links')

    </div>
</main>

@endsection

@section('page-script')
@endsection
