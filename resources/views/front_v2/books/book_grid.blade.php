@if($books->count() > 0)
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 ">
        @foreach($books as $book)
            <div class="flex justify-start">
                @livewire('book-card', ['book' => $book], key('book-' . $book->id . '-' . time()))
            </div>
        @endforeach
    </div>
    
    <!-- Pagination -->
    <div class="mt-8 flex justify-center" dir="rtl">
        <div class="join gap-1">
            @if($books->onFirstPage())
            @else
                <a href="{{ $books->previousPageUrl() }}" class="join-item btn btn-ghost ">‹ السابق</a>
            @endif
            
            @php
                $start = max($books->currentPage() - 2, 1);
                $end = min($start + 2, $books->lastPage());
                if ($end - $start < 2) {
                    $start = max($end - 2, 1);
                }
                
                function toArabicNumbers($number) {
                    $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                    return str_replace($western, $arabic, $number);
                }
            @endphp

            @if($start > 1)
                <a href="{{ $books->url(1) }}" class="join-item btn btn-ghost  {{ $books->currentPage() == 1 ? 'btn-active' : '' }}">{{ toArabicNumbers('1') }}</a>
                @if($start > 2)
                    <button class="join-item btn btn-ghost  btn-disabled">...</button>
                @endif
            @endif

            @for($i = $start; $i <= $end; $i++)
                <a href="{{ $books->url($i) }}" class="join-item btn btn-ghost  {{ $books->currentPage() == $i ? 'border border-base-300' : '' }}">{{ toArabicNumbers($i) }}</a>
            @endfor

            @if($end < $books->lastPage())
                @if($end < $books->lastPage() - 1)
                    <button class="join-item btn btn-ghost  btn-disabled">...</button>
                @endif
                <a href="{{ $books->url($books->lastPage()) }}" class="join-item btn btn-ghost rounded-sm  {{ $books->currentPage() == $books->lastPage() ? 'btn-active' : '' }}">{{ toArabicNumbers($books->lastPage()) }}</a>
            @endif
            
            @if($books->hasMorePages())
                <a href="{{ $books->nextPageUrl() }}" class="join-item btn btn-ghost ">التالي ›</a>
            @else
            @endif
        </div>
    </div>
@else
    <div class="flex flex-col items-center justify-center py-12 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-base-content/30 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
        <h3 class="text-xl font-semibold mb-2">لا توجد كتب</h3>
        <p class="text-base-content/70">لم يتم العثور على كتب تطابق معايير البحث الخاصة بك</p>
    </div>
@endif 