@php
    $url = '#';
    if ($result['type'] === 'كتاب') {
        $url = route('front.book.show', $result['id']);
    } elseif ($result['type'] === 'مؤلف') {
        $url = route('front.authors.show', $result['id']);
    } elseif ($result['type'] === 'تصنيف') {
        $url = route('front.categories.show', $result['id']);
    } elseif ($result['type'] === 'نص') {
        $url = route('front.book.page', ['id' => $result['id'], 'page' => $result['page'], 'part' => $result['part']]);
    }
@endphp
@if ($result['type'] === 'النصوص' || $result['type'] === 'نص')
        <div class="flex items-center justify-between p-4 w-full shadow-sm hover:bg-base-300 mb-3">
            <!-- Title + Description -->
            <div class="flex flex-col flex-grow pl-4">
                <div class="text-start font-semibold text-base-content">
                    {{ $result['title'] }}
                </div>
                @if (isset($result['subtitle']))
                    <div class="text-start text-icons line-clamp-2 text-sm pb-2">
                        {!! $result['subtitle'] !!}
                    </div>
                @endif
                @if (isset($result['desc']))
                    <div class="text-start text-sm text-onsearch ">
                        <!-- circle -->
                        <i class="fas fa-circle text-[8px]"></i> <b>الصفحة {{ $result['page'] }} @if (isset($result['part']))من الجزء{{ $result['part'] }}@endif:</b>
                        {!! $result['desc'] !!}
                        <span class="underline font-bold cursor-pointer hover:text-primary-600"
                            wire:click="expandBookContent('{{ $result['id'] }}', {{ $result['page'] }}, {{ $result['part'] ?? 'null' }})">قراءة المزيد</span>
                    </div>
                @endif
            </div>
        </div>
@else
    <a href="{{ $url }}"
        class="block col-span-full hover:bg-base-300 transition-colors rounded-md mb-3 mx-3 shadow-sm">
        <div class="flex items-center justify-between p-4 w-full">
            <!-- Title + Description -->
            <div class="flex flex-col flex-grow pl-4">
                <div class="text-start font-semibold text-gray-800">
                    {{ $result['title'] }}
                </div>
                @if (isset($result['subtitle']))
                    <div class="text-start text-gray-500 line-clamp-2 text-sm pb-2">
                        {!! $result['subtitle'] !!}
                    </div>
                @endif
                @if (isset($result['desc']))
                    <div class="text-start text-sm text-gray-500 ">
                        <!-- circle -->
                         @if(isset( $result['page']))
                        <i class="fas fa-circle text-[8px]"></i> <b>الصفحة {{ $result['page'] }} @if (isset($result['part']))
                                من المجلد {{ $result['part'] }}
                            @endif:</b>
                        @endif
                        {!! $result['desc'] !!}
                    </div>
                @endif
            </div>
        </div>
    </a>
@endif
