<div>
    <div class="search-bar-wrapper" x-cloak>
        <div class="flex-grow max-w-3xl w-full mx-auto" x-data="{
            open: @entangle('showOptions'),
            showResults: @entangle('showResults'),
            isMobile: false,
            checkMobile() {
                this.isMobile = window.innerWidth < 768;
                $wire.set('isMobileView', this.isMobile);
            },
            init() {
                // Ensure mobile modal is closed on initialization
                $wire.set('showMobileModal', false);
        
                this.checkMobile();
                window.addEventListener('resize', this.checkMobile.bind(this));
        
                // Handle focus-search event
                document.addEventListener('focus-search', () => {
                    if (this.isMobile && !$wire.showMobileModal) {
                        $wire.call('toggleMobileSearch');
                    } else if (!$wire.showMobileModal) {
                        document.getElementById('search-input')?.focus();
                    }
                });
            }
        }"
            @click.away="if (!isMobile) { open = false; showResults = false; }">

            <!-- For non-mobile view -->
            <!-- Mobile Search Trigger Button -->
            @if ($screen != 'home')
                <div x-show="isMobile" class="w-full">
                    <div @click="$wire.toggleMobileSearch()" class="w-full cursor-pointer">
                        <div class=" pr-1 flex items-center">
                            <i class="fas fa-search text-icons"></i>
                        </div>
                    </div>
                </div>
            @else
                <div x-show="isMobile" @click="$wire.toggleMobileSearch()" class="w-full cursor-pointer">
                    <div class=" pr-1 flex items-center">
                        <div @click="$wire.toggleMobileSearch()" class="w-full cursor-pointer">
                            <div class="relative w-full">
                                <input type="text" placeholder="ابحث عن نص..."
                                    class="w-full pl-4 pr-10 py-3 rounded-lg bg-base-100 focus:outline-none transition-all shadow-sm cursor-pointer"
                                    readonly>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="fas fa-search text-icons"></i>
                                </div>
                                <div class="absolute inset-y-0 left-2 pl-3 flex items-center">
                                <a href="{{ route('front.advanced-search') }}" target="_blank">
                                    <div class=" text-primary">بحث متقدم </div>
                                </a>
                            </div>
                            </div>
                           
                        </div>
                    </div>
                </div>
            @endif

            <!-- Desktop Search -->
            <div x-show="!isMobile" class="w-full  hidden sm:block">
                <form wire:submit="search" class="w-full">
                    <div class="relative w-[{{ $searchBarWidth }}] md:m-auto">
                        <div class="flex">
                            <input type="text" wire:model.live.debounce.500ms="query"
                                placeholder="ابحث عن نص ... (⌘ + K)"
                                class=" pl-4 pr-10 py-3 rounded-lg bg-base-100  focus:outline-none transition-all shadow-sm  @if ($screen == 'home') h-[56px] @else h-[40px] @endif w-[736px]"
                                autocomplete="off" id="search-input" @focus="open = true; showResults = false;">
                            <button type="submit" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="$wire.isSearching">
                                    <div
                                        class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                                    </div>
                                </template>
                                <template x-if="!$wire.isSearching">
                                    <i class="fas fa-search text-icons w-6 "></i>
                                </template>
                            </button>

                            <div class="absolute inset-y-0 left-2 pl-3 flex items-center">
                                <a href="{{ route('front.advanced-search') }}" target="_blank">
                                    <div class=" text-primary">بحث متقدم </div>
                                </a>
                            </div>
                        </div>

                        <!-- Combined dropdown for contextual options and results -->
                        <div x-show=" (open || showResults)" x-cloak
                            class="absolute left-0 right-0 -mt-2 bg-base-100 shadow-lg rounded-bl-lg rounded-br-lg  z-50  overflow-hidden">



                            <!-- Contextual Options (shown when not searching and no results) -->
                            <div x-show=" open && !showResults && !$wire.isSearching" class="px-4">
                                <!-- Recent Searches Section -->
                                @if (count($recentSearches) > 0 || count($popularSearches) > 0 || count($popularBooks) > 0)
                                    <div class="pt-4"></div>
                                @endif
                                @if (count($recentSearches) > 0)
                                    <div class="">
                                        <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                                            عمليات البحث الأخيرة
                                        </h3>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach ($recentSearches as $term)
                                                <div wire:click="selectRecentSearch('{{ $term }}')"
                                                    class="px-3 py-1 bg-base-200 hover:bg-base-300 rounded-sm cursor-pointer text-sm transition-colors">
                                                    <i class="fas fa-history text-xs text-icons ml-1"></i>
                                                    {{ $term }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="divider my-3"></div>
                                @endif

                                <!-- Popular Searches Section -->
                                @if (count($popularSearches) > 0)
                                    <div>
                                        <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                                            عمليات البحث الشائعة</h3>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach ($popularSearches as $search)
                                                <div wire:click="selectPopularSearch('{{ $search->search_term }}')"
                                                    class="px-3 py-1 bg-base-200 hover:bg-base-300 rounded-sm cursor-pointer text-sm transition-colors">
                                                    <i class="fas fa-arrow-trend-up text-xs  ml-1 text-icons"></i>
                                                    {{ $search->search_term }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="divider my-3"></div>
                                @endif

                                <!-- Popular Searches Section -->
                                @if (count($popularBooks) > 0)
                                    <div>
                                        <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                                            الكتب الشائعة
                                        </h3>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach ($popularBooks as $book)
                                                @livewire('book-card', ['book' => $book, 'size' => 'small'], key('search-book-' . $book->id . '-' . time()))
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="p-3"></div>
                                @endif

                            </div>

                            <!-- Search Results -->
                            <div x-show=" (showResults && !$wire.isSearching)" id="autocomplete-results"
                                class="p-1 max-h-[75vh] overflow-y-auto md:max-h-96 {{-- Mobile: larger max-height, Desktop: fixed max-height --}}">
                                <div class="justify-between items-center mb-4 flex  col-span-full mx-3  ">
                                    <div role="tablist" class="tabs tabs-box p-1 ">
                                        @if ($currentBookId)
                                            <a role="tab" wire:click="selectSearchType('all_content')"
                                                class="tab flex-1 cursor-pointer {{ $searchType == 'all_content' ? 'tab-active' : '' }} p-3">النصوص</a>
                                            <a role="tab" wire:click="selectSearchType('current_book')"
                                                class="tab flex-1 cursor-pointer {{ $searchType == 'current_book' ? 'tab-active' : '' }} p-3">الكتاب
                                                الحالي</a>
                                        @endif
                                    </div>
                                    @if ($resultsCount)
                                        <a href="{{ url('/advanced_search?q=' . urlencode($query) . '&search_type=' . $searchType) }}"
                                            class="text-primary text-sm hover:underline flex-grow text-end">عرض الكل
                                            ({{ $resultsCount }})</a>
                                    @endif
                                </div>

                                <!-- Loading Shimmer (moved below tabs) -->
                                <div x-show="$wire.isSearching" class="w-full p-4 col-span-full">
                                    <div class="animate-pulse space-y-4">
                                        <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                                        <div class="flex flex-wrap gap-2">
                                            <div class="h-8 bg-gray-200 rounded-full w-24"></div>
                                            <div class="h-8 bg-gray-200 rounded-full w-32"></div>
                                            <div class="h-8 bg-gray-200 rounded-full w-20"></div>
                                        </div>
                                        <div class="h-4 bg-gray-200 rounded w-1/3 mt-4"></div>
                                        <div class="grid grid-cols-2 gap-3">
                                            <div class="h-24 bg-gray-200 rounded"></div>
                                            <div class="h-24 bg-gray-200 rounded"></div>
                                            <div class="h-24 bg-gray-200 rounded"></div>
                                            <div class="h-24 bg-gray-200 rounded"></div>
                                        </div>
                                    </div>
                                </div>


                                @foreach ($autoCompleteResults as $result)
                                    @include('livewire.search-result-item', ['query' => $query])
                                @endforeach
                            </div>
                            @if (!empty($query) && strlen($query) > 2 && count($autoCompleteResults) === 0)
                                <div x-init="open = false" id="autocomplete-results"
                                    class="absolute left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                    <div class="p-4 text-center text-gray-600">
                                        <i class="fas fa-search-minus text-icons text-2xl mb-2"></i>
                                        <p>لا توجد نتائج للبحث عن "{{ $query }}"</p>
                                        <p class="text-sm text-icons mt-1">حاول استخدام كلمات مفتاحية أخرى</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Mobile Search Modal -->
        @include('livewire.search-bar-mobile')
    </div>

    <!-- Book Page Modal -->
    @livewire('book-page-modal')

    <style>
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }

        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Prevent flash of mobile modal on page load */
        [x-cloak] {
            display: none !important;
        }


        /* Only show when Alpine.js has loaded and x-show is true */
        [x-show="true"].fixed.inset-0.z-50.bg-white {
            display: block;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle keyboard shortcut for search focus (Command+K or Ctrl+K)
            document.addEventListener('keydown', function(event) {
                if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'k') {
                    event.preventDefault();

                    // Try to get the Livewire component and call focusSearch
                    if (window.Livewire) {
                        const searchComponent = window.Livewire.getByName('search-bar');
                        if (searchComponent) {
                            searchComponent.call('focusSearch');
                            return;
                        }
                    }

                    // Dispatch a custom event as another approach
                    document.dispatchEvent(new CustomEvent('focus-search'));

                    // Focus the search input directly as a fallback
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            });

            // Close autocomplete when clicking outside
            document.addEventListener('click', function(event) {
                const autocompleteResults = document.getElementById('autocomplete-results');
                const searchInput = document.querySelector(
                    'input[wire\\:model\\.live\\.debounce\\.500ms="query"]');
                const searchContainer = document.querySelector('.search-container');

                if (autocompleteResults &&
                    !autocompleteResults.contains(event.target) &&
                    !searchInput?.contains(event.target)) {
                    if (window.Livewire) {
                        const searchComponent = window.Livewire.find('search-bar');
                        if (searchComponent) {
                            searchComponent.set('showResults', false, false);
                            searchComponent.set('showOptions', false, false);
                        }
                    }
                }
            });
        });
    </script>

</div>
