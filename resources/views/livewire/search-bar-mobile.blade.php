<div x-show="$wire.showMobileModal" x-cloak x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 translate-y-4" x-transition:enter-end="opacity-100 translate-y-0"
    x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0"
    x-transition:leave-end="opacity-0 translate-y-4"
    class="fixed inset-0 z-50 bg-secondary-content overflow-y-auto pt-4 px-4" @click.away="$wire.closeMobileModal()"
    style="display: none;">



    <!-- Search Input -->
    <div class="flex justify-start items-center">

        <button @click="$wire.closeMobileModal()" class="text-natural p-3">
            <i class="fas fa-arrow-right text-2xl"></i>
        </button>
        <p class="text-2xl font-bold">بحث</p>
    </div>

    <div class="relative w-full mb-4">
        <input type="text" wire:model.live.debounce.500ms="query" placeholder="ابحث عن نص..."
            class="w-full pl-4 pr-10 py-3 rounded-lg bg-secondary-content focus:outline-none focus:ring-2 focus:ring-primary transition-all"
            autocomplete="off" autofocus>
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <template x-if="$wire.isSearching">
                <div class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                </div>
            </template>
            <template x-if="!$wire.isSearching">
                <i class="fas fa-search text-gray-500"></i>
            </template>
        </div>
    </div>



    <!-- Contextual Options (shown when not searching and no results) -->
    <div x-show=" open && !$wire.showResults && !$wire.isSearching" class="px-4">
        <!-- Recent Searches Section -->
        @if (count($recentSearches) > 0 || count($popularSearches) > 0 || count($popularBooks) > 0)
            <div class="pt-4"></div>
        @endif
        @if (count($recentSearches) > 0)
            <div class="">
                <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                    عمليات البحث الأخيرة
                </h3>
                <div class="flex flex-wrap gap-2">
                    @foreach ($recentSearches as $term)
                        <div wire:click="selectRecentSearch('{{ $term }}')"
                            class="px-3 py-1 bg-base-200 hover:bg-base-300 rounded-sm cursor-pointer text-sm transition-colors">
                            <i class="fas fa-history text-xs text-gray-500 ml-1"></i>
                            {{ $term }}
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="divider my-3"></div>
        @endif

        <!-- Popular Searches Section -->
        @if (count($popularSearches) > 0)
            <div>
                <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                    عمليات البحث الشائعة</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach ($popularSearches as $search)
                        <div wire:click="selectPopularSearch('{{ $search->search_term }}')"
                            class="px-3 py-1 bg-base-200 hover:bg-base-300 rounded-sm cursor-pointer text-sm transition-colors">
                            <i class="fas fa-arrow-trend-up text-xs  ml-1"></i>
                            {{ $search->search_term }}
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="divider my-3"></div>
        @endif

        <!-- Popular Searches Section -->
        @if (count($popularBooks) > 0)
            <div>
                <h3 class="text-sm font-semibold opacity-75 mb-3 text-start justify-start">
                    الكتب الشائعة
                </h3>
                <div class="flex flex-wrap gap-2">
                    @foreach ($popularBooks as $book)
                        @livewire('book-card', ['book' => $book, 'size' => 'small'], key('search-book-' . $book->id . '-' . time()))
                    @endforeach
                </div>
            </div>
            <div class="divider my-3"></div>
        @endif

    </div>

    <!-- Search Results -->
    <div x-show=" ($wire.showResults && !$wire.isSearching)" id="autocomplete-results"
        class="p-3 max-h-[75vh] overflow-y-auto md:max-h-96 {{-- Mobile: larger max-height, Desktop: fixed max-height --}}">
        <div class="justify-between items-center mb-4 flex  col-span-full ">
            <div role="tablist" class="tabs tabs-box ">
                @if ($currentBookId)
                    <a role="tab" wire:click="selectSearchType('all_content')"
                        class="tab flex-1 cursor-pointer {{ $searchType == 'all_content' ? 'tab-active' : '' }}">النصوص</a>
                    <a role="tab" wire:click="selectSearchType('current_book')"
                        class="tab flex-1 cursor-pointer {{ $searchType == 'current_book' ? 'tab-active' : '' }}">الكتاب
                        الحالي</a>
                @endif
            </div>
            @if ($resultsCount > 0)
                <a href="{{ url('/advanced_search?q=' . urlencode($query) . '&search_type=' . $searchType) }}"
                    class="text-primary text-sm hover:underline flex-grow text-end">عرض الكل
                    ({{ $resultsCount }})</a>
            @endif
        </div>

        <!-- Loading Shimmer (moved below tabs) -->
        <div x-show="$wire.isSearching" class="w-full p-4 col-span-full">
            <div class="animate-pulse space-y-4">
                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                <div class="flex flex-wrap gap-2">
                    <div class="h-8 bg-gray-200 rounded-full w-24"></div>
                    <div class="h-8 bg-gray-200 rounded-full w-32"></div>
                    <div class="h-8 bg-gray-200 rounded-full w-20"></div>
                </div>
                <div class="h-4 bg-gray-200 rounded w-1/3 mt-4"></div>
                <div class="grid grid-cols-2 gap-3">
                    <div class="h-24 bg-gray-200 rounded"></div>
                    <div class="h-24 bg-gray-200 rounded"></div>
                    <div class="h-24 bg-gray-200 rounded"></div>
                    <div class="h-24 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>


        @foreach ($autoCompleteResults as $result)
            @include('livewire.search-result-item', ['query' => $query])
        @endforeach

        <!-- Expanded Content Section -->
        @if($expandedContent)
            <div class="border-t border-gray-200 mt-4 pt-4"
                 x-data="{ expanded: @entangle('expandedContent') }"
                 x-show="expanded"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95">

                <!-- Header with close button -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex-1">
                        @if($expandedBook)
                            <h3 class="text-lg font-bold text-base-content" style="font-family: Ziydia-Bold;">
                                {{ $expandedBook->title }}
                            </h3>
                            @if($expandedBook->author)
                                <p class="text-sm text-icons mt-1">
                                    {{ $expandedBook->author->name }}
                                    @if($expandedBook->author->death_date != 'معاصر')
                                        (ت: {{ $expandedBook->author->formattedDeathDate() }})
                                    @endif
                                </p>
                            @endif
                        @endif
                    </div>
                    <button wire:click="closeExpandedContent"
                            class="text-icons hover:text-base-content focus:outline-none ml-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Content Area -->
                <div class="bg-base-100 rounded-lg p-4 max-h-96 overflow-y-auto">
                    @if($expandedIsLoading)
                        <!-- Loading State -->
                        <div class="flex items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            <span class="mr-3 text-icons">جاري التحميل...</span>
                        </div>
                    @else
                        <!-- Page Header -->
                        @if($expandedBook && $expandedCurrentTitle)
                            <div class="mb-4">
                                <div class="border-r-4 px-3 py-2 border-primary bg-base-200 rounded-r">
                                    <h4 class="text-base font-bold text-base-content" style="font-family: Ziydia-Bold;">
                                        {{ $expandedCurrentTitle }}
                                    </h4>
                                    <p class="text-sm text-icons">
                                        الصفحة {{ $expandedPage }}
                                        @if($expandedPart) من الجزء {{ $expandedPart }} @endif
                                    </p>
                                </div>
                            </div>
                        @endif

                        <!-- Content Text -->
                        <div class="prose prose-sm max-w-none text-base-content leading-relaxed">
                            {!! $expandedContentText !!}
                        </div>
                    @endif
                </div>

                <!-- Navigation Controls -->
                @if(!$expandedIsLoading && $expandedPagination)
                    <div class="flex items-center justify-between mt-4 pt-3 border-t border-gray-200">
                        <button wire:click="navigateExpandedContent('prev')"
                                @if(!$expandedPagination['previousPage']) disabled @endif
                                class="flex items-center px-3 py-2 text-sm bg-base-200 hover:bg-base-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            السابق
                        </button>

                        <span class="text-sm text-icons">
                            صفحة {{ $expandedPage }} من {{ $expandedPagination['lastPage'] ?? 'غير محدد' }}
                        </span>

                        <button wire:click="navigateExpandedContent('next')"
                                @if(!$expandedPagination['nextPage']) disabled @endif
                                class="flex items-center px-3 py-2 text-sm bg-base-200 hover:bg-base-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            التالي
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                @endif
            </div>
        @endif
    </div>
    @if (!empty($query) && strlen($query) > 2 && count($autoCompleteResults) === 0)
        <div x-init="open = false" id="autocomplete-results"
            class="absolute left-0 right-0 mt-1 mx-2 bg-white rounded-lg border border-gray-200 z-50">
            <div class="p-4 text-center text-gray-600">
                <i class="fas fa-search-minus text-gray-400 text-2xl mb-2"></i>
                <p>لا توجد نتائج للبحث عن "{{ $query }}"</p>
                <p class="text-sm text-gray-500 mt-1">حاول استخدام كلمات مفتاحية أخرى</p>
            </div>
        </div>
    @endif
</div>
