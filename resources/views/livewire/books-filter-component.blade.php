<div class="container mx-auto px-4 py-8">

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar (Right Side) -->
        <div class="order-2 lg:order-1 hidden lg:block">
            <!-- Year Filter (Death Date) -->
            @if ($showYearFilter)
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <div>
                        <button class="flex justify-between items-center w-full font-semibold mb-2">
                            <span>وفاة المؤلف (هجري)</span>
                        </button>
                        <div class="mt-2">
                            <div class="flex gap-2">
                                <div class="w-1/2">
                                    <input id="year-from" type="number" class="input input-bordered w-full"
                                        placeholder="من" wire:model.live.debounce.500ms="yearFrom">
                                </div>
                                <div class="w-1/2">
                                    <input id="year-to" type="number" class="input input-bordered w-full"
                                        placeholder="إلى" wire:model.live.debounce.500ms="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Categories Filter -->
            @if ($allCategories)
                <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                    <div>
                        <button class="flex justify-between items-center w-full font-semibold mb-2">
                            <span>التصنيفات</span>
                        </button>
                        <div class="mt-2">
                            <div class="category-list ">
                                @foreach ($allCategories as $category)
                                    <div class="form-control mb-1" wire:key="category-{{ $category->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $category->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="categories">
                                            <span class="label-text">{{ $category->title }}
                                                ({{ $category->books_count }})
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Authors Filter -->
            @if ($allAuthors)
                <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                    <div>
                        <button class="flex justify-between items-center w-full font-semibold mb-2">
                            <span>المؤلفين</span>
                        </button>
                        <div class="mt-2">
                            <div class="relative mb-2">
                                <input type="text" class="input input-bordered w-full pr-10"
                                    placeholder="البحث عن مؤلف" wire:model.live.debounce.500ms="authorSearchTerm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="author-list min-h-[120px]">
                                @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                                    <div class="form-control mb-1" wire:key="author-{{ $author->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $author->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="authors">
                                            <span class="label-text">{{ $author->name }}
                                                @if ($author->death_date && $author->death_date != 'معاصر')
                                                    <span class="text-xs text-base-content/70">(ت:
                                                        {{ $author->death_date }})</span>
                                                @endif
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @if ($allAuthors->count() > $visibleAuthorsCount)
                                <button class="btn btn-ghost btn-sm w-full mt-2 text-primary text-lg"
                                    wire:click="loadMoreAuthors">
                                    تحميل المزيد
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Main Content Area -->
        <div class="lg:w-3/4 order-1 lg:order-2">
            <!-- Search and Filter Header -->
            <div class="mb-6">
                <!-- Mobile Search Bar (Visible on mobile only) -->
                <div class="flex items-center gap-2 lg:hidden">
                    <div class="relative flex-grow">
                        <input type="text" placeholder="البحث عن كتاب" class="input input-bordered w-full pr-10"
                            wire:model.live.debounce.500ms="query">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>

                    <!-- Filter Button for Mobile -->
                    <button class="btn btn-outline flex justify-between items-center"
                        onclick="document.getElementById('mobile-filters-drawer').classList.toggle('translate-x-full')">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                    </button>
                    <!-- Sort Button for Mobile -->
                    <div class="dropdown dropdown-end">
                        <button class="btn btn-outline flex justify-between items-center"> <i
                                class="fa-solid fa-arrow-down-wide-short"></i>
                        </button>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a wire:click="$set('sort', 'newest')"
                                    class="{{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث</a>
                            </li>
                            <li><a wire:click="$set('sort', 'oldest')"
                                    class="{{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم</a>
                            </li>
                            <li><a wire:click="$set('sort', 'title_asc')"
                                    class="{{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                    تصاعدي</a></li>
                            <li><a wire:click="$set('sort', 'title_desc')"
                                    class="{{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                    تنازلي</a></li>
                            @if ($showYearFilter)
                                <li><a wire:click="$set('sort', 'author_asc')"
                                        class="{{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تصاعدي</a></li>
                                <li><a wire:click="$set('sort', 'author_desc')"
                                        class="{{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تنازلي</a></li>
                                <li><a wire:click="$set('sort', 'author_die_data_asc')"
                                        class="{{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تصاعدي</a></li>
                                <li><a wire:click="$set('sort', 'author_die_data_desc')"
                                        class="{{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تنازلي</a></li>
                            @endif
                        </ul>
                    </div>
                </div>

                <!-- Desktop Search and Sort (Hidden on mobile) -->
                <div class="hidden lg:flex flex-col md:flex-row gap-4 items-center">
                    <div class="relative flex-grow">
                        <input type="text" placeholder="البحث عن كتاب"
                            class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                            wire:model.live.debounce.500ms="query">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>

                    <div class="w-full md:w-auto">
                        <div class="dropdown">
                            @php
                                if (!$showYearFilter) {
                                    $sortLabels = [
                                        'newest' => 'الأحدث',
                                        'oldest' => 'الأقدم',
                                        'title_asc' => 'العنوان تصاعدي',
                                        'title_desc' => 'العنوان تنازلي',
                                    ];
                                } else {
                                    $sortLabels = [
                                        'newest' => 'الأحدث',
                                        'oldest' => 'الأقدم',
                                        'title_asc' => 'العنوان تصاعدي',
                                        'title_desc' => 'العنوان تنازلي',
                                        'author_asc' => 'المؤلف تصاعدي',
                                        'author_desc' => 'المؤلف تنازلي',
                                        'author_die_data_asc' => 'وفاة المؤلف تصاعدي',
                                        'author_die_data_desc' => 'وفاة المؤلف تنازلي',
                                    ];
                                }

                            @endphp
                            <div tabindex="0"
                                class="h-10 btn btn-outline min-w-[200px] justify-between bg-none border-base-300 font-light">
                                {{ $sortLabels[$sort] ?? 'ترتيب حسب' }}
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50" aria-hidden="true">
                                    <path d="m7 15 5 5 5-5"></path>
                                    <path d="m7 9 5-5 5 5"></path>
                                </svg>
                            </div>
                            <ul tabindex="0"
                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                                <li><a value="newest" wire:click="$set('sort', 'newest')"
                                        class="sort-option {{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث
                                        @if ($sort == 'newest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="oldest" wire:click="$set('sort', 'oldest')"
                                        class="sort-option {{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم
                                        @if ($sort == 'oldest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_asc" wire:click="$set('sort', 'title_asc')"
                                        class="sort-option {{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تصاعدي @if ($sort == 'title_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_desc" wire:click="$set('sort', 'title_desc')"
                                        class="sort-option {{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تنازلي @if ($sort == 'title_desc')
                                        @endif
                                    </a>
                                </li>
                                @if ($showYearFilter)
                                <li><a value="author_asc" wire:click="$set('sort', 'author_asc')"
                                        class="sort-option {{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تصاعدي @if ($sort == 'author_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_desc" wire:click="$set('sort', 'author_desc')"
                                        class="sort-option {{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تنازلي @if ($sort == 'author_desc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_asc" wire:click="$set('sort', 'author_die_data_asc')"
                                        class="sort-option {{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تصاعدي @if ($sort == 'author_die_data_asc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_desc" wire:click="$set('sort', 'author_die_data_desc')"
                                        class="sort-option {{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تنازلي @if ($sort == 'author_die_data_desc')
                                        @endif
                                    </a></li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Filters Drawer -->
            <div id="mobile-filters-drawer" wire:ignore.self
                class="lg:hidden fixed top-0 right-0 w-80 h-full bg-base-100 shadow-xl z-50 transform translate-x-full transition-transform duration-300 ease-in-out overflow-y-auto">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">تصفية</h3>
                        <button class="btn btn-ghost btn-sm p-1"
                            onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Sort options removed from drawer -->

                    <!-- Death Date Filter -->
                    <div class="mb-4 border-t pt-4  hidden">
                        <h4 class="font-semibold mb-2">وفاة المؤلف (هجري)</h4>
                        <div class="mt-2">
                            <div class="relative w-full flex items-center mt-2 mb-4" style="height: 2.5rem;">
                                <input id="mobile-year-range-min" type="range" min="0" max="1446"
                                    value="{{ $yearFrom ?: 0 }}"
                                    class="range range-primary w-full absolute pointer-events-auto"
                                    style="z-index:2; background: transparent;"
                                    wire:change="$dispatch('year-range-changed', {type: 'min', value: $event.target.value})">
                                <input id="mobile-year-range-max" type="range" min="0" max="1446"
                                    value="{{ $yearTo ?: 1446 }}"
                                    class="range range-primary w-full absolute pointer-events-auto"
                                    style="z-index:1; background: transparent;"
                                    wire:change="$dispatch('year-range-changed', {type: 'max', value: $event.target.value})">
                            </div>
                            <div class="flex gap-2">
                                <div class="w-1/2">
                                    <input id="mobile-year-from" type="number" class="input input-bordered w-full"
                                        placeholder="من" wire:model="yearFrom">
                                </div>
                                <div class="w-1/2">
                                    <input id="mobile-year-to" type="number" class="input input-bordered w-full"
                                        placeholder="إلى" wire:model="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Categories Filter -->
                    @if ($allCategories)
                        <div class="mb-4  pt-4">
                            <h4 class="font-semibold mb-2">التصنيفات</h4>
                            <div class="category-list  overflow-y-auto">
                                @foreach ($allCategories as $category)
                                    <div class="form-control mb-1" wire:key="mobile-category-{{ $category->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $category->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="categories">
                                            <span class="label-text">{{ $category->title }}
                                                ({{ $category->books_count }})
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Authors Filter -->
                    @if ($allAuthors)
                        <div class="mb-4  pt-4 pb-16">
                            <h4 class="font-semibold mb-2">المؤلفين</h4>
                            <div class="relative mb-2">
                                <input type="text" class="input input-bordered w-full pr-10"
                                    placeholder="البحث عن مؤلف" wire:model.live.debounce.500ms="authorSearchTerm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="author-list  overflow-y-auto">
                                @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                                    <div class="form-control mb-1" wire:key="mobile-author-{{ $author->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $author->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="authors">
                                            <span class="label-text">{{ $author->name }}
                                                @if ($author->death_date && $author->death_date != 'معاصر')
                                                    <span class="text-xs text-base-content/70">(ت:
                                                        {{ $author->death_date }})</span>
                                                @endif
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @if ($allAuthors->count() > $visibleAuthorsCount)
                                <button class="btn btn-ghost btn-sm w-full mt-2 text-primary text-lg"
                                    wire:click="loadMoreAuthors">
                                    تحميل المزيد
                                </button>
                            @endif
                        </div>
                    @endif
                    <!-- Apply Filters Button -->
                    <div class="fixed bottom-0 left-0 right-0 pb-4 pt-2 bg-base-100 shadow-lg z-50 px-4 w-80">
                        <button class="btn btn-primary w-full" wire:click="$refresh"
                            onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            تطبيق
                        </button>
                    </div>
                </div>
            </div>

            <!-- Book Grid with Loading State -->
            <div>
                <!-- Loading Indicator -->
                <div wire:loading
                    wire:target="categories, authors, query, sort, yearFrom, yearTo, refresh, nextPage, previousPage, gotoPage"
                    class="flex justify-center items-center py-12">
                    <span class="loading loading-spinner loading-lg"></span>
                </div>

                <!-- Books Grid (hidden when loading) -->
                <div wire:loading.remove
                    wire:target="categories, authors, query, sort, yearFrom, yearTo, refresh, nextPage, previousPage, gotoPage">
                    @if ($books->count() > 0)
                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-6 ">
                            @foreach ($books as $book)
                                <div class="flex justify-start" wire:key="book-{{ $book->id }}">
                                    <div class="hidden lg:block">
                                        @livewire('book-card', ['book' => $book, 'imageWidth' => '210px', 'imageHeight' => '300px', 'size' => 'normal'], key('book-card-' . $book->id))
                                    </div>
                                    <div class="lg:hidden">
                                        @livewire('book-card', ['book' => $book, 'isMobile' => true, 'imageWidth' => '160px', 'imageHeight' => '220px'], key('book-card-mobile-' . $book->id))
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-8 flex justify-center" dir="rtl">
                            <div class="join gap-1">
                                @if ($books->onFirstPage())
                                @else
                                    <button wire:click="previousPage"
                                        class="join-item btn btn-ghost hidden sm:block">‹ السابق</button>
                                    <button wire:click="previousPage"
                                        class="join-item btn btn-ghost sm:hidden">‹</button>
                                @endif

                                @php
                                    $start = max($books->currentPage() - 2, 1);
                                    $end = min($start + 2, $books->lastPage());
                                    if ($end - $start < 2) {
                                        $start = max($end - 2, 1);
                                    }

                                    function toArabicNumbers($number)
                                    {
                                        $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                                        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                                        return str_replace($western, $arabic, $number);
                                    }
                                @endphp

                                @if ($start > 1)
                                    <button wire:click="gotoPage(1)"
                                        class="join-item btn btn-ghost {{ $books->currentPage() == 1 ? 'btn-active' : '' }}">{{ toArabicNumbers('1') }}</button>
                                    @if ($start > 2)
                                        <button class="join-item btn btn-ghost btn-disabled">...</button>
                                    @endif
                                @endif

                                @for ($i = $start; $i <= $end; $i++)
                                    <button wire:click="gotoPage({{ $i }})"
                                        class="join-item btn btn-ghost {{ $books->currentPage() == $i ? 'border border-base-300' : '' }}">{{ toArabicNumbers($i) }}</button>
                                @endfor

                                @if ($end < $books->lastPage())
                                    @if ($end < $books->lastPage() - 1)
                                        <button class="join-item btn btn-ghost btn-disabled">...</button>
                                    @endif
                                    <button wire:click="gotoPage({{ $books->lastPage() }})"
                                        class="join-item btn btn-ghost {{ $books->currentPage() == $books->lastPage() ? 'btn-active' : '' }}">{{ toArabicNumbers($books->lastPage()) }}</button>
                                @endif

                                @if ($books->hasMorePages())
                                    <button wire:click="nextPage"
                                        class="join-item btn btn-ghost hidden md:block">التالي ›</button>
                                    <button wire:click="nextPage"
                                        class="join-item btn btn-ghost sm:hidden ">›</button>
                                @else
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="flex flex-col items-center justify-center py-12 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-base-content/30 mb-4"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            <h3 class="text-xl font-semibold mb-2">لا توجد كتب</h3>
                            <p class="text-base-content/70">لم يتم العثور على كتب تطابق معايير البحث الخاصة بك</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('livewire:initialized', function() {
        // Preserve drawer state during Livewire updates

        // Sort functionality is now handled directly by Livewire wire:click
        // Handle year slider changes
        Livewire.on('year-range-changed', (data) => {
            aaa
            // For desktop sliders
            const yearFromInput = document.getElementById('year-from');
            const yearToInput = document.getElementById('year-to');

            // For mobile sliders
            const mobileYearFromInput = document.getElementById('mobile-year-from');
            const mobileYearToInput = document.getElementById('mobile-year-to');


            if (data.type === 'min') {

                Livewire.dispatch('input', {
                    id: yearFromInput?.getAttribute('wire:model')?.split('.')[0] ||
                        mobileYearFromInput?.getAttribute('wire:model')?.split('.')[0],
                    value: min
                });
            }

            if (data.type === 'max') {
                Livewire.dispatch('input', {
                    id: yearToInput?.getAttribute('wire:model')?.split('.')[0] ||
                        mobileYearToInput?.getAttribute('wire:model')?.split('.')[0],
                    value: max
                });
            }
        });

        // Add overlay for drawer
        const body = document.querySelector('body');
        let overlay;

        function createOverlay() {
            overlay = document.createElement('div');
            overlay.id = 'drawer-overlay';
            overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 hidden';
            overlay.addEventListener('click', closeDrawer);
            body.appendChild(overlay);
        }

        function closeDrawer() {
            const drawer = document.getElementById('mobile-filters-drawer');
            drawer.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }

        createOverlay();

        // Show/hide overlay when drawer is opened/closed
        const filterButton = document.querySelector('[onclick*="mobile-filters-drawer"]');
        const drawerCloseButtons = document.querySelectorAll('[onclick*="translate-x-full"]');

        if (filterButton) {
            filterButton.addEventListener('click', function() {
                overlay.classList.remove('hidden');
            });
        }

        drawerCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                overlay.classList.add('hidden');
            });
        });

        // Mobile sort functionality is now handled by the built-in dropdown component
    });
</script>
