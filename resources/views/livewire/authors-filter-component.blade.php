<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar (Right Side) -->
        <div class="order-2 lg:order-1 hidden lg:block">
            <!-- Year Filter (Death Date) -->
            <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                <div>
                    <button class="flex justify-between items-center w-full font-semibold mb-2">
                        <span>وفاة المؤلف (هجري)</span>
                    </button>
                    <div class="mt-2">
                        <div class="flex gap-2">
                            <div class="w-1/2">
                                <input id="year-from" type="number" class="input input-bordered w-full" placeholder="من"
                                    wire:model.live.debounce.500ms="yearFrom">
                            </div>
                            <div class="w-1/2">
                                <input id="year-to" type="number" class="input input-bordered w-full"
                                    placeholder="إلى" wire:model.live.debounce.500ms="yearTo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Filter -->
            <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                <div>
                    <button class="flex justify-between items-center w-full font-semibold mb-2">
                        <span>التصنيفات</span>
                    </button>
                    <div class="mt-2">
                        <div class="category-list min-h-[120px]">
                            @foreach ($allCategories as $category)
                                <div class="form-control mb-1" wire:key="category-{{ $category->id }}">
                                    <label class="label cursor-pointer justify-start gap-2 py-1">
                                        <input type="checkbox" value="{{ $category->id }}"
                                            class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                            wire:model.live="categories">
                                        <span class="label-text">{{ $category->title }}
                                            ({{ $category->books_count }})</span>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:w-3/4 order-1 lg:order-2">
            <!-- Search and Filter Header -->
            <div class="mb-6">
                <!-- Mobile Search Bar (Visible on mobile only) -->
                <div class="flex items-center gap-2 lg:hidden">
                    <div class="relative flex-grow">
                        <input type="text" placeholder="البحث عن مؤلف"
                            class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                            wire:model.live.debounce.500ms="query">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                    <!-- Filter -->
                    <button class="btn btn-outline flex justify-between items-center"                    onclick="document.getElementById('mobile-filters-drawer').classList.toggle('translate-x-full')">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                    </button>
                    <!-- Sort -->
                    <div class="dropdown dropdown-end">
                    <button class="btn btn-outline flex justify-between items-center">                            <i class="fa-solid fa-arrow-down-wide-short"></i>
                        </button>
                        <ul tabindex="0"
                            class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                            <li><a wire:click="$set('sort', 'name_asc')"
                                    class="sort-option {{ $sort == 'name_asc' ? 'active font-bold text-primary' : '' }}">الإسم
                                    تصاعدي</a></li>
                            <li><a wire:click="$set('sort', 'name_desc')"
                                    class="sort-option {{ $sort == 'name_desc' ? 'active font-bold text-primary' : '' }}">الإسم
                                    تنازلي</a></li>
                            <li><a wire:click="$set('sort', 'death_date_asc')"
                                    class="sort-option {{ $sort == 'death_date_asc' ? 'active font-bold text-primary' : '' }}">تاريخ
                                    الوفاة تصاعدي</a></li>
                            <li><a wire:click="$set('sort', 'death_date_desc')"
                                    class="sort-option {{ $sort == 'death_date_desc' ? 'active font-bold text-primary' : '' }}">تاريخ
                                    الوفاة تنازلي</a></li>
                            <li><a wire:click="$set('sort', 'books_count_asc')"
                                    class="sort-option {{ $sort == 'books_count_asc' ? 'active font-bold text-primary' : '' }}">عدد
                                    الكتب تصاعدي</a></li>
                            <li><a wire:click="$set('sort', 'books_count_desc')"
                                    class="sort-option {{ $sort == 'books_count_desc' ? 'active font-bold text-primary' : '' }}">عدد
                                    الكتب تنازلي</a></li>
                        </ul>
                    </div>
                  

                </div>

                <!-- Desktop Search and Sort (Hidden on mobile) -->
                <div class="hidden lg:flex flex-col md:flex-row gap-4 items-center">
                    <div class="relative flex-grow">
                        <input type="text" placeholder="البحث عن مؤلف"
                            class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                            wire:model.live.debounce.500ms="query">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>

                    <div class="w-full md:w-auto ">
                        <div class="dropdown">
                            @php
                                $sortLabels = [
                                    'name_asc' => 'الإسم تصاعدي',
                                    'name_desc' => 'الإسم تنازلي',
                                    'death_date_asc' => 'تاريخ الوفاة تصاعدي',
                                    'death_date_desc' => 'تاريخ الوفاة تنازلي',
                                    'books_count_asc' => 'عدد الكتب تصاعدي',
                                    'books_count_desc' => 'عدد الكتب تنازلي',
                                ];
                            @endphp
                            <div tabindex="0"
                                class="h-10 btn btn-outline min-w-[200px] justify-between bg-none border-base-300 font-light">
                                {{ $sortLabels[$sort] ?? 'ترتيب حسب' }}
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50" aria-hidden="true">
                                    <path d="m7 15 5 5 5-5"></path>
                                    <path d="m7 9 5-5 5 5"></path>
                                </svg>
                            </div>
                            <ul tabindex="0"
                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                                <li><a wire:click="$set('sort', 'name_asc')"
                                        class="sort-option {{ $sort == 'name_asc' ? 'active font-bold text-primary' : '' }}">الإسم
                                        تصاعدي</a></li>
                                <li><a wire:click="$set('sort', 'name_desc')"
                                        class="sort-option {{ $sort == 'name_desc' ? 'active font-bold text-primary' : '' }}">الإسم
                                        تنازلي</a></li>
                                <li><a wire:click="$set('sort', 'death_date_asc')"
                                        class="sort-option {{ $sort == 'death_date_asc' ? 'active font-bold text-primary' : '' }}">تاريخ
                                        الوفاة تصاعدي</a></li>
                                <li><a wire:click="$set('sort', 'death_date_desc')"
                                        class="sort-option {{ $sort == 'death_date_desc' ? 'active font-bold text-primary' : '' }}">تاريخ
                                        الوفاة تنازلي</a></li>
                                <li><a wire:click="$set('sort', 'books_count_asc')"
                                        class="sort-option {{ $sort == 'books_count_asc' ? 'active font-bold text-primary' : '' }}">عدد
                                        الكتب تصاعدي</a></li>
                                <li><a wire:click="$set('sort', 'books_count_desc')"
                                        class="sort-option {{ $sort == 'books_count_desc' ? 'active font-bold text-primary' : '' }}">عدد
                                        الكتب تنازلي</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Filters Drawer -->
            <div id="mobile-filters-drawer"
                class="lg:hidden fixed top-0 right-0 w-80 h-full bg-base-100 shadow-xl z-50 transform translate-x-full transition-transform duration-300 ease-in-out overflow-y-auto">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">تصفية</h3>
                        <button class="btn btn-ghost btn-sm p-1"
                            onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Year Filter (Death Date) -->
                    <div class="mb-4">
                        <h3 class="font-semibold mb-2">وفاة المؤلف (هجري)</h3>

                        <div class="flex gap-2">
                            <div class="w-1/2">
                                <input id="year-from-mobile" type="number" class="input input-bordered w-full"
                                    placeholder="من" wire:model="yearFrom">
                            </div>
                            <div class="w-1/2">
                                <input id="year-to-mobile" type="number" class="input input-bordered w-full"
                                    placeholder="إلى" wire:model="yearTo">
                            </div>
                        </div>
                    </div>

                    <!-- Categories Filter -->
                    <div class="mt-4">
                        <h3 class="font-semibold mb-2">التصنيفات</h3>
                        <div class="category-list mt-2  overflow-y-auto">
                            @foreach ($allCategories as $category)
                                <div class="form-control mb-1" wire:key="mobile-category-{{ $category->id }}">
                                    <label class="label cursor-pointer justify-start gap-2 py-1">
                                        <input type="checkbox" value="{{ $category->id }}"
                                            class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                            wire:model="categories">
                                        <span class="label-text">{{ $category->title }}
                                            ({{ $category->books_count }})</span>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Apply Filters Button -->
                    <div class="mt-4 sticky bottom-0 pb-4 pt-2 bg-base-100">
                    <button class="btn btn-primary w-full"
                            wire:click="$refresh" onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            تطبيق
                        </button>
                    </div>
                </div>
            </div>

            <!-- Authors Grid with Loading State -->
            <div>
                <!-- Loading Indicator -->
                <div wire:loading
                    wire:target="categories, query, sort, yearFrom, yearTo, refresh, nextPage, previousPage, gotoPage"
                    class="flex justify-center items-center py-12">
                    <span class="loading loading-spinner loading-lg"></span>
                </div>

                <!-- Authors Grid (hidden when loading) -->
                <div wire:loading.remove
                    wire:target="categories, query, sort, yearFrom, yearTo, refresh, nextPage, previousPage, gotoPage">
                    @if ($authors->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
                            @foreach ($authors as $author)
                                <div class="bg-base-100 shadow-md rounded-lg overflow-hidden border border-base-300 flex flex-col h-full"
                                    wire:key="author-{{ $author->id }}">
                                    <a href="{{ url('/admin/v2/authors/' . $author->id) }}"
                                        class="block p-4 flex-grow">
                                        <div class="flex items-center gap-4">
                                            @if ($author->getFirstMediaUrl('authors'))
                                                <div
                                                    class="w-16 h-16 rounded-full overflow-hidden bg-base-200 flex-shrink-0">
                                                    <img src="{{ $author->getFirstMediaUrl('authors', 'preview') }}"
                                                        alt="{{ $author->name }}" class="w-full h-full object-cover">
                                                </div>
                                            @else
                                                <div
                                                    class="w-16 h-16 rounded-full overflow-hidden bg-base-200 flex-shrink-0 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="h-8 w-8 text-base-content/30" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                    </svg>
                                                </div>
                                            @endif
                                            <div>
                                                <h3 class="font-semibold text-lg mb-1">{{ $author->name }}</h3>
                                                @if ($author->death_date && $author->death_date != 'معاصر')
                                                    <p class="text-sm text-base-content/70">توفي:
                                                        {{ $author->formattedDeathDate() }}</p>
                                                @elseif($author->death_date == 'معاصر')
                                                    <p class="text-sm text-base-content/70">{{ $author->death_date }}
                                                    </p>
                                                @endif
                                            </div>
                                        </div>
                                        @if ($author->nickname)
                                            <p class="mt-2 text-sm">{{ $author->nickname }}</p>
                                        @endif
                                        <div class="flex justify-between items-center mt-3">
                                            <span class="text-sm text-primary">{{ $author->books_count }} كتاب</span>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-8 flex justify-center" dir="rtl">
                            <div class="join gap-1">
                                @if ($authors->onFirstPage())
                                @else
                                <button wire:click="previousPage" class="join-item btn btn-ghost hidden sm:block">‹ السابق</button>
                                <button wire:click="previousPage" class="join-item btn btn-ghost sm:hidden">‹</button>
                                @endif

                                @php
                                    $start = max($authors->currentPage() - 2, 1);
                                    $end = min($start + 2, $authors->lastPage());
                                    if ($end - $start < 2) {
                                        $start = max($end - 2, 1);
                                    }

                                    function toArabicNumbers($number)
                                    {
                                        $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                                        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                                        return str_replace($western, $arabic, $number);
                                    }
                                @endphp

                                @if ($start > 1)
                                    <button wire:click="gotoPage(1)"
                                        class="join-item btn btn-ghost {{ $authors->currentPage() == 1 ? 'btn-active' : '' }}">{{ toArabicNumbers('1') }}</button>
                                    @if ($start > 2)
                                        <button class="join-item btn btn-ghost btn-disabled">...</button>
                                    @endif
                                @endif

                                @for ($i = $start; $i <= $end; $i++)
                                    <button wire:click="gotoPage({{ $i }})"
                                        class="join-item btn btn-ghost {{ $authors->currentPage() == $i ? 'border border-base-300' : '' }}">{{ toArabicNumbers($i) }}</button>
                                @endfor

                                @if ($end < $authors->lastPage())
                                    @if ($end < $authors->lastPage() - 1)
                                        <button class="join-item btn btn-ghost btn-disabled">...</button>
                                    @endif
                                    <button wire:click="gotoPage({{ $authors->lastPage() }})"
                                        class="join-item btn btn-ghost {{ $authors->currentPage() == $authors->lastPage() ? 'btn-active' : '' }}">{{ toArabicNumbers($authors->lastPage()) }}</button>
                                @endif

                                @if ($authors->hasMorePages())
                                <button wire:click="nextPage" class="join-item btn btn-ghost hidden md:block">التالي ›</button>
                                <button wire:click="nextPage" class="join-item btn btn-ghost sm:hidden ">›</button>
                                @else
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="flex flex-col items-center justify-center py-12 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-base-content/30 mb-4"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            <h3 class="text-xl font-semibold mb-2">لا يوجد مؤلفين</h3>
                            <p class="text-base-content/70">لم يتم العثور على مؤلفين تطابق معايير البحث الخاصة بك</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('livewire:initialized', function() {
        // Handle year slider changes
        Livewire.on('year-range-changed', (data) => {
            const yearFromInput = document.getElementById('year-from');
            const yearToInput = document.getElementById('year-to');

            // Mobile inputs
            const yearFromInputMobile = document.getElementById('year-from-mobile');
            const yearToInputMobile = document.getElementById('year-to-mobile');

            const minGap = 1;

            if (data.type === 'min') {
                let min = parseInt(data.value);



                if (yearFromInput) yearFromInput.value = min;
                if (yearFromInputMobile) yearFromInputMobile.value = min;

                Livewire.dispatch('input', {
                    id: yearFromInput?.getAttribute('wire:model')?.split('.')[0] ||
                        yearFromInputMobile?.getAttribute('wire:model')?.split('.')[0],
                    value: min
                });
            }

            if (data.type === 'max') {
                let max = parseInt(data.value);


                if (yearToInput) yearToInput.value = max;
                if (yearToInputMobile) yearToInputMobile.value = max;

                Livewire.dispatch('input', {
                    id: yearToInput?.getAttribute('wire:model')?.split('.')[0] ||
                        yearToInputMobile?.getAttribute('wire:model')?.split('.')[0],
                    value: max
                });
            }
        });

        // Add overlay for drawer
        const body = document.querySelector('body');
        let overlay;

        function createOverlay() {
            overlay = document.createElement('div');
            overlay.id = 'drawer-overlay';
            overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 hidden';
            overlay.addEventListener('click', closeDrawer);
            body.appendChild(overlay);
        }

        function closeDrawer() {
            const drawer = document.getElementById('mobile-filters-drawer');
            drawer.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }

        createOverlay();

        // Show/hide overlay when drawer is opened/closed
        const filterButton = document.querySelector('[onclick*="mobile-filters-drawer"]');
        const drawerCloseButtons = document.querySelectorAll('[onclick*="translate-x-full"]');

        if (filterButton) {
            filterButton.addEventListener('click', function() {
                overlay.classList.remove('hidden');
            });
        }

        drawerCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                overlay.classList.add('hidden');
            });
        });
    });
</script>
