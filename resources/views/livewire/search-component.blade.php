<div class="flex-grow" x-data="{
    init() {
            // Initialize Alpine.js
        },
        mobileFiltersOpen: false
}">
    <section class="py-8 md:py-8 text-center bg-base-300">
        <div class="container mx-auto px-4 items-center">
            <div class="max-w-5xl mx-auto">
                <h1 class="text-3xl md:text-5xl font-bold mb-6 text-base-content leading-tight text-onsearch"
                    style="font-family: 'logo';">
                    بحث متقدم
                </h1>
            </div>
        </div>
    </section>
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
         

            <!-- Mobile Filters Drawer -->
            <div id="mobile-filters-drawer"
                class="fixed top-0 right-0 h-full w-80 bg-base-100 shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-full lg:hidden overflow-y-auto"
                :class="{ 'translate-x-0': mobileFiltersOpen, 'translate-x-full': !mobileFiltersOpen }">

                <!-- Drawer Header -->
                <div
                    class="p-4 border-b border-base-300 flex justify-between items-center sticky top-0 bg-base-100 z-10">
                    <h3 class="text-lg font-semibold">خيارات التصفية</h3>
                    <button @click="mobileFiltersOpen = false" class="btn btn-sm btn-circle btn-ghost">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Drawer Content (Filter Options) -->
                <div class="p-4 space-y-4">
                    <!-- Books filter -->
                    <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                        <h3 class="font-semibold mb-3">الكتب</h3>

                        <!-- Book Search -->
                        <div class="mb-3">
                            <input type="text" wire:model.live.debounce.300ms="bookSearchTerm"
                                placeholder="ابحث عن كتاب..." class="input input-bordered input-sm w-full">
                        </div>

                        <div class="space-y-2 max-h-60 overflow-y-auto">
                            @foreach ($allBooks->take($visibleBooksCount) as $book)
                            <div class="form-control mb-1" wire:key="mobile-book-{{ $book->id }}">
                                <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                    <input type="checkbox" wire:model.live="books" value="{{ $book->id }}"
                                        class="checkbox checkbox-sm">
                                    <span class="text-sm mr-2">{{ $book->title }}</span>
                                </label>
                            </div>
                            @endforeach

                            @if ($allBooks->count() > $visibleBooksCount)
                                <button wire:click="loadMoreBooks" class="btn btn-sm btn-outline w-full mt-2">
                                    عرض المزيد
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Categories Filter -->
                    <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                        <h3 class="font-semibold mb-3">التصانيف</h3>
                        <div class="space-y-2 max-h-60 overflow-y-auto">
                            @foreach ($allCategories as $category)
                            <div class="form-control mb-1" wire:key="mobile-category-{{ $category->id }}">
                            <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                    <input type="checkbox" wire:model.live="categories" value="{{ $category->id }}"
                                        class="checkbox checkbox-sm">
                                    <span class="text-sm">{{ $category->title }}</span>
                                    <span class="text-xs text-gray-500">({{ $category->books_count }})</span>
                                </label>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Authors Filter -->
                    <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                        <h3 class="font-semibold mb-3">المؤلفون</h3>

                        <!-- Author Search -->
                        <div class="mb-3">
                            <input type="text" wire:model.live.debounce.300ms="authorSearchTerm"
                                placeholder="ابحث عن مؤلف..." class="input input-bordered input-sm w-full">
                        </div>

                        <div class="space-y-2 max-h-60 overflow-y-auto">
                            @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                            <div class="form-control mb-1" wire:key="mobile-author-{{ $author->id }}">
                                <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                    <input type="checkbox" wire:model.live="authors" value="{{ $author->id }}"
                                        class="checkbox checkbox-sm">
                                    <span class="text-sm mr-2">{{ $author->name }}</span>
                                </label>
                            </div>
                            @endforeach

                            @if ($allAuthors->count() > $visibleAuthorsCount)
                                <button wire:click="loadMoreAuthors" class="btn btn-sm btn-outline w-full mt-2">
                                    عرض المزيد
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Year Filter (Death Date) -->
                    <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                        <h3 class="font-semibold mb-3">وفاة المؤلف (هجري)</h3>
                        <div class="flex gap-2">
                            <div class="w-1/2">
                                <input id="mobile-year-from" type="number" class="input input-bordered input-sm w-full"
                                    placeholder="من" wire:model.live.debounce.500ms="yearFrom">
                            </div>
                            <div class="w-1/2">
                                <input id="mobile-year-to" type="number" class="input input-bordered input-sm w-full"
                                    placeholder="إلى" wire:model.live.debounce.500ms="yearTo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overlay for mobile drawer -->
            <div class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300 lg:hidden"
                :class="{ 'opacity-100 pointer-events-auto': mobileFiltersOpen, 'opacity-0 pointer-events-none': !
                        mobileFiltersOpen }"
                @click="mobileFiltersOpen = false">
            </div>

            <!-- Desktop Sidebar (Right Side) -->
            <div class="order-2 lg:order-1 w-full lg:w-1/4 space-y-4 hidden lg:block">
                <!-- Books filter -->
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <h3 class="font-semibold mb-3">الكتب</h3>

                    <!-- Author Search -->
                    <div class="mb-3">
                        <input type="text" wire:model.live.debounce.300ms="bookSearchTerm"
                            placeholder="ابحث عن كتاب..." class="input input-bordered input-sm w-full">
                    </div>

                    <div class="space-y-2 max-h-60 overflow-y-auto">
                        @foreach ($allBooks->take($visibleBooksCount) as $book)
                            <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                <input type="checkbox" wire:model.live="books" value="{{ $book->id }}"
                                    class="checkbox checkbox-sm">
                                <span class="text-sm mr-2">{{ $book->title }}</span>
                            </label>
                        @endforeach

                        @if ($allBooks->count() > $visibleBooksCount)
                            <button wire:click="loadMoreBooks" class="btn btn-sm btn-outline w-full mt-2">
                                عرض المزيد
                            </button>
                        @endif
                    </div>
                </div>




                <!-- Categories Filter -->
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <h3 class="font-semibold mb-3">التصانيف</h3>
                    <div class="space-y-2 max-h-60 overflow-y-auto">
                        @foreach ($allCategories as $category)
                            <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                <input type="checkbox" wire:model.live="categories" value="{{ $category->id }}"
                                    class="checkbox checkbox-sm">
                                <span class="text-sm">{{ $category->title }}</span>
                                <span class="text-xs text-gray-500">({{ $category->books_count }})</span>
                            </label>
                        @endforeach
                    </div>
                </div>

                <!-- Authors Filter -->
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <h3 class="font-semibold mb-3">المؤلفون</h3>

                    <!-- Author Search -->
                    <div class="mb-3">
                        <input type="text" wire:model.live.debounce.300ms="authorSearchTerm"
                            placeholder="ابحث عن مؤلف..." class="input input-bordered input-sm w-full">
                    </div>

                    <div class="space-y-2 max-h-60 overflow-y-auto">
                        @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                            <label class="flex items-center space-x-2 rtl:space-x-reverse cursor-pointer">
                                <input type="checkbox" wire:model.live="authors" value="{{ $author->id }}"
                                    class="checkbox checkbox-sm">
                                <span class="text-sm">{{ $author->name }}</span>
                                <span class="text-xs text-gray-500">({{ $author->books_count }})</span>
                            </label>
                        @endforeach

                        @if ($allAuthors->count() > $visibleAuthorsCount)
                            <button wire:click="loadMoreAuthors" class="btn btn-sm btn-outline w-full mt-2">
                                عرض المزيد
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Year Filter (Death Date) -->
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <h3 class="font-semibold mb-3">وفاة المؤلف (هجري)</h3>
                    <div class="flex gap-2">
                        <div class="w-1/2">
                            <input id="year-from" type="number" class="input input-bordered input-sm w-full"
                                placeholder="من" wire:model.live.debounce.500ms="yearFrom">
                        </div>
                        <div class="w-1/2">
                            <input id="year-to" type="number" class="input input-bordered input-sm w-full"
                                placeholder="إلى" wire:model.live.debounce.500ms="yearTo">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content (Search) -->
            <div class="lg:w-3/4 order-1 lg:order-2">
                <!-- Search Bar and sort-->
                <div class="hidden lg:flex flex-col md:flex-row gap-4 items-center">
                    <div class="relative flex-grow">
                        <div class="flex">
                            <input type="text" wire:model.live.debounce.500ms="query" placeholder="ابحث..."
                                class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                                autocomplete="off" id="search-input">

                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="$wire.isSearching">
                                    <div
                                        class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                                    </div>
                                </template>
                                <template x-if="!$wire.isSearching">
                                    <i class="fas fa-search text-gray-400"></i>
                                </template>
                            </div>
                        </div>


                    </div>
                    <div class="w-full md:w-auto">
                        <div class="dropdown">
                            @php
                                $sortLabels = [
                                    'newest' => 'الأحدث',
                                    'oldest' => 'الأقدم',
                                    'title_asc' => 'العنوان تصاعدي',
                                    'title_desc' => 'العنوان تنازلي',
                                    'author_asc' => 'المؤلف تصاعدي',
                                    'author_desc' => 'المؤلف تنازلي',
                                    'author_die_data_asc' => 'وفاة المؤلف تصاعدي',
                                    'author_die_data_desc' => 'وفاة المؤلف تنازلي',
                                ];
                            @endphp
                            <div tabindex="0"
                                class="h-10 btn btn-outline min-w-[200px] justify-between bg-none border-base-300 font-light">
                                {{ $sortLabels[$sort] ?? 'ترتيب حسب' }}
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50" aria-hidden="true">
                                    <path d="m7 15 5 5 5-5"></path>
                                    <path d="m7 9 5-5 5 5"></path>
                                </svg>
                            </div>
                            <ul tabindex="0"
                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                                <li><a value="newest" wire:click="$set('sort', 'newest')"
                                        class="sort-option {{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث
                                        @if ($sort == 'newest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="oldest" wire:click="$set('sort', 'oldest')"
                                        class="sort-option {{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم
                                        @if ($sort == 'oldest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_asc" wire:click="$set('sort', 'title_asc')"
                                        class="sort-option {{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تصاعدي @if ($sort == 'title_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_desc" wire:click="$set('sort', 'title_desc')"
                                        class="sort-option {{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تنازلي @if ($sort == 'title_desc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_asc" wire:click="$set('sort', 'author_asc')"
                                        class="sort-option {{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تصاعدي @if ($sort == 'author_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_desc" wire:click="$set('sort', 'author_desc')"
                                        class="sort-option {{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تنازلي @if ($sort == 'author_desc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_asc" wire:click="$set('sort', 'author_die_data_asc')"
                                        class="sort-option {{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تصاعدي @if ($sort == 'author_die_data_asc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_desc" wire:click="$set('sort', 'author_die_data_desc')"
                                        class="sort-option {{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تنازلي @if ($sort == 'author_die_data_desc')
                                        @endif
                                    </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-2 lg:hidden">
                <div class="relative flex-grow">
                        <div class="flex">
                            <input type="text" wire:model.live.debounce.500ms="query" placeholder="ابحث..."
                                class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                                autocomplete="off" id="search-input">

                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="$wire.isSearching">
                                    <div
                                        class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                                    </div>
                                </template>
                                <template x-if="!$wire.isSearching">
                                    <i class="fas fa-search text-gray-400"></i>
                                </template>
                            </div>
                        </div>


                    </div>
                    <div class="dropdown dropdown-end">
                        <button class="btn btn-outline flex justify-between items-center">
                        <i class="fa-solid fa-arrow-down-wide-short"></i>
                        </button>
                            <ul tabindex="0"
                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                                <li><a value="newest" wire:click="$set('sort', 'newest')"
                                        class="sort-option {{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث
                                        @if ($sort == 'newest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="oldest" wire:click="$set('sort', 'oldest')"
                                        class="sort-option {{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم
                                        @if ($sort == 'oldest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_asc" wire:click="$set('sort', 'title_asc')"
                                        class="sort-option {{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تصاعدي @if ($sort == 'title_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_desc" wire:click="$set('sort', 'title_desc')"
                                        class="sort-option {{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تنازلي @if ($sort == 'title_desc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_asc" wire:click="$set('sort', 'author_asc')"
                                        class="sort-option {{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تصاعدي @if ($sort == 'author_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_desc" wire:click="$set('sort', 'author_desc')"
                                        class="sort-option {{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تنازلي @if ($sort == 'author_desc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_asc" wire:click="$set('sort', 'author_die_data_asc')"
                                        class="sort-option {{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تصاعدي @if ($sort == 'author_die_data_asc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_desc" wire:click="$set('sort', 'author_die_data_desc')"
                                        class="sort-option {{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تنازلي @if ($sort == 'author_die_data_desc')
                                        @endif
                                    </a></li>
                            </ul>
                        </div>

                <button @click="mobileFiltersOpen = true"
                    class="btn btn-outline flex justify-between items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                    </svg>
                </button>

                </div>

            <!-- Search Results Display -->
            <div class="bg-base-100 mt-4 rounded-lg  p-4 border border-base-300">

                @if ($hasSearched && !empty($query))
                    <div class="mb-4 p-3 bg-background rounded-lg">
                        <p class="text-onsearch">
                            نتائج البحث عن: <span class="font-bold">{{ $query }}</span>
                            <span class="mx-2">|</span>
                            <span>عدد النتائج: {{ $resultsCount }}</span>
                            @if (!empty($categories) || !empty($authors) || !empty($yearFrom) || !empty($yearTo))
                                <span class="mx-2">|</span>
                                <span class="text-blue-600">مع فلاتر مطبقة</span>
                            @endif
                        </p>
                    </div>

                    <!-- Loading Indicator -->
                    <div x-show="$wire.isSearching" class="w-full py-4">
                        <div class="animate-pulse space-y-4">
                            <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div class="flex flex-wrap gap-2">
                                <div class="h-8 bg-gray-200 rounded-full w-24"></div>
                                <div class="h-8 bg-gray-200 rounded-full w-32"></div>
                                <div class="h-8 bg-gray-200 rounded-full w-20"></div>
                            </div>
                            <div class="h-4 bg-gray-200 rounded w-1/3 mt-4"></div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                                <div class="h-40 bg-gray-200 rounded-lg"></div>
                            </div>
                        </div>
                    </div>


                    @foreach ($autoCompleteResults as $result)
                        @include('livewire.search-result-item', ['query' => $query])
                    @endforeach


                    @if (!empty($query) && strlen($query) > 2 && count($autoCompleteResults) === 0)
                        <div class=" mt-1  ">
                            <div class="p-4 text-center text-gray-600">
                                <i class="fas fa-search-minus text-gray-400 text-2xl mb-2"></i>
                                <p>لا توجد نتائج للبحث عن "{{ $query }}"</p>
                                <p class="text-sm text-gray-500 mt-1">حاول استخدام كلمات مفتاحية أخرى</p>
                            </div>
                        </div>

                        <script>
                            document.addEventListener('livewire:initialized', function() {
                                // Handle Livewire updates to close mobile filters drawer if open
                                Livewire.hook('message.processed', (message, component) => {
                                    // Check if Alpine.js is available and the mobile filters drawer is open
                                    if (window.Alpine) {
                                        const searchComponent = document.querySelector('[x-data]');
                                        if (searchComponent && searchComponent.__x) {
                                            // Access the Alpine.js data
                                            const data = searchComponent.__x.getUnobservedData();
                                            // If a filter was changed and the drawer is open on mobile, close it
                                            if (data.mobileFiltersOpen && window.innerWidth < 1024) {
                                                // Only close on certain updates (like filter changes)
                                                if (message.updateQueue.some(update =>
                                                        update.payload.method === '$set' ||
                                                        update.payload.method === 'loadMoreBooks' ||
                                                        update.payload.method === 'loadMoreAuthors')) {
                                                    data.mobileFiltersOpen = false;
                                                }
                                            }
                                        }
                                    }
                                });
                            });
                        </script>
                    @endif

                    <!-- Custom Pagination -->
                    @php
                        $currentPage = $this->getCurrentPage();
                        $totalPages = $this->getTotalPages();
                        $pageNumbers = $this->getPageNumbers();
                    @endphp

                    @if ($resultsCount > 0 && $totalPages > 1)
                        <div class="mt-6 border-t border-base-300 pt-6">
                            <!-- Results Info -->
                            <div class="text-sm text-gray-600 text-center mb-4">
                                عرض {{ $offset + 1 }} - {{ min($offset + $limit, $resultsCount) }} من
                                {{ $resultsCount }} نتيجة
                                (صفحة {{ $currentPage }} من {{ $totalPages }})
                            </div>

                            <!-- Pagination Controls -->
                            <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                                <!-- Mobile: Simple Previous/Next -->
                                <div class="flex sm:hidden items-center gap-2 w-full justify-between">
                                    <button wire:click="previousPage"
                                        class="btn btn-sm btn-outline {{ $currentPage <= 1 ? 'btn-disabled' : '' }} flex-1"
                                        @if ($currentPage <= 1) disabled @endif>
                                        <i class="fas fa-chevron-right"></i>
                                        السابق
                                    </button>

                                    <span class="text-sm font-medium px-4">
                                        {{ $currentPage }} / {{ $totalPages }}
                                    </span>

                                    <button wire:click="nextPage" @if ($currentPage >= $totalPages) disabled @endif
                                        class="btn btn-sm btn-outline {{ $currentPage >= $totalPages ? 'btn-disabled' : '' }} flex-1">
                                        التالي
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                </div>

                                <!-- Desktop: Full Pagination -->
                                <div class="hidden sm:flex items-center gap-2">
                                    <!-- Previous Button -->
                                    <button wire:click="previousPage"
                                        class="btn btn-sm btn-outline {{ $currentPage <= 1 ? 'btn-disabled' : '' }}"
                                        @if ($currentPage <= 1) disabled @endif>
                                        <i class="fas fa-chevron-right"></i>
                                        السابق
                                    </button>

                                    <!-- Page Numbers -->
                                    <div class="flex items-center gap-1">
                                        @foreach ($pageNumbers as $page)
                                            @if ($page === '...')
                                                <span class="px-2 py-1 text-gray-500">...</span>
                                            @else
                                                <button wire:click="goToPage({{ $page }})"
                                                    class="btn btn-sm {{ $currentPage == $page ? 'btn-primary' : 'btn-outline' }}">
                                                    {{ $page }}
                                                </button>
                                            @endif
                                        @endforeach
                                    </div>

                                    <!-- Next Button -->
                                    <button wire:click="nextPage" @if ($currentPage >= $totalPages) disabled @endif
                                        class="btn btn-sm btn-outline {{ $currentPage >= $totalPages ? 'btn-disabled' : '' }}">
                                        التالي
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
            </div>
        </div>
    @else
        <!-- Recent Searches -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-3">عمليات البحث الأخيرة</h3>

            @if (count($recentSearches) > 0)
                <div class="flex flex-wrap gap-2">
                    @foreach ($recentSearches as $search)
                        <button wire:click="selectRecentSearch('{{ $search['search_term'] }}')"
                            class="btn btn-sm btn-outline">
                            {{ $search['search_term'] }}
                        </button>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-sm">لا توجد عمليات بحث سابقة</p>
            @endif
        </div>

        <!-- Popular Searches -->
        <div>
            <h3 class="text-lg font-semibold mb-3">عمليات البحث الشائعة</h3>

            @if (count($popularSearches) > 0)
                <div class="flex flex-wrap gap-2">
                    @foreach ($popularSearches as $search)
                        <button wire:click="selectRecentSearch('{{ $search['search_term'] }}')"
                            class="btn btn-sm btn-outline">
                            {{ $search['search_term'] }}
                        </button>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-sm">لا توجد عمليات بحث شائعة</p>
            @endif
        </div>
        @endif
    </div>
</div>
</div>
</div>
</div>
