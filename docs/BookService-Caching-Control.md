# BookService Caching Control

## Overview

The `BookService` class now supports configurable caching behavior with automatic debug mode detection. This enhancement allows you to control whether caching is enabled and automatically disables caching when the application is in debug mode.

## Features

### 1. Automatic Debug Mode Detection
- **Default Behavior**: Caching is automatically disabled when `APP_DEBUG=true`
- **Production Mode**: Caching is enabled when `APP_DEBUG=false`
- **Rationale**: In debug mode, you typically want fresh data for testing and development

### 2. Manual Caching Control
- **Force Enable**: You can explicitly enable caching even in debug mode
- **Force Disable**: You can explicitly disable caching even in production mode
- **Flexibility**: Useful for specific testing scenarios or performance debugging

### 3. Backward Compatibility
- **No Breaking Changes**: Existing code continues to work without modifications
- **Optional Parameter**: The caching control is implemented via an optional constructor parameter

## Usage Examples

### Default Behavior (Recommended)
```php
// Caching automatically controlled by debug mode
$bookService = new BookService();

// Check if caching is enabled
if ($bookService->isCachingEnabled()) {
    echo "Caching is enabled";
} else {
    echo "Caching is disabled (likely in debug mode)";
}
```

### Force Enable Caching
```php
// Enable caching regardless of debug mode
$bookService = new BookService(true);
```

### Force Disable Caching
```php
// Disable caching regardless of debug mode
$bookService = new BookService(false);
```

### Using Service Methods
```php
$bookService = new BookService();

// These methods work the same regardless of caching status
$latestBooks = $bookService->getLatestBooks(12);
$mostReadBooks = $bookService->getMostReadBooksThisMonth(10);
$selectedBooks = $bookService->getSelectedBooks(8);
$categories = $bookService->getMainCategoriesWithBookCounts();
```

## Implementation Details

### Constructor Parameters
- `$enableCaching` (bool|null): Controls caching behavior
  - `null` (default): Auto-detect based on debug mode
  - `true`: Force enable caching
  - `false`: Force disable caching

### Internal Logic
1. **Debug Mode Check**: Uses `Config::get('app.debug')` to detect debug mode
2. **Conditional Caching**: The private `cache()` method checks `$cachingEnabled` before caching
3. **Direct Execution**: When caching is disabled, callbacks are executed directly

### Cache Tags
When caching is enabled, the service uses the following cache tags:
- `['books', 'home']` for book-related queries
- `['categories', 'home']` for category queries

## Benefits

### Development Benefits
- **Fresh Data**: Always get current data during development
- **Debugging**: Easier to debug issues without cache interference
- **Testing**: Consistent test results without cache pollution

### Production Benefits
- **Performance**: Maintains caching benefits in production
- **Flexibility**: Can disable caching for specific debugging scenarios
- **Control**: Fine-grained control over caching behavior

## Migration Guide

### For Existing Code
No changes required! Existing instantiations of `BookService` will automatically:
- Disable caching in debug mode (`APP_DEBUG=true`)
- Enable caching in production mode (`APP_DEBUG=false`)

### For New Code
Consider the caching requirements:
```php
// For most cases (recommended)
$bookService = new BookService();

// For performance testing in debug mode
$bookService = new BookService(true);

// For debugging cache issues in production
$bookService = new BookService(false);
```

## Testing

The implementation includes comprehensive tests covering:
- Default debug mode behavior
- Default production mode behavior
- Explicit caching control
- Status checking functionality

Run tests with:
```bash
php artisan test tests/Unit/BookServiceCacheTest.php
```

## Configuration

The caching behavior is controlled by the `APP_DEBUG` environment variable in your `.env` file:

```env
# Debug mode - caching disabled by default
APP_DEBUG=true

# Production mode - caching enabled by default
APP_DEBUG=false
```
