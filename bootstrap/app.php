<?php

use App\Http\Middleware\AppMiddleware;
use App\Http\Middleware\LocaleMiddleware;
use App\Http\Middleware\CacheControlMiddleware;
use App\Http\Middleware\ResponseCompressionMiddleware;
use App\Http\Middleware\ImageOptimizationMiddleware;
use Modules\TrafficLogs\Middleware\TrafficLogsMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Log;
use App\Exceptions\InvalidOrderException;
use App\Http\Controllers\TelegramErrorBotController;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(LocaleMiddleware::class);
        $middleware->web(TrafficLogsMiddleware::class);
        $middleware->web(CacheControlMiddleware::class);
        $middleware->web(ResponseCompressionMiddleware::class);
        $middleware->web(ImageOptimizationMiddleware::class);
        $middleware->api(CacheControlMiddleware::class);
        $middleware->api(ResponseCompressionMiddleware::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        if(env('APP_ENV') != 'local') {
            $exceptions->reportable(function (Throwable $e) {
                    $telegramBot = new TelegramErrorBotController();
                    $telegramBot->sendError($e);
            });
            // $exceptions->renderable(function (Throwable $e, $request) {
            //     return response()->json(['error' => 'An error occurred'], 500);
            // });
        }
    })->create();
