<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cookie;
use Modules\Chat\app\Models\ChatMessage;
use Modules\Chat\app\Models\ChatAttachment;

class ChatController extends Controller
{
    /**
     * Display the chat room page
     */
    public function index(Request $request)
    {
        // Get the display name from cookie if available
        $displayName = $request->cookie('chat_display_name');
        
        return view('chat::index', compact('displayName'));
    }
    
    /**
     * Save user's display name in cookie
     */
    public function setDisplayName(Request $request)
    {
        $validated = $request->validate([
            'display_name' => 'required|string|max:50|min:3',
        ]);
        
        // Set cookie for 1 year
        $cookie = Cookie::make('chat_display_name', $validated['display_name'], 525600);
        
        return response()->json(['success' => true])->withCookie($cookie);
    }
    
    /**
     * Get chat messages
     */
    public function getMessages(Request $request)
    {
        $lastId = $request->input('last_id', 0);
        
        $messages = ChatMessage::with(['user', 'attachments'])
            ->when($lastId > 0, function($query) use ($lastId) {
                return $query->where('id', '>', $lastId);
            })
            ->latest()
            ->limit(50)
            ->get()
            ->reverse()
            ->values();
            
        return response()->json($messages);
    }
    
    /**
     * Store a new chat message
     */
    public function sendMessage(Request $request)
    {
        $validated = $request->validate([
            'message' => 'required_without:attachments|nullable|string|max:2000',
            'display_name' => 'required|string|max:50',
            'attachments.*' => 'nullable|file|max:5000|mimes:jpeg,png,gif,pdf,doc,docx,txt',
        ]);
        
        $message = ChatMessage::create([
            'user_id' => Auth::id(),
            'display_name' => $validated['display_name'],
            'message' => $request->input('message') ?? '',
            'ip_address' => $request->ip(),
        ]);
        
        // Handle file attachments
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('chat-attachments', 'public');
                
                ChatAttachment::create([
                    'message_id' => $message->id,
                    'file_path' => $path,
                    'file_name' => $file->getClientOriginalName(),
                    'file_type' => $file->getMimeType(),
                    'file_size' => $file->getSize() / 1024, // Convert bytes to KB
                ]);
            }
        }
        
        $message->load(['user', 'attachments']);
        
        return response()->json($message);
    }
    
    /**
     * Delete a chat message
     */
    public function deleteMessage(ChatMessage $message)
    {
        // Check if user owns this message
        if ($message->user_id != Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        
        // Delete attachments from storage
        foreach ($message->attachments as $attachment) {
            Storage::disk('public')->delete($attachment->file_path);
        }
        
        $message->delete();
        
        return response()->json(['success' => true]);
    }
} 