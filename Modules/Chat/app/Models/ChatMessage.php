<?php

namespace Modules\Chat\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;

class ChatMessage extends Model
{
    protected $fillable = [
        'user_id',
        'display_name',
        'message',
        'is_system_message',
        'ip_address',
    ];

    protected $casts = [
        'is_system_message' => 'boolean',
    ];

    /**
     * Get the user who sent the message
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the message's attachments
     */
    public function attachments(): Has<PERSON>any
    {
        return $this->hasMany(ChatAttachment::class, 'message_id');
    }
} 