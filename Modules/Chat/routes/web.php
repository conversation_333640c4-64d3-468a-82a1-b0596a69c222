<?php

use Illuminate\Support\Facades\Route;
use Modules\Chat\app\Http\Controllers\ChatController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('admin')->middleware(['web', 'auth'])->group(function () {
    Route::get('chat-room', [ChatController::class, 'index'])->name('chat.index');
    Route::post('chat-room/messages', [ChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::get('chat-room/messages', [ChatController::class, 'getMessages'])->name('chat.get-messages');
    Route::delete('chat-room/messages/{message}', [ChatController::class, 'deleteMessage'])->name('chat.delete-message');
    Route::post('chat-room/set-display-name', [ChatController::class, 'setDisplayName'])->name('chat.set-display-name');
}); 