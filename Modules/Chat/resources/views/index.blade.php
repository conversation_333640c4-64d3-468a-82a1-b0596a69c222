@extends('layouts/layoutMaster')

@section('title', 'غرفة المحادثة')

@section('page-style')
<style>
    #chat-container {
        height: 65vh;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        padding: 1rem;
    }
    
    .message-bubble {
        max-width: 80%;
        margin-bottom: 1rem;
        position: relative;
        border-radius: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .message-bubble.own-message {
        align-self: flex-end;
        background: linear-gradient(135deg, #696cff 0%, #8789ff 100%);
        color: white;
        border-bottom-right-radius: 0.25rem;
    }
    
    .message-bubble.other-message {
        align-self: flex-start;
        background: #f8f9fa;
        border-bottom-left-radius: 0.25rem;
    }
    
    .message-display-name {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
        opacity: 0.9;
    }
    
    .message-time {
        font-size: 0.7rem;
        opacity: 0.7;
        text-align: right;
        margin-top: 0.25rem;
    }
    
    .message-actions {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        opacity: 0;
        transition: opacity 0.2s;
    }
    
    .message-bubble:hover .message-actions {
        opacity: 1;
    }
    
    .message-attachments {
        margin-top: 0.5rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .attachment-item {
        display: flex;
        align-items: center;
        background: rgba(255,255,255,0.1);
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin-bottom: 0.25rem;
        transition: all 0.2s ease;
    }
    
    .attachment-item:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .attachment-icon {
        margin-right: 0.5rem;
    }
    
    .welcome-screen {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .message-input-container {
        position: relative;
        background: #fff;
        border-radius: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 1rem;
        margin-top: 1rem;
    }

    .message-input-wrapper {
        display: flex;
        align-items: flex-end;
        gap: 0.5rem;
    }

    .message-input-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .btn-attach {
        background: transparent;
        border: none;
        color: #696cff;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .btn-attach:hover {
        background: rgba(105,108,255,0.1);
    }

    .btn-send {
        background: #696cff;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .btn-send:hover {
        background: #5f62e6;
    }

    .delete-message {
        background: transparent;
        border: none;
        color: #ff3e1d;
        padding: 0.25rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .delete-message:hover {
        background: rgba(255,62,29,0.1);
    }

    .message-content {
        word-break: break-word;
        line-height: 1.5;
    }
</style>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">غرفة المحادثة</h5>
    </div>
    <div class="card-body p-0">
        <div id="welcome-screen" class="welcome-screen" style="{{ $displayName ? 'display: none;' : '' }}">
            <div class="text-center mb-4">
                <h4>مرحبًا بك في غرفة المحادثة!</h4>
                <p>يرجى إدخال اسمك المستعار للانضمام إلى المحادثة</p>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <form id="display-name-form">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label" for="display-name">الاسم المستعار</label>
                            <input type="text" class="form-control" id="display-name" placeholder="أدخل اسمك المستعار" minlength="3" maxlength="50" required>
                            <div class="invalid-feedback">يرجى إدخال اسم مستعار صالح (3-50 حرفًا)</div>
                        </div>
                        <button type="submit" class="btn btn-primary">دخول المحادثة</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div id="chat-room" style="{{ $displayName ? '' : 'display: none;' }}">
            <div id="chat-container" class="mb-3">
                <!-- Chat messages will appear here -->
                <div class="d-flex justify-content-center mb-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
            
            <div class="message-input-container">
                <form id="message-form">
                    <div id="attachments-preview" class="mb-2" style="display: none;">
                        <div id="attachment-list" class="mb-2"></div>
                    </div>
                    
                    <div class="message-input-wrapper">
                        <textarea class="form-control border-0" id="message-input" rows="1" placeholder="اكتب رسالتك هنا..." maxlength="2000"></textarea>
                        <div class="message-input-actions">
                            <input type="file" id="file-input" class="d-none" multiple accept="image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain">
                            <button type="button" id="attachment-btn" class="btn-attach">
                                <i class="ti ti-paperclip"></i>
                            </button>
                            <button type="submit" class="btn-send">
                                <i class="ti ti-send"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-muted" id="character-counter">0/2000</small>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const welcomeScreen = document.getElementById('welcome-screen');
    const chatRoom = document.getElementById('chat-room');
    const chatContainer = document.getElementById('chat-container');
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const characterCounter = document.getElementById('character-counter');
    const displayNameForm = document.getElementById('display-name-form');
    const displayNameInput = document.getElementById('display-name');
    const attachmentBtn = document.getElementById('attachment-btn');
    const fileInput = document.getElementById('file-input');
    const attachmentsPreview = document.getElementById('attachments-preview');
    const attachmentList = document.getElementById('attachment-list');
    
    let displayName = '{{ $displayName ?? "" }}';
    let lastMessageId = 0;
    let isPolling = false;
    let selectedFiles = [];
    let processedMessageIds = new Set();
    
    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Display name form submission
    if (displayNameForm) {
        displayNameForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!displayNameInput.value || displayNameInput.value.length < 3) {
                displayNameInput.classList.add('is-invalid');
                return;
            }
            
            displayName = displayNameInput.value;
            
            fetch('{{ route("chat.set-display-name") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    display_name: displayName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    welcomeScreen.style.display = 'none';
                    chatRoom.style.display = 'block';
                    fetchMessages();
                    startPolling();
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    }
    
    // Character counter for message input
    messageInput.addEventListener('input', function() {
        const length = this.value.length;
        characterCounter.textContent = `${length}/2000`;
        
        if (length > 2000) {
            characterCounter.classList.add('text-danger');
            this.value = this.value.substring(0, 2000);
        } else {
            characterCounter.classList.remove('text-danger');
        }
    });
    
    // Attachment button click
    attachmentBtn.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function() {
        selectedFiles = Array.from(this.files);
        updateAttachmentPreview();
    });
    
    // Update attachment preview
    function updateAttachmentPreview() {
        attachmentList.innerHTML = '';
        
        if (selectedFiles.length > 0) {
            attachmentsPreview.style.display = 'block';
            
            selectedFiles.forEach((file, index) => {
                const fileExt = file.name.split('.').pop().toLowerCase();
                let iconClass = 'ti-file';
                
                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                    iconClass = 'ti-photo';
                } else if (['pdf'].includes(fileExt)) {
                    iconClass = 'ti-file-text';
                } else if (['doc', 'docx'].includes(fileExt)) {
                    iconClass = 'ti-file-description';
                }
                
                const fileItem = document.createElement('div');
                fileItem.className = 'attachment-item';
                fileItem.innerHTML = `
                    <i class="ti ${iconClass} attachment-icon"></i>
                    <span>${file.name}</span>
                    <button type="button" class="btn btn-sm btn-text-secondary ms-auto remove-attachment" data-index="${index}">
                        <i class="ti ti-x"></i>
                    </button>
                `;
                
                attachmentList.appendChild(fileItem);
                
                fileItem.querySelector('.remove-attachment').addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    selectedFiles.splice(index, 1);
                    updateAttachmentPreview();
                });
            });
        } else {
            attachmentsPreview.style.display = 'none';
        }
    }
    
    // Message form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = messageInput.value.trim();
        
        // Don't submit if there's no message and no attachments
        if (!message && selectedFiles.length === 0) {
            return;
        }
        
        const formData = new FormData();
        formData.append('display_name', displayName);
        formData.append('message', message);
        
        // Append files if any
        selectedFiles.forEach(file => {
            formData.append('attachments[]', file);
        });
        
        fetch('{{ route("chat.send-message") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            addMessage(data);
            messageInput.value = '';
            messageInput.style.height = 'auto';
            characterCounter.textContent = '0/2000';
            selectedFiles = [];
            fileInput.value = '';
            updateAttachmentPreview();
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });
    
    // Fetch messages function
    function fetchMessages() {
        fetch(`{{ route('chat.get-messages') }}?last_id=${lastMessageId}`)
            .then(response => response.json())
            .then(data => {
                if (data.length > 0) {
                    // Clear loading indicator if it's the first fetch
                    if (lastMessageId === 0) {
                        chatContainer.innerHTML = '';
                    }
                    
                    // Add all messages
                    data.forEach(message => {
                        if (!processedMessageIds.has(message.id)) {
                            addMessage(message);
                            processedMessageIds.add(message.id);
                        }
                    });
                    
                    // Update the last message ID
                    lastMessageId = data[data.length - 1].id;
                    
                    // Scroll to bottom
                    scrollToBottom();
                } else if (lastMessageId === 0) {
                    // No messages and first load
                    chatContainer.innerHTML = '<div class="text-center p-5 text-muted">لا توجد رسائل بعد. كن أول من يبدأ المحادثة!</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                chatContainer.innerHTML = '<div class="text-center p-5 text-danger">حدث خطأ أثناء تحميل الرسائل. يرجى المحاولة مرة أخرى.</div>';
            });
    }
    
    // Scroll to bottom function
    function scrollToBottom() {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // Start polling for new messages
    function startPolling() {
        if (!isPolling) {
            isPolling = true;
            setInterval(fetchMessages, 60000); // Poll every 60 seconds
        }
    }
    
    // Add message to the chat container
    function addMessage(message) {
        const currentUserId = 1;
        const isOwnMessage = message.user_id == currentUserId;
        const messageDate = new Date(message.created_at);
        const formattedTime = messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        const messageElement = document.createElement('div');
        messageElement.className = `message-bubble ${isOwnMessage ? 'own-message' : 'other-message'}`;
        messageElement.id = `message-${message.id}`;
        messageElement.innerHTML = `
            <div class="p-3">
                <div class="message-display-name">${message.display_name}</div>
                <div class="message-content">${message.message}</div>
                
                ${message.attachments && message.attachments.length > 0 ? `
                <div class="message-attachments">
                    ${message.attachments.map(attachment => {
                        const fileExt = attachment.file_name.split('.').pop().toLowerCase();
                        const isImage = ['jpg', 'jpeg', 'png', 'gif'].includes(fileExt);
                        
                        return isImage ? `
                            <div class="attachment-image mb-2">
                                <a href="/storage/${attachment.file_path}" target="_blank">
                                    <img src="/storage/${attachment.file_path}" class="img-fluid rounded" style="max-height: 150px;">
                                </a>
                            </div>
                        ` : `
                            <div class="attachment-file">
                                <a href="/storage/${attachment.file_path}" target="_blank" class="attachment-item">
                                    <i class="ti ${getFileIcon(fileExt)} attachment-icon"></i>
                                    <span>${attachment.file_name}</span>
                                    <span class="ms-auto text-muted small">${formatFileSize(attachment.file_size)}</span>
                                </a>
                            </div>
                        `;
                    }).join('')}
                </div>
                ` : ''}
                
                <div class="message-time">${formattedTime}</div>
                
                ${isOwnMessage ? `
                <div class="message-actions">
                    <button type="button" class="delete-message" data-id="${message.id}">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
                ` : ''}
            </div>
        `;
        
        chatContainer.appendChild(messageElement);
        
        // Add event listener to delete button
        if (isOwnMessage) {
            const deleteBtn = messageElement.querySelector('.delete-message');
            deleteBtn.addEventListener('click', function() {
                const messageId = this.getAttribute('data-id');
                deleteMessage(messageId);
            });
        }
        
        // Scroll to the bottom
        scrollToBottom();
    }
    
    // Delete message function
    function deleteMessage(messageId) {
        if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
            fetch(`{{ url('admin/chat-room/messages') }}/${messageId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const messageElement = document.getElementById(`message-${messageId}`);
                    if (messageElement) {
                        messageElement.remove();
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    }
    
    // Helper function to get icon class based on file extension
    function getFileIcon(fileExt) {
        switch(fileExt) {
            case 'pdf':
                return 'ti-file-text';
            case 'doc':
            case 'docx':
                return 'ti-file-description';
            case 'txt':
                return 'ti-file-dots';
            default:
                return 'ti-file';
        }
    }
    
    // Helper function to format file size
    function formatFileSize(sizeInKB) {
        if (sizeInKB < 1024) {
            return Math.round(sizeInKB) + ' KB';
        } else {
            return (sizeInKB / 1024).toFixed(2) + ' MB';
        }
    }
    
    // If display name is already set, show chat room and start polling
    if (displayName) {
        fetchMessages();
        startPolling();
        
        // Add scroll to bottom on window load
        window.addEventListener('load', function() {
            scrollToBottom();
        });
    }
});
</script>
@endsection 