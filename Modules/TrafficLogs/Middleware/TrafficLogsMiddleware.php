<?php

namespace Modules\TrafficLogs\Middleware;

use App\Helpers\SecurityHelper;
use App\Helpers\UserSystemInfoHelper;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Modules\TrafficLogs\Models\TrafficLogs;
use Modules\TrafficLogs\Models\TrafficLogsDetails;
use Modules\TrafficLogs\Models\UnderAttack;
use Modules\TrafficLogs\Jobs\UpdateTrafficCountryJob;

class TrafficLogsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * This middleware logs traffic details, monitors for potential DDoS attacks,
     * and blocks IPs that exceed request limits.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Update the last activity timestamp for authenticated users
        $this->updateUserActivity();

        // Get the IP address of the user
        $ip = UserSystemInfoHelper::get_ip();

        // Check for DDoS attacks and IP limits
        $this->checkForAttacks($ip);

        // Log the traffic
        $this->logTraffic($ip, $request);

        // Proceed to the next middleware or request handler
        return $next($request);
    }

    /**
     * Update the last activity timestamp for authenticated users
     */
    private function updateUserActivity(): void
    {
        if (auth()->check()) {
            User::where('id', auth()->user()->id)->update(['last_activity' => now()]);
        }
    }

    /**
     * Check for DDoS attacks and IP request limits
     */
    private function checkForAttacks(string $ip): void
    {
        $oneMinuteAgo = Carbon::parse(now())->subMinutes(1)->format('Y-m-d H:i:s');

        // Use cache to reduce DB queries for counting total requests
        $totalRequestsKey = 'total_requests_last_minute';
        $totalRequests = Cache::remember($totalRequestsKey, 60, function () use ($oneMinuteAgo) {
            return TrafficLogsDetails::where('created_at', '>=', $oneMinuteAgo)
                ->count();
        });

        // Check if system is under attack (more than 2000 requests per minute)
        if ($totalRequests >= 2000) {
            $this->handlePotentialAttack();
        }

        // Check if current IP should be blocked (more than 100 requests per minute)
        $ipRequestsKey = 'ip_requests_' . md5($ip);
        $ipRequests = Cache::remember($ipRequestsKey, 60, function () use ($ip, $oneMinuteAgo) {
            return TrafficLogsDetails::where('ip', $ip)
                ->where('created_at', '>=', $oneMinuteAgo)
                ->count();
        });

        if ($ipRequests >= 100) {
            (new SecurityHelper())->block_ip($ip, request()->header('User-Agent'));
            abort(403);
        }
    }

    /**
     * Handle potential DDoS attack
     */
    private function handlePotentialAttack(): void
    {
        $attackExists = UnderAttack::where('status', 'UNDER_ATTACK')
            ->where('release_at', '>', now())
            ->exists();

        if (!$attackExists) {
            UnderAttack::create([
                'status' => 'UNDER_ATTACK', 
                'release_at' => Carbon::now()->addMinutes(30)
            ]);
            (new SecurityHelper())->enable_under_attack_mode();
        }
    }

    /**
     * Log the traffic details
     */
    private function logTraffic(string $ip, Request $request): void
    {


        //ignore traffic logs for the following prefix admin && route livewire
        if (strpos($request->fullUrl(), 'admin') !== false) {
            return;
        }
        if (strpos($request->fullUrl(), 'livewire') !== false) {
            return;
        }
        if (strpos($request->fullUrl(), 'telescope') !== false) {
            return;
        }

        if (strpos($request->fullUrl(), 'log-viewer') !== false) {
            return;
        }




        $userId = auth()->check() ? auth()->user()->id : null;
        
        // Check for recent traffic log within the last hour
        $lastInsert = TrafficLogs::where('ip', $ip)
            ->where('user_id', $userId)
            ->where('created_at', '>=', Carbon::now()->subMinutes(60*12))
            ->first();

            $fullUrl = $request->fullUrl();
            $agentName = $request->header('User-Agent');
            if (strlen($agentName) > 200) {
                $agentName = substr($agentName, 0, 200);
            }
            
            if (!$lastInsert) {
            // Create a new traffic log
            $traffic = TrafficLogs::create([
                // 'traffic_landing' => $request->fullUrl(),
                'ip' => $ip,
                'country' => null, // Will be updated by job
                'agent_name' => $agentName,
                'user_id' => $userId,
                'browser' => UserSystemInfoHelper::get_browsers(),
                'device' => UserSystemInfoHelper::get_device(),
                'operating_system' => UserSystemInfoHelper::get_os(),
            ]);

            // Queue country update job
            UpdateTrafficCountryJob::dispatch($traffic->id, $ip);

            // Get previous URL if valid
            $prevUrl = $this->getPreviousUrl();
            //*check if url bigger than varchar(255) if yes cut it
            if (strlen($prevUrl) > 255) {
                $prevUrl = substr($prevUrl, 0, 250);
            }
            if (strlen($fullUrl) > 255) {
                $fullUrl = substr($fullUrl, 0, 250);
            }
            // Create traffic details
            TrafficLogsDetails::create([
                'url' => $fullUrl,
                'prev_link' => $prevUrl,
                'traffic_logs_id' => $traffic->id,
                'ip' => $ip,
            ]);
        } else {
            if (strlen($fullUrl) > 255) {
                $fullUrl = substr($fullUrl, 0, 250);
            }
            $prevUrl = $this->getPreviousUrl();
            if (strlen($prevUrl) > 255) {
                $prevUrl = substr($prevUrl, 0, 250);
            }

            //*check if prevUrl from ziydia web
            if (strpos($prevUrl, 'ziydia.com') !== false) {
                $prevUrl = null;
            }
            // Only create traffic details for existing log
            TrafficLogsDetails::create([
                'url' => $fullUrl,
                'traffic_logs_id' => $lastInsert->id,
                'ip' => $ip,
                'prev_link' => $prevUrl,
            ]);
        }
    }

    /**
     * Get validated previous URL
     */
    private function getPreviousUrl(): ?string
    {
        $prevUrl = url()->previous();
    
        // Ensure it's a valid URL before returning
        return filter_var($prevUrl, FILTER_VALIDATE_URL) ? $prevUrl : null;
    }
}