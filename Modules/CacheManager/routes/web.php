<?php

use Illuminate\Support\Facades\Route;
use Modules\CacheManager\app\Http\Controllers\CacheManagerController;

Route::middleware(['web', 'auth'])->group(function () {
    Route::prefix('admin')->group(function () {
        Route::prefix('cache-manager')->group(function () {
            Route::get('/', [CacheManagerController::class, 'index'])->name('cache-manager.index');
            
            // Allow both DELETE and POST for better form compatibility
            Route::match(['delete', 'post'], '/delete', [CacheManagerController::class, 'delete'])->name('cache-manager.delete');
            Route::match(['delete', 'post'], '/clear', [CacheManagerController::class, 'clear'])->name('cache-manager.clear');
        });
    });
}); 