<?php

namespace Modules\CacheManager\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class CacheManagerController extends Controller
{
    protected $cacheDriver;

    public function __construct()
    {
        $this->cacheDriver = config('cache.default');
    }

    public function index(Request $request)
    {

        try {
            $search = $request->input('search', '');
            $page = max(1, intval($request->input('page', 1)));
            $perPage = config('cache-manager.per_page', 10);

            $keys = $this->getCacheKeys($search);
            $totalKeys = count($keys);

            // Ensure we don't go beyond the available pages
            if ($totalKeys > 0 && ($page - 1) * $perPage >= $totalKeys) {
                $page = ceil($totalKeys / $perPage);
            }

            $paginatedKeys = array_slice($keys, ($page - 1) * $perPage, $perPage);
            $cacheItems = [];

            foreach ($paginatedKeys as $key) {
                try {
                    $value = $this->getCacheValue($key);
                    $cacheItems[] = [
                        'key' => $key,
                        'value' => $value,
                        'type' => is_null($value) ? 'null' : gettype($value),
                        'ttl' => $this->getCacheTTL($key),
                    ];
                } catch (\Exception $e) {
                    Log::error('Error retrieving cache value for key: ' . $key . ' - ' . $e->getMessage());
                    $cacheItems[] = [
                        'key' => $key,
                        'value' => 'Error retrieving value',
                        'type' => 'error',
                        'ttl' => null,
                    ];
                }
            }

            return view('cache-manager::index', [
                'cacheItems' => $cacheItems,
                'total' => $totalKeys,
                'perPage' => $perPage,
                'currentPage' => $page,
                'lastPage' => max(1, ceil($totalKeys / $perPage)),
                'search' => $search
            ]);
        } catch (\Exception $e) {
            Log::error('Error in cache manager index: ' . $e->getMessage());
            return view('cache-manager::index', [
                'cacheItems' => [],
                'total' => 0,
                'perPage' => config('cache-manager.per_page', 10),
                'currentPage' => 1,
                'lastPage' => 1,
                'search' => $search,
                'error' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        try {
            $key = $request->input('key');

            if (empty($key)) {
                return redirect()->back()->with('error', 'Cache key is required');
            }

            $this->deleteCacheKey($key);
            return redirect()->back()->with('success', 'Cache key deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting cache key: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting cache key: ' . $e->getMessage());
        }
    }

    public function clear()
    {
        try {
            if ($this->cacheDriver === 'redis') {
                // For Redis, we need to clear only application keys
                $prefix = config('cache.prefix', '');
                $pattern = $prefix ? $prefix . ':*' : '*';
                $this->clearRedisCache($pattern);
            } else {
                // For other drivers, use the Laravel Cache facade
                Cache::flush();
            }
            return redirect()->back()->with('success', 'Cache cleared successfully');
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error clearing cache: ' . $e->getMessage());
        }
    }

    protected function clearRedisCache($pattern)
    {
        $iterator = null;
        do {
            $keys = Redis::scan($iterator, ['MATCH' => $pattern, 'COUNT' => 100]);
            if (!empty($keys)) {
                foreach ($keys as $key) {
                    Redis::del($key);
                }
            }
        } while ($iterator > 0);
    }

    protected function getCacheKeys($search = '')
    {
        if ($this->cacheDriver === 'redis') {
            return $this->getRedisKeys($search);
        }

        return $this->getLaravelCacheKeys($search);
    }

    protected function getRedisKeys($search)
    {
        $keys = [];
        $iterator = null;
        $viewData = Cache::get('book_page_XL6VqbPe_6_1');
        $keys = Redis::keys('Ziydia_cache:*');
        try {
            $pattern = empty($search) ? '*' : '*' . $search . '*';

            do {
                // Use scan with proper parameters format
                $scanKeys = Redis::scan($iterator, ['MATCH' => $pattern, 'COUNT' => 100]);
                if (!empty($scanKeys)) {
                    $keys = array_merge($keys, $scanKeys);
                }
            } while ($iterator > 0);

            // Filter out duplicates and convert to array if needed
            if (!empty($keys)) {
                $fKeys = [];
                foreach ($keys as $key) {
                if (!in_array($key, $fKeys)) {
                        //*check if key is array and has value and value is not empty or zero
                        if (is_array($key) && count($key) > 0) {
                            foreach ($key as $k) {
                                $fKeys[] = $k;
                            }
                        }
                    }
                }
            }
            return $fKeys;
        } catch (\Exception $e) {
            dd($e);
            Log::error('Error getting Redis keys: ' . $e->getMessage());
            return [];
        }
    }

    protected function getLaravelCacheKeys($search)
    {
        $keys = [];

        try {
            // For file cache, we need to scan the cache directory
            // Check the correct cache path based on Laravel version
            $cachePaths = [
                storage_path('framework/cache/data'),  // Laravel 5.8+
                storage_path('framework/cache'),       // Older versions
            ];

            foreach ($cachePaths as $cachePath) {
                if (is_dir($cachePath)) {
                    $this->scanDirectory($cachePath, $keys, $search);
                }
            }

            return $keys;
        } catch (\Exception $e) {
            Log::error('Error getting Laravel cache keys: ' . $e->getMessage());
            return [];
        }
    }

    protected function scanDirectory($directory, &$keys, $search)
    {
        $files = glob($directory . '/*');

        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->scanDirectory($file, $keys, $search);
            } else {
                $key = pathinfo($file, PATHINFO_FILENAME);
                // Remove Laravel cache hash if present
                if (strpos($key, '.') !== false) {
                    $key = substr($key, 0, strpos($key, '.'));
                }

                if (empty($search) || stripos($key, $search) !== false) {
                    $keys[] = $key;
                }
            }
        }
    }

    protected function getCacheValue($key)
    {
        try {
            $value = Cache::get($key);

            // Handle serialized values
            if (is_string($value) && $this->looksLikeSerialized($value)) {
                $unserialized = @unserialize($value);
                if ($unserialized !== false) {
                    return $unserialized;
                }
            }

            return $value;
        } catch (\Exception $e) {
            Log::error('Error getting cache value for key ' . $key . ': ' . $e->getMessage());
            return null;
        }
    }

    protected function looksLikeSerialized($value)
    {
        return is_string($value) &&
            substr($value, 0, 2) === 'a:' ||
            substr($value, 0, 2) === 'O:' ||
            substr($value, 0, 2) === 's:' ||
            substr($value, 0, 2) === 'i:' ||
            substr($value, 0, 2) === 'd:' ||
            substr($value, 0, 2) === 'b:' ||
            substr($value, 0, 2) === 'N;';
    }

    protected function getCacheTTL($key)
    {
        try {
            if ($this->cacheDriver === 'redis') {
                //* check if key is string or array
                if (is_string($key)) {
                    $ttl = Redis::ttl($key);
                    return $ttl > 0 ? $ttl : null;
                } else {
                    return null;
                }
            }

            // For file cache, TTL is not directly available
            return null;
        } catch (\Exception $e) {
            Log::error('Error getting TTL for key ' . $key . ': ' . $e->getMessage());
            return null;
        }
    }

    protected function deleteCacheKey($key)
    {
        try {
            return Cache::forget($key);
        } catch (\Exception $e) {
            Log::error('Error deleting cache key ' . $key . ': ' . $e->getMessage());
            throw $e;
        }
    }
}
