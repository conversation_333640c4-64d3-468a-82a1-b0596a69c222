<?php

namespace Modules\CacheManager\app\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class CacheManagerServiceProvider extends ServiceProvider
{
    protected $moduleName = 'CacheManager';

    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'database/migrations'));
    }

    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    protected function registerConfig()
    {
        $this->publishes([
            module_path($this->moduleName, 'config/config.php') => config_path('cache-manager.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'config/config.php'), 'cache-manager'
        );
    }

    protected function registerViews()
    {
        $viewPath = resource_path('views/modules/cache-manager');

        $sourcePath = module_path($this->moduleName, 'resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleName . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), 'cache-manager');
    }

    protected function registerTranslations()
    {
        $langPath = resource_path('lang/modules/cache-manager');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'cache-manager');
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'resources/lang'), 'cache-manager');
        }
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/cache-manager')) {
                $paths[] = $path . '/modules/cache-manager';
            }
        }
        return $paths;
    }
} 