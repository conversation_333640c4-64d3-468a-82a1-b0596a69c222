@extends('layouts/layoutMaster')

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Cache Manager</h1>
        <form action="{{ route('cache-manager.clear') }}" method="POST" class="d-inline">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear all cache? This action cannot be undone.')">
                <i class="bi bi-trash"></i> Clear All Cache
            </button>
        </form>
    </div>

    <div class="card">
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if(isset($error))
                <div class="alert alert-danger">
                    {{ $error }}
                </div>
            @endif

            <form action="{{ route('cache-manager.index') }}" method="GET" class="mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="Search cache keys..." value="{{ $search }}">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Key</th>
                            <th>Value</th>
                            <th>Type</th>
                            <th>TTL</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cacheItems as $item)
                            <tr>
                                <td>{{ $item['key'] }}</td>
                                <td>
                                    @if($item['type'] === 'error')
                                        <span class="text-danger">{{ $item['value'] }}</span>
                                    @else
                                        <pre class="mb-0"><code>{{ json_encode($item['value'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PARTIAL_OUTPUT_ON_ERROR) }}</code></pre>
                                    @endif
                                </td>
                                <td>{{ $item['type'] }}</td>
                                <td>{{ $item['ttl'] !== null ? $item['ttl'] . ' seconds' : 'N/A' }}</td>
                                <td>
                                    <div class="btn-group">
                                        <form action="{{ route('cache-manager.delete') }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <input type="hidden" name="key" value="{{ $item['key'] }}">
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this cache key?')">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">No cache keys found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            @if($total > 0)
                <div class="d-flex justify-content-center mt-4">
                    <ul class="pagination">
                        @if($currentPage > 1)
                            <li class="page-item">
                                <a class="page-link" href="{{ route('cache-manager.index', ['page' => $currentPage - 1, 'search' => $search]) }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        @endif

                        @for($i = 1; $i <= $lastPage; $i++)
                            @if($i == 1 || $i == $lastPage || ($i >= $currentPage - 2 && $i <= $currentPage + 2))
                                <li class="page-item {{ $i == $currentPage ? 'active' : '' }}">
                                    <a class="page-link" href="{{ route('cache-manager.index', ['page' => $i, 'search' => $search]) }}">{{ $i }}</a>
                                </li>
                            @elseif($i == $currentPage - 3 || $i == $currentPage + 3)
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            @endif
                        @endfor

                        @if($currentPage < $lastPage)
                            <li class="page-item">
                                <a class="page-link" href="{{ route('cache-manager.index', ['page' => $currentPage + 1, 'search' => $search]) }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        @endif
                    </ul>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
    pre {
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 0.25rem;
        max-height: 200px;
        overflow-y: auto;
    }
    code {
        white-space: pre-wrap;
        word-break: break-word;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
    .btn-group {
        display: inline-flex;
    }
</style>
@endpush
@endsection 