const t=["rtl","style","headerType","contentLayout","layoutCollapsed","showDropdownOnHover","layoutNavbarOptions","layoutFooterFixed","themes"],e=["light","dark","system"],s=["sticky","static","hidden"];let i;const n=document.documentElement.classList;i=n.contains("layout-navbar-fixed")?"sticky":n.contains("layout-navbar-hidden")?"hidden":"static";const o=document.getElementsByTagName("HTML")[0].getAttribute("data-theme")||0,a=n.contains("dark-style")?"dark":"light",l="rtl"===document.documentElement.getAttribute("dir"),r=!!n.contains("layout-menu-collapsed"),h=i,c=n.contains("layout-wide")?"wide":"compact",d=!!n.contains("layout-footer-fixed");let u;u=n.contains("layout-menu-offcanvas")?"static-offcanvas":n.contains("layout-menu-fixed")?"fixed":n.contains("layout-menu-fixed-offcanvas")?"fixed-offcanvas":"static";const m=u;class p{constructor({cssPath:i,themesPath:n,cssFilenamePattern:u,displayCustomizer:g,controls:y,defaultTextDir:_,defaultHeaderType:v,defaultContentLayout:b,defaultMenuCollapsed:f,defaultShowDropdownOnHover:S,defaultNavbarType:C,defaultFooterFixed:w,styles:x,navbarOptions:k,defaultStyle:L,availableContentLayouts:T,availableDirections:N,availableStyles:z,availableThemes:O,availableLayouts:E,availableHeaderTypes:H,availableNavbarOptions:A,defaultTheme:F,pathResolver:$,onSettingsChange:q,lang:R}){if(!this._ssr){if(!window.Helpers)throw new Error("window.Helpers required.");if(this.settings={},this.settings.cssPath=i,this.settings.themesPath=n,this.settings.cssFilenamePattern=u||"%name%.scss",this.settings.displayCustomizer=void 0===g||g,this.settings.controls=y||t,this.settings.defaultTextDir="rtl"===_||l,this.settings.defaultHeaderType=v||m,this.settings.defaultMenuCollapsed=void 0!==f?f:r,this.settings.defaultContentLayout=void 0!==b?b:c,this.settings.defaultShowDropdownOnHover=void 0===S||S,this.settings.defaultNavbarType=void 0!==C?C:h,this.settings.defaultFooterFixed=void 0!==w?w:d,this.settings.availableDirections=N||p.DIRECTIONS,this.settings.availableStyles=z||p.STYLES,this.settings.availableThemes=O||p.THEMES,this.settings.availableHeaderTypes=H||p.HEADER_TYPES,this.settings.availableContentLayouts=T||p.CONTENT,this.settings.availableLayouts=E||p.LAYOUTS,this.settings.availableNavbarOptions=A||p.NAVBAR_OPTIONS,this.settings.defaultTheme=this._getDefaultTheme(void 0!==F?F:o),this.settings.styles=x||e,this.settings.navbarOptions=k||s,this.settings.defaultStyle=L||a,this.settings.lang=R||"en",this.pathResolver=$||(t=>t),this.settings.styles.length<2){const t=this.settings.controls.indexOf("style");-1!==t&&(this.settings.controls=this.settings.controls.slice(0,t).concat(this.settings.controls.slice(t+1)))}this.settings.onSettingsChange="function"==typeof q?q:()=>{},this._loadSettings(),this._listeners=[],this._controls={},this._initDirection(),this._initStyle(),this._initTheme(),this.setLayoutType(this.settings.headerType,!1),this.setContentLayout(this.settings.contentLayout,!1),this.setDropdownOnHover(this.settings.showDropdownOnHover,!1),this.setLayoutNavbarOption(this.settings.layoutNavbarOptions,!1),this.setLayoutFooterFixed(this.settings.layoutFooterFixed,!1),this._setup()}}setRtl(t){this._hasControls("rtl")&&(this._setSetting("Rtl",String(t)),this._setCookie("direction",t,365),window.location.reload())}setContentLayout(t,e=!0){this._hasControls("contentLayout")&&(this.settings.contentLayout=t,e&&this._setSetting("contentLayout",t),window.Helpers.setContentLayout(t),e&&this.settings.onSettingsChange.call(this,this.settings))}setStyle(t){const e=!this._getLayoutName().includes("front");this._setSetting("Style",t);const s=e?"admin-mode":"front-mode",i=e?"admin-colorPref":"front-colorPref";""!==t&&this._checkCookie(s)?"system"===t?(this._setCookie(s,"system",365),window.matchMedia("(prefers-color-scheme: dark)").matches?this._setCookie(i,"dark",365):this._setCookie(i,"light",365)):"dark"===t?(this._setCookie(s,"dark",365),this._setCookie(i,"dark",365)):(this._setCookie(s,"light",365),this._setCookie(i,"light",365)):this._setCookie(s,t||"light",365),window.location.reload()}setTheme(t,e=!0,s=null){if(!this._hasControls("themes"))return;const i=this._getThemeByName(t);if(!i)return;this.settings.theme=i,e&&this._setSetting("Theme",t),this._setCookie("theme",t,365);const n=this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t+("light"!==this.settings.style?`-${this.settings.style}`:"")));this._loadStylesheets({[n]:document.querySelector(".template-customizer-theme-css")},s||(()=>{})),e&&this.settings.onSettingsChange.call(this,this.settings)}setLayoutType(t,e=!0){if(!this._hasControls("headerType"))return;if("static"!==t&&"static-offcanvas"!==t&&"fixed"!==t&&"fixed-offcanvas"!==t)return;this.settings.headerType=t,e&&this._setSetting("LayoutType",t),window.Helpers.setPosition("fixed"===t||"fixed-offcanvas"===t,"static-offcanvas"===t||"fixed-offcanvas"===t),e&&this.settings.onSettingsChange.call(this,this.settings);let s=window.Helpers.menuPsScroll;const i=window.PerfectScrollbar;"fixed"===this.settings.headerType||"fixed-offcanvas"===this.settings.headerType?i&&s&&(window.Helpers.menuPsScroll.destroy(),s=new i(document.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!1}),window.Helpers.menuPsScroll=s):s&&window.Helpers.menuPsScroll.destroy()}setDropdownOnHover(t,e=!0){if(this._hasControls("showDropdownOnHover")){if(this.settings.showDropdownOnHover=t,e&&this._setSetting("ShowDropdownOnHover",t),window.Helpers.mainMenu){window.Helpers.mainMenu.destroy(),config.showDropdownOnHover=t;const{Menu:e}=window;window.Helpers.mainMenu=new e(document.getElementById("layout-menu"),{orientation:"horizontal",closeChildren:!0,showDropdownOnHover:config.showDropdownOnHover})}e&&this.settings.onSettingsChange.call(this,this.settings)}}setLayoutNavbarOption(t,e=!0){this._hasControls("layoutNavbarOptions")&&(this.settings.layoutNavbarOptions=t,e&&this._setSetting("FixedNavbarOption",t),window.Helpers.setNavbar(t),e&&this.settings.onSettingsChange.call(this,this.settings))}setLayoutFooterFixed(t,e=!0){this.settings.layoutFooterFixed=t,e&&this._setSetting("FixedFooter",t),window.Helpers.setFooterFixed(t),e&&this.settings.onSettingsChange.call(this,this.settings)}setLang(t,e=!0,s=!1){if(t===this.settings.lang&&!s)return;if(!p.LANGUAGES[t])throw new Error(`Language "${t}" not found!`);const i=p.LANGUAGES[t];["panel_header","panel_sub_header","theming_header","style_label","style_switch_light","style_switch_dark","layout_header","layout_label","layout_header_label","content_label","layout_static","layout_offcanvas","layout_fixed","layout_fixed_offcanvas","layout_dd_open_label","layout_navbar_label","layout_footer_label","misc_header","theme_label","direction_label"].forEach((t=>{const e=this.container.querySelector(`.template-customizer-t-${t}`);e&&(e.textContent=i[t])}));const n=i.themes||{},o=this.container.querySelectorAll(".template-customizer-theme-item")||[];for(let a=0,l=o.length;a<l;a++){const t=o[a].querySelector('input[type="radio"]').value;o[a].querySelector(".template-customizer-theme-name").textContent=n[t]||this._getThemeByName(t).title}this.settings.lang=t,e&&this._setSetting("Lang",t),e&&this.settings.onSettingsChange.call(this,this.settings)}update(){if(this._ssr)return;const t=!!document.querySelector(".layout-navbar"),e=!!document.querySelector(".layout-menu"),s=!!document.querySelector(".layout-menu-horizontal.menu, .layout-menu-horizontal .menu");document.querySelector(".layout-wrapper.layout-navbar-full");const i=!!document.querySelector(".content-footer");this._controls.showDropdownOnHover&&(e?(this._controls.showDropdownOnHover.setAttribute("disabled","disabled"),this._controls.showDropdownOnHover.classList.add("disabled")):(this._controls.showDropdownOnHover.removeAttribute("disabled"),this._controls.showDropdownOnHover.classList.remove("disabled"))),this._controls.layoutNavbarOptions&&(t?(this._controls.layoutNavbarOptions.removeAttribute("disabled"),this._controls.layoutNavbarOptionsW.classList.remove("disabled")):(this._controls.layoutNavbarOptions.setAttribute("disabled","disabled"),this._controls.layoutNavbarOptionsW.classList.add("disabled")),s&&t&&"fixed"===this.settings.headerType&&(this._controls.layoutNavbarOptions.setAttribute("disabled","disabled"),this._controls.layoutNavbarOptionsW.classList.add("disabled"))),this._controls.layoutFooterFixed&&(i?(this._controls.layoutFooterFixed.removeAttribute("disabled"),this._controls.layoutFooterFixedW.classList.remove("disabled")):(this._controls.layoutFooterFixed.setAttribute("disabled","disabled"),this._controls.layoutFooterFixedW.classList.add("disabled"))),this._controls.headerType&&(e||s?this._controls.headerType.removeAttribute("disabled"):this._controls.headerType.setAttribute("disabled","disabled"))}clearLocalStorage(){if(this._ssr)return;const t=this._getLayoutName();["Theme","Style","LayoutCollapsed","FixedNavbarOption","LayoutType","contentLayout","Rtl","Lang"].forEach((e=>{const s=`templateCustomizer-${t}--${e}`;localStorage.removeItem(s)})),this._showResetBtnNotification(!1)}destroy(){this._ssr||(this._cleanup(),this.settings=null,this.container.parentNode.removeChild(this.container),this.container=null)}_loadSettings(){const t=this._getSetting("Rtl"),e=this._getSetting("Style"),s=this._getSetting("Theme"),i=this._getSetting("contentLayout"),o=this._getSetting("LayoutCollapsed"),a=this._getSetting("ShowDropdownOnHover"),l=this._getSetting("FixedNavbarOption"),r=this._getSetting("FixedFooter"),h=this._getSetting("LayoutType"),c=!this._getLayoutName().includes("front"),d=c?"admin-mode":"front-mode",u=c?"admin-colorPref":"front-colorPref";let m,p;""!==t||""!==e||""!==s||""!==i||""!==o||""!==l||""!==h?this._showResetBtnNotification(!0):this._showResetBtnNotification(!1),m=""!==h&&-1!==["static","static-offcanvas","fixed","fixed-offcanvas"].indexOf(h)?h:this.settings.defaultHeaderType,this.settings.headerType=m,this.settings.rtl=this._checkCookie("direction")?this._getCookie("direction"):""!==t?"true"===t:this.settings.defaultTextDir,this.settings.stylesOpt=-1!==this.settings.styles.indexOf(e)?e:this.settings.defaultStyle,"system"===this._getCookie(d)?window.matchMedia("(prefers-color-scheme: dark)").matches?(this._setCookie(u,"dark",365),this.settings.style="dark"):(this._setCookie(u,"light",365),this.settings.style="light"):"system"===this.settings.stylesOpt?window.matchMedia("(prefers-color-scheme: dark)").matches?this.settings.style="dark":this.settings.style="light":this.settings.style=-1!==this.settings.styles.indexOf(e)?e:this.settings.stylesOpt,this.settings.contentLayout=""!==i?i:this.settings.defaultContentLayout,this.settings.layoutCollapsed=""!==o?"true"===o:this.settings.defaultMenuCollapsed,this.settings.showDropdownOnHover=""!==a?"true"===a:this.settings.defaultShowDropdownOnHover,p=""!==l&&-1!==["static","sticky","hidden"].indexOf(l)?l:this.settings.defaultNavbarType,this.settings.layoutNavbarOptions=p,this.settings.layoutFooterFixed=""!==r?"true"===r:this.settings.defaultFooterFixed,this._checkCookie("theme")?this.settings.theme=this._getThemeByName(this._getCookie("theme"),!0):this.settings.theme=this._getThemeByName(this._getSetting("Theme"),!0),this._hasControls("rtl")||(this.settings.rtl="rtl"===document.documentElement.getAttribute("dir")),this._hasControls("style")||(this.settings.style=n.contains("dark-style")?"dark":"light"),this._hasControls("contentLayout")||(this.settings.contentLayout=null),this._hasControls("headerType")||(this.settings.headerType=null),this._hasControls("layoutCollapsed")||(this.settings.layoutCollapsed=null),this._hasControls("layoutNavbarOptions")||(this.settings.layoutNavbarOptions=null),this._hasControls("themes")||(this.settings.theme=null)}_setup(t=document){const e=(t,e,s,i,n)=>(n=n||t,this._getElementFromString(`<div class="col-4 px-2">\n      <div class="form-check custom-option custom-option-icon">\n        <label class="form-check-label custom-option-content p-0" for="${s}${t}">\n          <span class="custom-option-body mb-0">\n            <img src="${assetsPath}img/customizer/${n}${i?"-dark":""}.svg" alt="${e}" class="img-fluid scaleX-n1-rtl" />\n          </span>\n          <input\n            name="${s}"\n            class="form-check-input d-none"\n            type="radio"\n            value="${t}"\n            id="${s}${t}" />\n        </label>\n      </div>\n      <label class="form-check-label small text-nowrap text-body mt-1" for="${s}${t}">${e}</label>\n    </div>`));this._cleanup(),this.container=this._getElementFromString('<div id="template-customizer" class="bg-card">\n  <a href="javascript:void(0)" class="template-customizer-open-btn" tabindex="-1"></a>\n\n  <div class="p-6 m-0 lh-1 border-bottom template-customizer-header position-relative py-4">\n    <h6 class="template-customizer-t-panel_header mb-1"></h6>\n    <p class="template-customizer-t-panel_sub_header mb-0 small"></p>\n    <div class="d-flex align-items-center gap-2 position-absolute end-0 top-0 mt-6 me-5">\n      <a\n        href="javascript:void(0)"\n        class="template-customizer-reset-btn text-heading"\n        data-bs-toggle="tooltip"\n        data-bs-placement="bottom"\n        title="Reset Customizer"\n        ><i class="ti ti-refresh ti-lg"></i\n        ><span class="badge rounded-pill bg-danger badge-dot badge-notifications d-none"></span\n      ></a>\n      <a href="javascript:void(0)" class="template-customizer-close-btn fw-light text-heading" tabindex="-1">\n        <i class="ti ti-x ti-lg"></i>\n      </a>\n    </div>\n  </div>\n\n  <div class="template-customizer-inner pt-6">\n    \x3c!-- Theming --\x3e\n    <div class="template-customizer-theming">\n      <h5 class="m-0 px-6 py-6">\n        <span class="template-customizer-t-theming_header bg-label-primary rounded-1 py-1 px-3 small"></span>\n      </h5>\n\n      \x3c!-- Style --\x3e\n      <div class="m-0 px-6 pb-6 template-customizer-style w-100">\n        <label for="customizerStyle" class="form-label d-block template-customizer-t-style_label mb-2"></label>\n        <div class="row px-1 template-customizer-styles-options"></div>\n      </div>\n\n      \x3c!-- Themes --\x3e\n      <div class="m-0 px-6 template-customizer-themes w-100">\n        <label for="customizerTheme" class="form-label template-customizer-t-theme_label mb-2"></label>\n        <div class="row px-1 template-customizer-themes-options"></div>\n      </div>\n    </div>\n    \x3c!--/ Theming --\x3e\n\n    \x3c!-- Layout --\x3e\n    <div class="template-customizer-layout">\n      <hr class="m-0 px-6 my-6" />\n      <h5 class="m-0 px-6 pb-6">\n        <span class="template-customizer-t-layout_header bg-label-primary rounded-2 py-1 px-3 small"></span>\n      </h5>\n\n      \x3c!-- Layout(Menu) --\x3e\n      <div class="m-0 px-6 pb-6 d-block template-customizer-layouts">\n        <label for="customizerStyle" class="form-label d-block template-customizer-t-layout_label mb-2"></label>\n        <div class="row px-1 template-customizer-layouts-options">\n          \x3c!--? Uncomment If using offcanvas layout --\x3e\n          \x3c!-- <div class="col-12">\n            <div class="form-check">\n              <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-offcanvas"\n                value="static-offcanvas">\n              <label class="form-check-label template-customizer-t-layout_offcanvas"\n                for="layoutRadios-offcanvas"></label>\n            </div>\n          </div> --\x3e\n          \x3c!-- <div class="col-12">\n            <div class="form-check">\n              <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-fixed_offcanvas"\n                value="fixed-offcanvas">\n              <label class="form-check-label template-customizer-t-layout_fixed_offcanvas"\n                for="layoutRadios-fixed_offcanvas"></label>\n            </div>\n          </div> --\x3e\n        </div>\n      </div>\n\n      \x3c!-- Header Options for Horizontal --\x3e\n      <div class="m-0 px-6 pb-6 template-customizer-headerOptions w-100">\n        <label for="customizerHeader" class="form-label template-customizer-t-layout_header_label mb-2"></label>\n        <div class="row px-1 template-customizer-header-options"></div>\n      </div>\n\n      \x3c!-- Fixed navbar --\x3e\n      <div class="m-0 px-6 pb-6 template-customizer-layoutNavbarOptions w-100">\n        <label for="customizerNavbar" class="form-label template-customizer-t-layout_navbar_label mb-2"></label>\n        <div class="row px-1 template-customizer-navbar-options"></div>\n      </div>\n\n      \x3c!-- Content --\x3e\n      <div class="m-0 px-6 pb-6 template-customizer-content w-100">\n        <label for="customizerContent" class="form-label template-customizer-t-content_label mb-2"></label>\n        <div class="row px-1 template-customizer-content-options"></div>\n      </div>\n\n      \x3c!-- Directions --\x3e\n      <div class="m-0 px-6 pb-6 template-customizer-directions w-100">\n        <label for="customizerDirection" class="form-label template-customizer-t-direction_label mb-2"></label>\n        <div class="row px-1 template-customizer-directions-options"></div>\n      </div>\n    </div>\n    \x3c!--/ Layout --\x3e\n  </div>\n</div>\n');const s=this.container;this.settings.displayCustomizer?s.setAttribute("style","visibility: visible"):s.setAttribute("style","visibility: hidden");const i=this.container.querySelector(".template-customizer-open-btn"),o=()=>{this.container.classList.add("template-customizer-open"),this.update(),this._updateInterval&&clearInterval(this._updateInterval),this._updateInterval=setInterval((()=>{this.update()}),500)};i.addEventListener("click",o),this._listeners.push([i,"click",o]);const a=this.container.querySelector(".template-customizer-reset-btn"),l=()=>{this._getLayoutName().includes("front")?(this._deleteCookie("front-mode"),this._deleteCookie("front-colorPref")):(this._deleteCookie("admin-mode"),this._deleteCookie("admin-colorPref")),this.clearLocalStorage(),window.location.reload(),this._deleteCookie("colorPref"),this._deleteCookie("theme"),this._deleteCookie("direction")};a.addEventListener("click",l),this._listeners.push([a,"click",l]);const r=this.container.querySelector(".template-customizer-close-btn"),h=()=>{this.container.classList.remove("template-customizer-open"),this._updateInterval&&(clearInterval(this._updateInterval),this._updateInterval=null)};r.addEventListener("click",h),this._listeners.push([r,"click",h]);const c=this.container.querySelector(".template-customizer-style"),d=c.querySelector(".template-customizer-styles-options");if(this._hasControls("style")){this.settings.availableStyles.forEach((t=>{const s=e(t.name,t.title,"customRadioIcon",n.contains("dark-style"));d.appendChild(s)})),d.querySelector(`input[value="${this.settings.stylesOpt}"]`).setAttribute("checked","checked");const t=t=>{this._loadingState(!0),this.setStyle(t.target.value,!0,(()=>{this._loadingState(!1)}))};d.addEventListener("change",t),this._listeners.push([d,"change",t])}else c.parentNode.removeChild(c);const u=this.container.querySelector(".template-customizer-themes"),m=u.querySelector(".template-customizer-themes-options");if(this._hasControls("themes")){this.settings.availableThemes.forEach((t=>{let s="";s="theme-semi-dark"===t.name?"semi-dark":"theme-bordered"===t.name?"border":"default";const i=e(t.name,t.title,"themeRadios",n.contains("dark-style"),s);m.appendChild(i)})),m.querySelector(`input[value="${this.settings.theme.name}"]`).setAttribute("checked","checked");const t=t=>{this._loading=!0,this._loadingState(!0,!0),this.setTheme(t.target.value,!0,(()=>{this._loading=!1,this._loadingState(!1,!0)}))};m.addEventListener("change",t),this._listeners.push([m,"change",t])}else u.parentNode.removeChild(u);const p=this.container.querySelector(".template-customizer-theming");this._hasControls("style")||this._hasControls("themes")||p.parentNode.removeChild(p);const g=this.container.querySelector(".template-customizer-layout");if(this._hasControls("rtl headerType contentLayout layoutCollapsed layoutNavbarOptions",!0)){const t=this.container.querySelector(".template-customizer-directions");if(this._hasControls("rtl")&&rtlSupport){const s=t.querySelector(".template-customizer-directions-options");this.settings.availableDirections.forEach((t=>{const i=e(t.name,t.title,"directionRadioIcon",n.contains("dark-style"));s.appendChild(i)})),s.querySelector(`input[value="${"true"===this.settings.rtl?"rtl":"ltr"}"]`).setAttribute("checked","checked");const i=t=>{this._loadingState(!0),"ar"===this._getSetting("Lang")?this._setSetting("Lang","en"):this._setSetting("Lang","ar"),this.setRtl("rtl"===t.target.value,!0,(()=>{this._loadingState(!1)})),"rtl"===t.target.value?window.location.href=baseUrl+"lang/ar":window.location.href=baseUrl+"lang/en"};s.addEventListener("change",i),this._listeners.push([s,"change",i])}else t.parentNode.removeChild(t);const s=this.container.querySelector(".template-customizer-headerOptions"),i=document.documentElement.getAttribute("data-template").split("-");if(this._hasControls("headerType")){const t=s.querySelector(".template-customizer-header-options");setTimeout((()=>{i.includes("vertical")&&s.parentNode.removeChild(s)}),100),this.settings.availableHeaderTypes.forEach((s=>{const i=e(s.name,s.title,"headerRadioIcon",n.contains("dark-style"),`horizontal-${s.name}`);t.appendChild(i)})),t.querySelector(`input[value="${this.settings.headerType}"]`).setAttribute("checked","checked");const o=t=>{this.setLayoutType(t.target.value)};t.addEventListener("change",o),this._listeners.push([t,"change",o])}else s.parentNode.removeChild(s);const o=this.container.querySelector(".template-customizer-content");if(this._hasControls("contentLayout")){const t=o.querySelector(".template-customizer-content-options");this.settings.availableContentLayouts.forEach((s=>{const i=e(s.name,s.title,"contentRadioIcon",n.contains("dark-style"));t.appendChild(i)})),t.querySelector(`input[value="${this.settings.contentLayout}"]`).setAttribute("checked","checked");const s=t=>{this._loading=!0,this._loadingState(!0,!0),this.setContentLayout(t.target.value,!0,(()=>{this._loading=!1,this._loadingState(!1,!0)}))};t.addEventListener("change",s),this._listeners.push([t,"change",s])}else o.parentNode.removeChild(o);const a=this.container.querySelector(".template-customizer-layouts");if(this._hasControls("layoutCollapsed")){setTimeout((()=>{document.querySelector(".layout-menu-horizontal")&&a.parentNode.removeChild(a)}),100);const t=a.querySelector(".template-customizer-layouts-options");this.settings.availableLayouts.forEach((s=>{const i=e(s.name,s.title,"layoutsRadios",n.contains("dark-style"));t.appendChild(i)})),t.querySelector(`input[value="${this.settings.layoutCollapsed?"collapsed":"expanded"}"]`).setAttribute("checked","checked");const s=t=>{window.Helpers.setCollapsed("collapsed"===t.target.value,!0),this._setSetting("LayoutCollapsed","collapsed"===t.target.value)};t.addEventListener("change",s),this._listeners.push([t,"change",s])}else a.parentNode.removeChild(a);const l=this.container.querySelector(".template-customizer-layoutNavbarOptions");if(this._hasControls("layoutNavbarOptions")){setTimeout((()=>{i.includes("horizontal")&&l.parentNode.removeChild(l)}),100);const t=l.querySelector(".template-customizer-navbar-options");this.settings.availableNavbarOptions.forEach((s=>{const i=e(s.name,s.title,"navbarOptionRadios",n.contains("dark-style"));t.appendChild(i)})),t.querySelector(`input[value="${this.settings.layoutNavbarOptions}"]`).setAttribute("checked","checked");const s=t=>{this._loading=!0,this._loadingState(!0,!0),this.setLayoutNavbarOption(t.target.value,!0,(()=>{this._loading=!1,this._loadingState(!1,!0)}))};t.addEventListener("change",s),this._listeners.push([t,"change",s])}else l.parentNode.removeChild(l)}else g.parentNode.removeChild(g);setTimeout((()=>{const t=this.container.querySelector(".template-customizer-layout");document.querySelector(".menu-vertical")?this._hasControls("rtl contentLayout layoutCollapsed layoutNavbarOptions",!0)||t&&t.parentNode.removeChild(t):document.querySelector(".menu-horizontal")&&(this._hasControls("rtl contentLayout headerType",!0)||t&&t.parentNode.removeChild(t))}),100),this.setLang(this.settings.lang,!1,!0),t===document?t.body?t.body.appendChild(this.container):window.addEventListener("DOMContentLoaded",(()=>t.body.appendChild(this.container))):t.appendChild(this.container)}_initDirection(){this._hasControls("rtl")&&document.documentElement.setAttribute("dir",this._checkCookie("direction")?"true"===this._getCookie("direction")?"rtl":"ltr":this.settings.rtl?"rtl":"ltr")}_initStyle(){if(!this._hasControls("style"))return;const{style:t}=this.settings;this._insertStylesheet("template-customizer-core-css",this.pathResolver(this.settings.cssPath+this.settings.cssFilenamePattern.replace("%name%","core"+("light"!==t?`-${t}`:""))));("light"===t?["dark-style"]:["light-style"]).forEach((t=>{document.documentElement.classList.remove(t)})),document.documentElement.classList.add(`${t}-style`)}_initTheme(){if(this._hasControls("themes"))this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",this.settings.theme.name+("light"!==this.settings.style?`-${this.settings.style}`:""))));else{const t=this._getSetting("Theme");this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t||this.settings.defaultTheme.name+("light"!==this.settings.style?`-${this.settings.style}`:""))))}}_loadStylesheet(t,e){const s=document.createElement("link");s.rel="stylesheet",s.type="text/css",s.href=t,s.className=e,document.head.appendChild(s)}_insertStylesheet(t,e){const s=document.querySelector(`.${t}`);if("number"==typeof document.documentMode&&document.documentMode<11){if(!s)return;if(e===s.getAttribute("href"))return;const i=document.createElement("link");i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.className=t,i.setAttribute("href",e),s.parentNode.insertBefore(i,s.nextSibling)}else this._loadStylesheet(e,t);s&&s.parentNode.removeChild(s)}_loadStylesheets(t,e){const s=Object.keys(t),i=s.length;let n=0;function o(t,e,s=()=>{}){const i=document.createElement("link");i.setAttribute("href",t),i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.className=e.className;const n="sheet"in i?"sheet":"styleSheet",o="sheet"in i?"cssRules":"rules";let a;const l=setTimeout((()=>{clearInterval(a),clearTimeout(l),e.parentNode.contains(i)&&e.parentNode.removeChild(i),s(!1,t)}),15e3);a=setInterval((()=>{try{i[n]&&i[n][o].length&&(clearInterval(a),clearTimeout(l),e.parentNode.removeChild(e),s(!0))}catch(t){}}),10),e.setAttribute("href",i.href)}for(let a=0;a<s.length;a++)o(s[a],t[s[a]],void((n+=1)>=i&&e()))}_loadingState(t,e){this.container.classList[t?"add":"remove"]("template-customizer-loading"+(e?"-theme":""))}_getElementFromString(t){const e=document.createElement("div");return e.innerHTML=t,e.firstChild}_getSetting(t){let e=null;const s=this._getLayoutName();try{e=localStorage.getItem(`templateCustomizer-${s}--${t}`)}catch(i){}return String(e||"")}_showResetBtnNotification(t=!0){setTimeout((()=>{const e=this.container.querySelector(".template-customizer-reset-btn .badge");t?e.classList.remove("d-none"):e.classList.add("d-none")}),200)}_setSetting(t,e){const s=this._getLayoutName();try{localStorage.setItem(`templateCustomizer-${s}--${t}`,String(e)),this._showResetBtnNotification()}catch(i){}}_getLayoutName(){return document.getElementsByTagName("HTML")[0].getAttribute("data-template")}_removeListeners(){for(let t=0,e=this._listeners.length;t<e;t++)this._listeners[t][0].removeEventListener(this._listeners[t][1],this._listeners[t][2])}_cleanup(){this._removeListeners(),this._listeners=[],this._controls={},this._updateInterval&&(clearInterval(this._updateInterval),this._updateInterval=null)}get _ssr(){return"undefined"==typeof window}_hasControls(t,e=!1){return t.split(" ").reduce(((t,s)=>(-1!==this.settings.controls.indexOf(s)?(e||!1!==t)&&(t=!0):e&&!0===t||(t=!1),t)),null)}_getDefaultTheme(t){let e;if(e="string"==typeof t?this._getThemeByName(t,!1):this.settings.availableThemes[t],!e)throw new Error(`Theme ID "${t}" not found!`);return e}_getThemeByName(t,e=!1){const s=this.settings.availableThemes;for(let i=0,n=s.length;i<n;i++)if(s[i].name===t)return s[i];return e?this.settings.defaultTheme:null}_setCookie(t,e,s,i="/",n=""){const o=`${encodeURIComponent(t)}=${encodeURIComponent(e)}`;let a="";if(s){const t=new Date;t.setTime(t.getTime()+24*s*60*60*1e3),a=`; expires=${t.toUTCString()}`}const l=`; path=${i}`,r=n?`; domain=${n}`:"";document.cookie=`${o}${a}${l}${r}`}_getCookie(t){const e=document.cookie.split("; ");for(let s=0;s<e.length;s++){const[i,n]=e[s].split("=");if(decodeURIComponent(i)===t)return decodeURIComponent(n)}return null}_checkCookie(t){return null!==this._getCookie(t)}_deleteCookie(t){document.cookie=t+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"}}p.STYLES=[{name:"light",title:"Light"},{name:"dark",title:"Dark"},{name:"system",title:"System"}],p.THEMES=[{name:"theme-default",title:"Default"},{name:"theme-bordered",title:"Bordered"},{name:"theme-semi-dark",title:"Semi Dark"}],p.LAYOUTS=[{name:"expanded",title:"Expanded"},{name:"collapsed",title:"Collapsed"}],p.NAVBAR_OPTIONS=[{name:"sticky",title:"Sticky"},{name:"static",title:"Static"},{name:"hidden",title:"Hidden"}],p.HEADER_TYPES=[{name:"fixed",title:"Fixed"},{name:"static",title:"Static"}],p.CONTENT=[{name:"compact",title:"Compact"},{name:"wide",title:"Wide"}],p.DIRECTIONS=[{name:"ltr",title:"Left to Right (En)"},{name:"rtl",title:"Right to Left (Ar)"}],p.LANGUAGES={en:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},fr:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},ar:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},de:{panel_header:"Vorlagen-Anpasser",panel_sub_header:"Anpassen und Vorschau in Echtzeit",theming_header:"Themen",style_label:"Stil (Modus)",theme_label:"Themen",layout_header:"Layout",layout_label:"Menü (Navigation)",layout_header_label:"Header-Typen",content_label:"Inhalt",layout_navbar_label:"Art der Navigationsleiste",direction_label:"Richtung"}},window.TemplateCustomizer=p;
