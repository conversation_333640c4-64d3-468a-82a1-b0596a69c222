!function(){const e=document.getElementById("slider-basic"),t=document.getElementById("slider-handles"),n=document.getElementById("slider-steps"),i=document.getElementById("slider-tap"),r=document.getElementById("slider-drag"),d=document.getElementById("slider-fixed-drag"),l=document.getElementById("slider-combined-options"),a=document.getElementById("slider-hover"),o=document.getElementById("slider-pips");e&&noUiSlider.create(e,{start:[50],connect:[!0,!1],direction:isRtl?"rtl":"ltr",range:{min:0,max:100}}),t&&noUiSlider.create(t,{start:[0,50],direction:isRtl?"rtl":"ltr",step:5,connect:!0,range:{min:0,max:100},pips:{mode:"range",density:5,stepped:!0}}),n&&noUiSlider.create(n,{start:[0,30],snap:!0,connect:!0,direction:isRtl?"rtl":"ltr",range:{min:0,"10%":10,"20%":20,"30%":30,"40%":40,"50%":50,max:100}}),i&&noUiSlider.create(i,{start:[10,30],behaviour:"tap",direction:isRtl?"rtl":"ltr",connect:!0,range:{min:10,max:100}}),r&&noUiSlider.create(r,{start:[40,60],limit:20,behaviour:"drag",direction:isRtl?"rtl":"ltr",connect:!0,range:{min:20,max:80}}),d&&noUiSlider.create(d,{start:[40,60],behaviour:"drag-fixed",direction:isRtl?"rtl":"ltr",connect:!0,range:{min:20,max:80}}),l&&noUiSlider.create(l,{start:[40,60],behaviour:"drag-tap",direction:isRtl?"rtl":"ltr",connect:!0,range:{min:20,max:80}}),a&&(noUiSlider.create(a,{start:20,behaviour:"hover-snap-tap",direction:isRtl?"rtl":"ltr",range:{min:0,max:100}}),a.noUiSlider.on("hover",(function(e){document.getElementById("slider-val").innerHTML=e}))),o&&noUiSlider.create(o,{start:[10],behaviour:"tap-drag",step:10,tooltips:!0,range:{min:0,max:100},pips:{mode:"steps",stepped:!0,density:5},direction:isRtl?"rtl":"ltr"});const c=document.getElementById("slider-primary"),s=document.getElementById("slider-success"),m=document.getElementById("slider-danger"),g=document.getElementById("slider-info"),u=document.getElementById("slider-warning"),p={start:[30,50],connect:!0,behaviour:"tap-drag",step:10,tooltips:!0,range:{min:0,max:100},pips:{mode:"steps",stepped:!0,density:5},direction:isRtl?"rtl":"ltr"};c&&noUiSlider.create(c,p),s&&noUiSlider.create(s,p),m&&noUiSlider.create(m,p),g&&noUiSlider.create(g,p),u&&noUiSlider.create(u,p);const y=document.getElementById("slider-dynamic"),h=document.getElementById("slider-select"),v=document.getElementById("slider-input");if(y&&(noUiSlider.create(y,{start:[10,30],connect:!0,direction:isRtl?"rtl":"ltr",range:{min:-20,max:40}}),y.noUiSlider.on("update",(function(e,t){const n=e[t];t?v.value=n:h.value=Math.round(n)}))),h){for(let e=-20;e<=40;e++){let t=document.createElement("option");t.text=e,t.value=e,h.appendChild(t)}h.addEventListener("change",(function(){y.noUiSlider.set([this.value,null])}))}v&&v.addEventListener("change",(function(){y.noUiSlider.set([null,this.value])}));const E=document.getElementById("slider-vertical"),S=document.getElementById("slider-connect-upper"),U=document.getElementById("slider-vertical-tooltip"),x=document.getElementById("slider-vertical-limit");E&&(E.style.height="200px",noUiSlider.create(E,{start:[40,60],orientation:"vertical",behaviour:"drag",connect:!0,range:{min:0,max:100}})),S&&(S.style.height="200px",noUiSlider.create(S,{start:40,orientation:"vertical",behaviour:"drag",connect:"upper",range:{min:0,max:100}})),U&&(U.style.height="200px",noUiSlider.create(U,{start:10,orientation:"vertical",behaviour:"drag",tooltips:!0,range:{min:0,max:100}})),x&&(x.style.height="200px",noUiSlider.create(x,{start:[0,40],orientation:"vertical",behaviour:"drag",limit:60,tooltips:!0,connect:!0,range:{min:0,max:100}}))}();
