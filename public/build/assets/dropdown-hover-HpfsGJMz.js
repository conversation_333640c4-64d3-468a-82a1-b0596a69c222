!function(t){if(!t||!t.fn)return;const o="[data-bs-toggle=dropdown][data-trigger=hover]";t((function(){t("body").on("mouseenter",`${o}, ${o} ~ .dropdown-menu`,(function(){t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle");const e=t(this).hasClass("dropdown-menu")?t(this):t(this).next(".dropdown-menu");"static"!==window.getComputedStyle(e[0],null).getPropertyValue("position")&&(t(this).is(o)&&t(this).data("hovered",!0),function(t){let o=t.data("dd-timeout");o&&(clearTimeout(o),o=null,t.data("dd-timeout",o)),"true"!==t.attr("aria-expanded")&&t.dropdown("toggle")}(t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle")))})).on("mouseleave",`${o}, ${o} ~ .dropdown-menu`,(function(){t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle");const e=t(this).hasClass("dropdown-menu")?t(this):t(this).next(".dropdown-menu");"static"!==window.getComputedStyle(e[0],null).getPropertyValue("position")&&(t(this).is(o)&&t(this).data("hovered",!1),function(t){let o=t.data("dd-timeout");o&&clearTimeout(o),o=setTimeout((()=>{let o=t.data("dd-timeout");o&&(clearTimeout(o),o=null,t.data("dd-timeout",o)),"true"===t.attr("aria-expanded")&&t.dropdown("toggle")}),150),t.data("dd-timeout",o)}(t(this).hasClass("dropdown-toggle")?t(this):t(this).prev(".dropdown-toggle")))})).on("hide.bs.dropdown",(function(e){t(this).find(o).data("hovered")&&e.preventDefault()}))}))}(window.jQuery);
