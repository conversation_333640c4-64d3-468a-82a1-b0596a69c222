document.addEventListener("DOMContentLoaded",(function(n){!function(){const n=document.querySelector(".price-duration-toggler"),e=[].slice.call(document.querySelectorAll(".price-monthly")),c=[].slice.call(document.querySelectorAll(".price-yearly"));function o(){n.checked?(c.map((function(n){n.classList.remove("d-none")})),e.map((function(n){n.classList.add("d-none")}))):(c.map((function(n){n.classList.add("d-none")})),e.map((function(n){n.classList.remove("d-none")})))}o(),n.onchange=function(){o()}}()}));
