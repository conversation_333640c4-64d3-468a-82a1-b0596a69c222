import{c as e}from"./_commonjsHelpers-MdiGH4nz.js";import{c as n}from"./_commonjs-dynamic-modules-BHR_E30J.js";!function(){return function e(t,o,i){function r(c,d){if(!o[c]){if(!t[c]){var s="function"==typeof n&&n;if(!d&&s)return s(c,!0);if(a)return a(c,!0);var l=new Error("Cannot find module '"+c+"'");throw l.code="MODULE_NOT_FOUND",l}var u=o[c]={exports:{}};t[c][0].call(u.exports,(function(e){return r(t[c][1][e]||e)}),u,u.exports,e,t,o,i)}return o[c].exports}for(var a="function"==typeof n&&n,c=0;c<i.length;c++)r(i[c]);return r}}()({1:[function(e,n,t){var o=e("dragula");!function(){this.jKanban=function(){var e=this,n={enabled:!1},t={enabled:!1};this._disallowedItemProperties=["id","title","click","drag","dragend","drop","order"],this.element="",this.container="",this.boardContainer=[],this.handlers=[],this.dragula=o,this.drake="",this.drakeBoard="",this.itemAddOptions=t,this.itemHandleOptions=n;var i={element:"",gutter:"15px",widthBoard:"250px",responsive:"700",responsivePercentage:!1,boards:[],dragBoards:!0,dragItems:!0,itemAddOptions:t,itemHandleOptions:n,dragEl:function(e,n){},dragendEl:function(e){},dropEl:function(e,n,t,o){},dragBoard:function(e,n){},dragendBoard:function(e){},dropBoard:function(e,n,t,o){},click:function(e){},buttonClick:function(e,n){}};function r(n,t){n.addEventListener("click",(function(n){n.preventDefault(),e.options.click(this),"function"==typeof this.clickfn&&this.clickfn(this)}))}function a(n,t){n.addEventListener("click",(function(n){n.preventDefault(),e.options.buttonClick(this,t)}))}function c(n){var t=[];return e.options.boards.map((function(e){if(e.id===n)return t.push(e)})),t[0]}function d(n,t){for(var o in t)e._disallowedItemProperties.indexOf(o)>-1||n.setAttribute("data-"+o,t[o])}function s(n){var t=n;if(e.options.itemHandleOptions.enabled)if(void 0===(e.options.itemHandleOptions.customHandler||void 0)){var o=e.options.itemHandleOptions.customCssHandler,i=e.options.itemHandleOptions.customCssIconHandler;void 0===(o||void 0)&&(o="drag_handler"),void 0===(i||void 0)&&(i=o+"_icon"),t="<div class='item_handle "+o+"'><i class='item_handle "+i+"'></i></div><div>"+t+"</div>"}else t=e.options.itemHandleOptions.customHandler.replace("%s",t);return t}arguments[0]&&"object"==typeof arguments[0]&&(this.options=function(e,n){var t;for(t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);return e}(i,arguments[0])),this.__getCanMove=function(n){return e.options.itemHandleOptions.enabled?e.options.itemHandleOptions.handleClass?n.classList.contains(e.options.itemHandleOptions.handleClass):n.classList.contains("item_handle"):!!e.options.dragItems},this.init=function(){!function(){e.element=document.querySelector(e.options.element);var n=document.createElement("div");n.classList.add("kanban-container"),e.container=n,document.querySelector(e.options.element).dataset.hasOwnProperty("board")?(url=document.querySelector(e.options.element).dataset.board,window.fetch(url,{method:"GET",headers:{"Content-Type":"application/json"}}).then((n=>{n.json().then((function(n){e.options.boards=n,e.addBoards(e.options.boards,!0)}))})).catch((e=>{}))):e.addBoards(e.options.boards,!0);e.element.appendChild(e.container)}(),window.innerWidth>e.options.responsive&&(e.drakeBoard=e.dragula([e.container],{moves:function(n,t,o,i){return!!e.options.dragBoards&&(o.classList.contains("kanban-board-header")||o.classList.contains("kanban-title-board"))},accepts:function(e,n,t,o){return n.classList.contains("kanban-container")},revertOnSpill:!0,direction:"horizontal"}).on("drag",(function(n,t){n.classList.add("is-moving"),e.options.dragBoard(n,t),"function"==typeof n.dragfn&&n.dragfn(n,t)})).on("dragend",(function(n){!function(){for(var n=1,t=0;t<e.container.childNodes.length;t++)e.container.childNodes[t].dataset.order=n++}(),n.classList.remove("is-moving"),e.options.dragendBoard(n),"function"==typeof n.dragendfn&&n.dragendfn(n)})).on("drop",(function(n,t,o,i){n.classList.remove("is-moving"),e.options.dropBoard(n,t,o,i),"function"==typeof n.dropfn&&n.dropfn(n,t,o,i)})),e.drake=e.dragula(e.boardContainer,{moves:function(n,t,o,i){return e.__getCanMove(o)},revertOnSpill:!0}).on("cancel",(function(n,t,o){e.enableAllBoards()})).on("drag",(function(n,t){var o=n.getAttribute("class");if(""!==o&&o.indexOf("not-draggable")>-1)e.drake.cancel(!0);else{n.classList.add("is-moving"),e.options.dragEl(n,t);var i=c(t.parentNode.dataset.id);void 0!==i.dragTo&&e.options.boards.map((function(n){-1===i.dragTo.indexOf(n.id)&&n.id!==t.parentNode.dataset.id&&e.findBoard(n.id).classList.add("disabled-board")})),null!==n&&"function"==typeof n.dragfn&&n.dragfn(n,t)}})).on("dragend",(function(n){e.options.dragendEl(n),null!==n&&"function"==typeof n.dragendfn&&n.dragendfn(n)})).on("drop",(function(n,t,o,i){e.enableAllBoards();var r=c(o.parentNode.dataset.id);(void 0!==r.dragTo&&-1===r.dragTo.indexOf(t.parentNode.dataset.id)&&t.parentNode.dataset.id!==o.parentNode.dataset.id&&e.drake.cancel(!0),null!==n)&&(!1===e.options.dropEl(n,t,o,i)&&e.drake.cancel(!0),n.classList.remove("is-moving"),"function"==typeof n.dropfn&&n.dropfn(n,t,o,i))})))},this.enableAllBoards=function(){var e=document.querySelectorAll(".kanban-board");if(e.length>0&&void 0!==e)for(var n=0;n<e.length;n++)e[n].classList.remove("disabled-board")},this.addElement=function(n,t){var o=e.element.querySelector('[data-id="'+n+'"] .kanban-drag'),i=document.createElement("div");return i.classList.add("kanban-item"),void 0!==t.id&&""!==t.id&&i.setAttribute("data-eid",t.id),t.class&&Array.isArray(t.class)&&t.class.forEach((function(e){i.classList.add(e)})),i.innerHTML=s(t.title),i.clickfn=t.click,i.dragfn=t.drag,i.dragendfn=t.dragend,i.dropfn=t.drop,d(i,t),r(i),e.options.itemHandleOptions.enabled&&(i.style.cursor="default"),o.appendChild(i),e},this.addForm=function(n,t){var o=e.element.querySelector('[data-id="'+n+'"] .kanban-drag'),i=t.getAttribute("class");return t.setAttribute("class",i+" not-draggable"),o.appendChild(t),e},this.addBoards=function(n,t){if(e.options.responsivePercentage)if(e.container.style.width="100%",e.options.gutter="1%",window.innerWidth>e.options.responsive)var o=(100-2*n.length)/n.length;else o=100-2*n.length;else o=e.options.widthBoard;var i=e.options.itemAddOptions.enabled,c=e.options.itemAddOptions.content,l=e.options.itemAddOptions.class,u=e.options.itemAddOptions.footer;for(var f in n){var p=n[f];t||e.options.boards.push(p),e.options.responsivePercentage||(""===e.container.style.width?e.container.style.width=parseInt(o)+2*parseInt(e.options.gutter)+"px":e.container.style.width=parseInt(e.container.style.width)+parseInt(o)+2*parseInt(e.options.gutter)+"px");var m=document.createElement("div");m.dataset.id=p.id,m.dataset.order=e.container.childNodes.length+1,m.classList.add("kanban-board"),e.options.responsivePercentage?m.style.width=o+"%":m.style.width=o,m.style.marginLeft=e.options.gutter,m.style.marginRight=e.options.gutter;var v=document.createElement("header");if(""!==p.class&&void 0!==p.class)var h=p.class.split(",");else h=[];v.classList.add("kanban-board-header"),h.map((function(e){e=e.replace(/^[ ]+/g,""),v.classList.add(e)})),v.innerHTML='<div class="kanban-title-board">'+p.title+"</div>";var g=document.createElement("main");if(g.classList.add("kanban-drag"),""!==p.bodyClass&&void 0!==p.bodyClass)var y=p.bodyClass.split(",");else y=[];for(var b in y.map((function(e){g.classList.add(e)})),e.boardContainer.push(g),p.item){var w=p.item[b],E=document.createElement("div");E.classList.add("kanban-item"),w.id&&(E.dataset.eid=w.id),w.class&&Array.isArray(w.class)&&w.class.forEach((function(e){E.classList.add(e)})),E.innerHTML=s(w.title),E.clickfn=w.click,E.dragfn=w.drag,E.dragendfn=w.dragend,E.dropfn=w.drop,d(E,w),r(E),e.options.itemHandleOptions.enabled&&(E.style.cursor="default"),g.appendChild(E)}var T=document.createElement("footer");if(i){var C=document.createElement("BUTTON"),O=document.createTextNode(c||"+");C.setAttribute("class",l||"kanban-title-button btn btn-default btn-xs"),C.appendChild(O),u?T.appendChild(C):v.appendChild(C),a(C,p.id)}m.appendChild(v),m.appendChild(g),m.appendChild(T),e.container.appendChild(m)}return e},this.findBoard=function(n){return e.element.querySelector('[data-id="'+n+'"]')},this.getParentBoardID=function(n){return"string"==typeof n&&(n=e.element.querySelector('[data-eid="'+n+'"]')),null===n?null:n.parentNode.parentNode.dataset.id},this.moveElement=function(e,n,t){if(e!==this.getParentBoardID(n))return this.removeElement(n),this.addElement(e,t)},this.replaceElement=function(n,t){var o=n;return"string"==typeof o&&(o=e.element.querySelector('[data-eid="'+n+'"]')),o.innerHTML=t.title,o.clickfn=t.click,o.dragfn=t.drag,o.dragendfn=t.dragend,o.dropfn=t.drop,d(o,t),e},this.findElement=function(n){return e.element.querySelector('[data-eid="'+n+'"]')},this.getBoardElements=function(n){return e.element.querySelector('[data-id="'+n+'"] .kanban-drag').childNodes},this.removeElement=function(n){return"string"==typeof n&&(n=e.element.querySelector('[data-eid="'+n+'"]')),null!==n&&("function"==typeof n.remove?n.remove():n.parentNode.removeChild(n)),e},this.removeBoard=function(n){var t=null;"string"==typeof n&&(t=e.element.querySelector('[data-id="'+n+'"]')),null!==t&&("function"==typeof t.remove?t.remove():t.parentNode.removeChild(t));for(var o=0;o<e.options.boards.length;o++)if(e.options.boards[o].id===n){e.options.boards.splice(o,1);break}return e},this.onButtonClick=function(e){},this.init()}}()},{dragula:9}],2:[function(e,n,t){n.exports=function(e,n){return Array.prototype.slice.call(e,n)}},{}],3:[function(e,n,t){var o=e("ticky");n.exports=function(e,n,t){e&&o((function(){e.apply(t||null,n||[])}))}},{ticky:11}],4:[function(e,n,t){var o=e("atoa"),i=e("./debounce");n.exports=function(e,n){var t=n||{},r={};return void 0===e&&(e={}),e.on=function(n,t){return r[n]?r[n].push(t):r[n]=[t],e},e.once=function(n,t){return t._once=!0,e.on(n,t),e},e.off=function(n,t){var o=arguments.length;if(1===o)delete r[n];else if(0===o)r={};else{var i=r[n];if(!i)return e;i.splice(i.indexOf(t),1)}return e},e.emit=function(){var n=o(arguments);return e.emitterSnapshot(n.shift()).apply(this,n)},e.emitterSnapshot=function(n){var a=(r[n]||[]).slice(0);return function(){var r=o(arguments),c=this||e;if("error"===n&&!1!==t.throws&&!a.length)throw 1===r.length?r[0]:r;return a.forEach((function(o){t.async?i(o,r,c):o.apply(c,r),o._once&&e.off(n,o)})),e}},e}},{"./debounce":3,atoa:2}],5:[function(n,t,o){(function(e){(function(){var o=n("custom-event"),i=n("./eventmap"),r=e.document,a=function(e,n,t,o){return e.addEventListener(n,t,o)},c=function(e,n,t,o){return e.removeEventListener(n,t,o)},d=[];function s(e,n,t){var o=function(e,n,t){var o,i;for(o=0;o<d.length;o++)if((i=d[o]).element===e&&i.type===n&&i.fn===t)return o}(e,n,t);if(o){var i=d[o].wrapper;return d.splice(o,1),i}}e.addEventListener||(a=function(n,t,o){return n.attachEvent("on"+t,function(n,t,o){var i=s(n,t,o)||function(n,t,o){return function(t){var i=t||e.event;i.target=i.target||i.srcElement,i.preventDefault=i.preventDefault||function(){i.returnValue=!1},i.stopPropagation=i.stopPropagation||function(){i.cancelBubble=!0},i.which=i.which||i.keyCode,o.call(n,i)}}(n,0,o);return d.push({wrapper:i,element:n,type:t,fn:o}),i}(n,t,o))},c=function(e,n,t){var o=s(e,n,t);if(o)return e.detachEvent("on"+n,o)}),t.exports={add:a,remove:c,fabricate:function(e,n,t){var a=-1===i.indexOf(n)?new o(n,{detail:t}):function(){var e;r.createEvent?(e=r.createEvent("Event")).initEvent(n,!0,!0):r.createEventObject&&(e=r.createEventObject());return e}();e.dispatchEvent?e.dispatchEvent(a):e.fireEvent("on"+n,a)}}}).call(this)}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":6,"custom-event":7}],6:[function(n,t,o){(function(e){(function(){var n=[],o="",i=/^on/;for(o in e)i.test(o)&&n.push(o.slice(2));t.exports=n}).call(this)}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],7:[function(n,t,o){(function(e){(function(){var n=e.CustomEvent;t.exports=function(){try{var e=new n("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(t){}return!1}()?n:"undefined"!=typeof document&&"function"==typeof document.createEvent?function(e,n){var t=document.createEvent("CustomEvent");return n?t.initCustomEvent(e,n.bubbles,n.cancelable,n.detail):t.initCustomEvent(e,!1,!1,void 0),t}:function(e,n){var t=document.createEventObject();return t.type=e,n?(t.bubbles=Boolean(n.bubbles),t.cancelable=Boolean(n.cancelable),t.detail=n.detail):(t.bubbles=!1,t.cancelable=!1,t.detail=void 0),t}}).call(this)}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,n,t){var o={};function i(e){var n=o[e];return n?n.lastIndex=0:o[e]=n=new RegExp("(?:^|\\s)"+e+"(?:\\s|$)","g"),n}n.exports={add:function(e,n){var t=e.className;t.length?i(n).test(t)||(e.className+=" "+n):e.className=n},rm:function(e,n){e.className=e.className.replace(i(n)," ").trim()}}},{}],9:[function(n,t,o){(function(e){(function(){var o=n("contra/emitter"),i=n("crossvent"),r=n("./classes"),a=document,c=a.documentElement;function d(n,t,o,r){e.navigator.pointerEnabled?i[t](n,{mouseup:"pointerup",mousedown:"pointerdown",mousemove:"pointermove"}[o],r):e.navigator.msPointerEnabled?i[t](n,{mouseup:"MSPointerUp",mousedown:"MSPointerDown",mousemove:"MSPointerMove"}[o],r):(i[t](n,{mouseup:"touchend",mousedown:"touchstart",mousemove:"touchmove"}[o],r),i[t](n,o,r))}function s(e){if(void 0!==e.touches)return e.touches.length;if(void 0!==e.which&&0!==e.which)return e.which;if(void 0!==e.buttons)return e.buttons;var n=e.button;return void 0!==n?1&n?1:2&n?3:4&n?2:0:void 0}function l(n,t){return void 0!==e[t]?e[t]:c.clientHeight?c[n]:a.body[n]}function u(e,n,t){var o,i=(e=e||{}).className||"";return e.className+=" gu-hide",o=a.elementFromPoint(n,t),e.className=i,o}function f(){return!1}function p(){return!0}function m(e){return e.width||e.right-e.left}function v(e){return e.height||e.bottom-e.top}function h(e){return e.parentNode===a?null:e.parentNode}function g(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||y(e)}function y(e){return!!e&&("false"!==e.contentEditable&&("true"===e.contentEditable||y(h(e))))}function b(e){return e.nextElementSibling||function(){var n=e;do{n=n.nextSibling}while(n&&1!==n.nodeType);return n}()}function w(e,n){var t=function(e){return e.targetTouches&&e.targetTouches.length?e.targetTouches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e}(n),o={pageX:"clientX",pageY:"clientY"};return e in o&&!(e in t)&&o[e]in t&&(e=o[e]),t[e]}t.exports=function(e,n){var t,y,E,T,C,O,k,S,x,L,B;1===arguments.length&&!1===Array.isArray(e)&&(n=e,e=[]);var N,I=null,_=n||{};void 0===_.moves&&(_.moves=p),void 0===_.accepts&&(_.accepts=p),void 0===_.invalid&&(_.invalid=function(){return!1}),void 0===_.containers&&(_.containers=e||[]),void 0===_.isContainer&&(_.isContainer=f),void 0===_.copy&&(_.copy=!1),void 0===_.copySortSource&&(_.copySortSource=!1),void 0===_.revertOnSpill&&(_.revertOnSpill=!1),void 0===_.removeOnSpill&&(_.removeOnSpill=!1),void 0===_.direction&&(_.direction="vertical"),void 0===_.ignoreInputTextSelection&&(_.ignoreInputTextSelection=!0),void 0===_.mirrorContainer&&(_.mirrorContainer=a.body);var A=o({containers:_.containers,start:function(e){var n=Y(e);n&&F(n)},end:R,cancel:G,remove:W,destroy:function(){P(!0),K({})},canMove:function(e){return!!Y(e)},dragging:!1});return!0===_.removeOnSpill&&A.on("over",(function(e){r.rm(e,"gu-hide")})).on("out",(function(e){A.dragging&&r.add(e,"gu-hide")})),P(),A;function H(e){return-1!==A.containers.indexOf(e)||_.isContainer(e)}function P(e){var n=e?"remove":"add";d(c,n,"mousedown",D),d(c,n,"mouseup",K)}function M(e){d(c,e?"remove":"add","mousemove",X)}function j(e){var n=e?"remove":"add";i[n](c,"selectstart",q),i[n](c,"click",q)}function q(e){N&&e.preventDefault()}function D(e){if(O=e.clientX,k=e.clientY,!(1!==s(e)||e.metaKey||e.ctrlKey)){var n=e.target,t=Y(n);t&&(N=t,M(),"mousedown"===e.type&&(g(n)?n.focus():e.preventDefault()))}}function X(e){if(N)if(0!==s(e)){if(!(void 0!==e.clientX&&Math.abs(e.clientX-O)<=(_.slideFactorX||0)&&void 0!==e.clientY&&Math.abs(e.clientY-k)<=(_.slideFactorY||0))){if(_.ignoreInputTextSelection){var n=w("clientX",e)||0,o=w("clientY",e)||0;if(g(a.elementFromPoint(n,o)))return}var i=N;M(!0),j(),R(),F(i);var u,f={left:(u=E.getBoundingClientRect()).left+l("scrollLeft","pageXOffset"),top:u.top+l("scrollTop","pageYOffset")};T=w("pageX",e)-f.left,C=w("pageY",e)-f.top,r.add(L||E,"gu-transit"),function(){if(t)return;var e=E.getBoundingClientRect();(t=E.cloneNode(!0)).style.width=m(e)+"px",t.style.height=v(e)+"px",r.rm(t,"gu-transit"),r.add(t,"gu-mirror"),_.mirrorContainer.appendChild(t),d(c,"add","mousemove",Q),r.add(_.mirrorContainer,"gu-unselectable"),A.emit("cloned",t,E,"mirror")}(),Q(e)}}else K({})}function Y(e){if(!(A.dragging&&t||H(e))){for(var n=e;h(e)&&!1===H(h(e));){if(_.invalid(e,n))return;if(!(e=h(e)))return}var o=h(e);if(o)if(!_.invalid(e,n))if(_.moves(e,o,n,b(e)))return{item:e,source:o}}}function F(e){var n,t;n=e.item,t=e.source,("boolean"==typeof _.copy?_.copy:_.copy(n,t))&&(L=e.item.cloneNode(!0),A.emit("cloned",L,e.item,"copy")),y=e.source,E=e.item,S=x=b(e.item),A.dragging=!0,A.emit("drag",E,y)}function R(){if(A.dragging){var e=L||E;z(e,h(e))}}function U(){N=!1,M(!0),j(!0)}function K(e){if(U(),A.dragging){var n=L||E,o=w("clientX",e)||0,i=w("clientY",e)||0,r=J(u(t,o,i),o,i);r&&(L&&_.copySortSource||!L||r!==y)?z(n,r):_.removeOnSpill?W():G()}}function z(e,n){var t=h(e);L&&_.copySortSource&&n===y&&t.removeChild(E),$(n)?A.emit("cancel",e,y,y):A.emit("drop",e,n,y,x),V()}function W(){if(A.dragging){var e=L||E,n=h(e);n&&n.removeChild(e),A.emit(L?"cancel":"remove",e,n,y),V()}}function G(e){if(A.dragging){var n=arguments.length>0?e:_.revertOnSpill,t=L||E,o=h(t),i=$(o);!1===i&&n&&(L?o&&o.removeChild(L):y.insertBefore(t,S)),i||n?A.emit("cancel",t,y,y):A.emit("drop",t,o,y,x),V()}}function V(){var e=L||E;U(),t&&(r.rm(_.mirrorContainer,"gu-unselectable"),d(c,"remove","mousemove",Q),h(t).removeChild(t),t=null),e&&r.rm(e,"gu-transit"),B&&clearTimeout(B),A.dragging=!1,I&&A.emit("out",e,I,y),A.emit("dragend",e),y=E=L=S=x=B=I=null}function $(e,n){var o;return o=void 0!==n?n:t?x:b(L||E),e===y&&o===S}function J(e,n,t){for(var o=e;o&&!i();)o=h(o);return o;function i(){if(!1===H(o))return!1;var i=Z(o,e),r=ee(o,i,n,t);return!!$(o,r)||_.accepts(E,o,y,r)}}function Q(e){if(t){e.preventDefault();var n=w("clientX",e)||0,o=w("clientY",e)||0,i=n-T,r=o-C;t.style.left=i+"px",t.style.top=r+"px";var a=L||E,c=u(t,n,o),d=J(c,n,o),s=null!==d&&d!==I;(s||null===d)&&(I&&m("out"),I=d,s&&m("over"));var l=h(a);if(d!==y||!L||_.copySortSource){var f,p=Z(d,c);if(null!==p)f=ee(d,p,n,o);else{if(!0!==_.revertOnSpill||L)return void(L&&l&&l.removeChild(a));f=S,d=y}(null===f&&s||f!==a&&f!==b(a))&&(x=f,d.insertBefore(a,f),A.emit("shadow",a,d,y))}else l&&l.removeChild(a)}function m(e){A.emit(e,a,I,y)}}function Z(e,n){for(var t=n;t!==e&&h(t)!==e;)t=h(t);return t===c?null:t}function ee(e,n,t,o){var i="horizontal"===_.direction;return n!==e?function(){var e=n.getBoundingClientRect();if(i)return r(t>e.left+m(e)/2);return r(o>e.top+v(e)/2)}():function(){var n,r,a,c=e.children.length;for(n=0;n<c;n++){if(a=(r=e.children[n]).getBoundingClientRect(),i&&a.left+a.width/2>t)return r;if(!i&&a.top+a.height/2>o)return r}return null}();function r(e){return e?b(n):n}}}}).call(this)}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./classes":8,"contra/emitter":4,crossvent:5}],10:[function(e,n,t){var o,i,r=n.exports={};function a(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function d(e){if(o===setTimeout)return setTimeout(e,0);if((o===a||!o)&&setTimeout)return o=setTimeout,setTimeout(e,0);try{return o(e,0)}catch(n){try{return o.call(null,e,0)}catch(t){return o.call(this,e,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:a}catch(e){o=a}try{i="function"==typeof clearTimeout?clearTimeout:c}catch(e){i=c}}();var s,l=[],u=!1,f=-1;function p(){u&&s&&(u=!1,s.length?l=s.concat(l):f=-1,l.length&&m())}function m(){if(!u){var e=d(p);u=!0;for(var n=l.length;n;){for(s=l,l=[];++f<n;)s&&s[f].run();f=-1,n=l.length}s=null,u=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===c||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{return i(e)}catch(n){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function v(e,n){this.fun=e,this.array=n}function h(){}r.nextTick=function(e){var n=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)n[t-1]=arguments[t];l.push(new v(e,n)),1!==l.length||u||d(m)},v.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=h,r.addListener=h,r.once=h,r.off=h,r.removeListener=h,r.removeAllListeners=h,r.emit=h,r.prependListener=h,r.prependOnceListener=h,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},{}],11:[function(e,n,t){(function(e){(function(){var t;t="function"==typeof e?function(n){e(n)}:function(e){setTimeout(e,0)},n.exports=t}).call(this)}).call(this,e("timers").setImmediate)},{timers:12}],12:[function(e,n,t){(function(n,o){(function(){var i=e("process/browser.js").nextTick,r=Function.prototype.apply,a=Array.prototype.slice,c={},d=0;function s(e,n){this._id=e,this._clearFn=n}t.setTimeout=function(){return new s(r.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new s(r.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e.close()},s.prototype.unref=s.prototype.ref=function(){},s.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,n){clearTimeout(e._idleTimeoutId),e._idleTimeout=n},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var n=e._idleTimeout;n>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),n))},t.setImmediate="function"==typeof n?n:function(e){var n=d++,o=!(arguments.length<2)&&a.call(arguments,1);return c[n]=!0,i((function(){c[n]&&(o?e.apply(null,o):e.call(null),t.clearImmediate(n))})),n},t.clearImmediate="function"==typeof o?o:function(e){delete c[e]}}).call(this)}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":10,timers:12}]},{},[1]);
