class e{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(t,r){return r?t&&t.loc&&r.loc&&t.loc.lexer===r.loc.lexer?new e(t.loc.lexer,t.loc.start,r.loc.end):null:t&&t.loc}}class t{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(r,a){return new t(a,e.range(this,r))}}class r{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var a,n,i="KaTeX parse error: "+e,o=t&&t.loc;if(o&&o.start<=o.end){var s=o.lexer.input;a=o.start,n=o.end,a===s.length?i+=" at end of input: ":i+=" at position "+(a+1)+": ";var l=s.slice(a,n).replace(/[^]/g,"$&̲");i+=(a>15?"…"+s.slice(a-15,a):s.slice(0,a))+l+(n+15<s.length?s.slice(n,n+15)+"…":s.slice(n))}var h=new Error(i);return h.name="ParseError",h.__proto__=r.prototype,h.position=a,null!=a&&null!=n&&(h.length=n-a),h.rawMessage=e,h}}r.prototype.__proto__=Error.prototype;var a=/([A-Z])/g,n={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},i=/[&><"']/g;var o=function e(t){return"ordgroup"===t.type||"color"===t.type?1===t.body.length?e(t.body[0]):t:"font"===t.type?e(t.body):t},s={contains:function(e,t){return-1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(i,(e=>n[e]))},hyphenate:function(e){return e.replace(a,"-$1").toLowerCase()},getBaseElem:o,isCharacterBox:function(e){var t=o(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?":"!==t[2]?null:/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?t[1].toLowerCase():null:"_relative"}},l={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};function h(e){if(e.default)return e.default;var t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class m{constructor(e){for(var t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},l)if(l.hasOwnProperty(t)){var r=l[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:h(r)}}reportNonstrict(e,t,a){var n=this.strict;if("function"==typeof n&&(n=n(e,t,a)),n&&"ignore"!==n&&(!0===n||"error"===n))throw new r("LaTeX-incompatible input and strict mode is set to 'error': "+t+" ["+e+"]",a)}useStrictBehavior(e,t,r){var a=this.strict;if("function"==typeof a)try{a=a(e,t,r)}catch(n){a="error"}return!(!a||"ignore"===a)&&(!0===a||"error"===a)}isTrusted(e){if(e.url&&!e.protocol){var t=s.protocolFromUrl(e.url);if(null==t)return!1;e.protocol=t}var r="function"==typeof this.trust?this.trust(e):this.trust;return Boolean(r)}}class c{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return p[u[this.id]]}sub(){return p[d[this.id]]}fracNum(){return p[g[this.id]]}fracDen(){return p[f[this.id]]}cramp(){return p[v[this.id]]}text(){return p[b[this.id]]}isTight(){return this.size>=2}}var p=[new c(0,0,!1),new c(1,0,!0),new c(2,1,!1),new c(3,1,!0),new c(4,2,!1),new c(5,2,!0),new c(6,3,!1),new c(7,3,!0)],u=[4,5,4,5,6,7,6,7],d=[5,5,5,5,7,7,7,7],g=[2,3,4,5,6,7,6,7],f=[3,3,5,5,7,7,7,7],v=[1,1,3,3,5,5,7,7],b=[0,1,2,3,2,3,2,3],y={DISPLAY:p[0],TEXT:p[2],SCRIPT:p[4],SCRIPTSCRIPT:p[6]},x=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];var w=[];function k(e){for(var t=0;t<w.length;t+=2)if(e>=w[t]&&e<=w[t+1])return!0;return!1}x.forEach((e=>e.blocks.forEach((e=>w.push(...e)))));var S=80,M={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"};class z{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return s.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map((e=>e.toText())).join("")}}var A={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},T={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},B={"Å":"A","Ð":"D","Þ":"o","å":"a","ð":"d","þ":"o","А":"A","Б":"B","В":"B","Г":"F","Д":"A","Е":"E","Ж":"K","З":"3","И":"N","Й":"N","К":"K","Л":"N","М":"M","Н":"H","О":"O","П":"N","Р":"P","С":"C","Т":"T","У":"y","Ф":"O","Х":"X","Ц":"U","Ч":"h","Ш":"W","Щ":"W","Ъ":"B","Ы":"X","Ь":"B","Э":"3","Ю":"X","Я":"R","а":"a","б":"b","в":"a","г":"r","д":"y","е":"e","ж":"m","з":"e","и":"n","й":"n","к":"n","л":"n","м":"m","н":"n","о":"o","п":"n","р":"p","с":"c","т":"o","у":"y","ф":"b","х":"x","ц":"n","ч":"n","ш":"w","щ":"w","ъ":"a","ы":"m","ь":"a","э":"e","ю":"m","я":"r"};function C(e,t,r){if(!A[t])throw new Error("Font metrics not found for font: "+t+".");var a=e.charCodeAt(0),n=A[t][a];if(!n&&e[0]in B&&(a=B[e[0]].charCodeAt(0),n=A[t][a]),n||"text"!==r||k(a)&&(n=A[t][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var N={};var q=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],I=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],R=function(e,t){return t.size<2?e:q[e-1][t.size-1]};class H{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||H.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=I[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new H(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:R(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:I[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=R(H.BASESIZE,e);return this.size===t&&this.textSize===H.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==H.BASESIZE?["sizing","reset-size"+this.size,"size"+H.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){var t;if(!N[t=e>=5?0:e>=3?1:2]){var r=N[t]={cssEmPerMu:T.quad[t]/18};for(var a in T)T.hasOwnProperty(a)&&(r[a]=T[a][t])}return N[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}H.BASESIZE=6;var O={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},E={ex:!0,em:!0,mu:!0},L=function(e){return"string"!=typeof e&&(e=e.unit),e in O||e in E||"ex"===e},D=function(e,t){var a;if(e.unit in O)a=O[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)a=t.fontMetrics().cssEmPerMu;else{var n;if(n=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)a=n.fontMetrics().xHeight;else{if("em"!==e.unit)throw new r("Invalid unit: '"+e.unit+"'");a=n.fontMetrics().quad}n!==t&&(a*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*a,t.maxSize)},V=function(e){return+e.toFixed(4)+"em"},P=function(e){return e.filter((e=>e)).join(" ")},F=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var a=t.getColor();a&&(this.style.color=a)}},G=function(e){var t=document.createElement(e);for(var r in t.className=P(this.classes),this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var a in this.attributes)this.attributes.hasOwnProperty(a)&&t.setAttribute(a,this.attributes[a]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t},U=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+s.escape(P(this.classes))+'"');var r="";for(var a in this.style)this.style.hasOwnProperty(a)&&(r+=s.hyphenate(a)+":"+this.style[a]+";");for(var n in r&&(t+=' style="'+s.escape(r)+'"'),this.attributes)this.attributes.hasOwnProperty(n)&&(t+=" "+n+'="'+s.escape(this.attributes[n])+'"');t+=">";for(var i=0;i<this.children.length;i++)t+=this.children[i].toMarkup();return t+="</"+e+">"};class Y{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,F.call(this,e,r,a),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return s.contains(this.classes,e)}toNode(){return G.call(this,"span")}toMarkup(){return U.call(this,"span")}}class W{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,F.call(this,t,a),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return s.contains(this.classes,e)}toNode(){return G.call(this,"a")}toMarkup(){return U.call(this,"a")}}class X{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return s.contains(this.classes,e)}toNode(){var e=document.createElement("img");for(var t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+s.escape(this.src)+'" alt="'+s.escape(this.alt)+'"',t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=s.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+s.escape(t)+'"'),e+="'/>"}}var _={"î":"ı̂","ï":"ı̈","í":"ı́","ì":"ı̀"};class j{constructor(e,t,r,a,n,i,o,s){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=a||0,this.skew=n||0,this.width=i||0,this.classes=o||[],this.style=s||{},this.maxFontSize=0;var l=function(e){for(var t=0;t<x.length;t++)for(var r=x[t],a=0;a<r.blocks.length;a++){var n=r.blocks[a];if(e>=n[0]&&e<=n[1])return r.name}return null}(this.text.charCodeAt(0));l&&this.classes.push(l+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=_[this.text])}hasClass(e){return s.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;for(var r in this.italic>0&&((t=document.createElement("span")).style.marginRight=V(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=P(this.classes)),this.style)this.style.hasOwnProperty(r)&&((t=t||document.createElement("span")).style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=s.escape(P(this.classes)),t+='"');var r="";for(var a in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(a)&&(r+=s.hyphenate(a)+":"+this.style[a]+";");r&&(e=!0,t+=' style="'+s.escape(r)+'"');var n=s.escape(this.text);return e?(t+=">",t+=n,t+="</span>"):n}}class ${constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+s.escape(this.attributes[t])+'"');e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>"}}class Z{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",M[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+s.escape(this.alternate)+'"/>':'<path d="'+s.escape(M[this.pathName])+'"/>'}}class K{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+s.escape(this.attributes[t])+'"');return e+="/>"}}function J(e){if(e instanceof j)return e;throw new Error("Expected symbolNode but got "+String(e)+".")}var Q={bin:1,close:1,inner:1,open:1,punct:1,rel:1},ee={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},te={math:{},text:{}};function re(e,t,r,a,n,i){te[e][n]={font:t,group:r,replace:a},i&&a&&(te[e][a]=te[e][n])}var ae="math",ne="text",ie="main",oe="ams",se="accent-token",le="bin",he="close",me="inner",ce="mathord",pe="op-token",ue="open",de="punct",ge="rel",fe="spacing",ve="textord";re(ae,ie,ge,"≡","\\equiv",!0),re(ae,ie,ge,"≺","\\prec",!0),re(ae,ie,ge,"≻","\\succ",!0),re(ae,ie,ge,"∼","\\sim",!0),re(ae,ie,ge,"⊥","\\perp"),re(ae,ie,ge,"⪯","\\preceq",!0),re(ae,ie,ge,"⪰","\\succeq",!0),re(ae,ie,ge,"≃","\\simeq",!0),re(ae,ie,ge,"∣","\\mid",!0),re(ae,ie,ge,"≪","\\ll",!0),re(ae,ie,ge,"≫","\\gg",!0),re(ae,ie,ge,"≍","\\asymp",!0),re(ae,ie,ge,"∥","\\parallel"),re(ae,ie,ge,"⋈","\\bowtie",!0),re(ae,ie,ge,"⌣","\\smile",!0),re(ae,ie,ge,"⊑","\\sqsubseteq",!0),re(ae,ie,ge,"⊒","\\sqsupseteq",!0),re(ae,ie,ge,"≐","\\doteq",!0),re(ae,ie,ge,"⌢","\\frown",!0),re(ae,ie,ge,"∋","\\ni",!0),re(ae,ie,ge,"∝","\\propto",!0),re(ae,ie,ge,"⊢","\\vdash",!0),re(ae,ie,ge,"⊣","\\dashv",!0),re(ae,ie,ge,"∋","\\owns"),re(ae,ie,de,".","\\ldotp"),re(ae,ie,de,"⋅","\\cdotp"),re(ae,ie,ve,"#","\\#"),re(ne,ie,ve,"#","\\#"),re(ae,ie,ve,"&","\\&"),re(ne,ie,ve,"&","\\&"),re(ae,ie,ve,"ℵ","\\aleph",!0),re(ae,ie,ve,"∀","\\forall",!0),re(ae,ie,ve,"ℏ","\\hbar",!0),re(ae,ie,ve,"∃","\\exists",!0),re(ae,ie,ve,"∇","\\nabla",!0),re(ae,ie,ve,"♭","\\flat",!0),re(ae,ie,ve,"ℓ","\\ell",!0),re(ae,ie,ve,"♮","\\natural",!0),re(ae,ie,ve,"♣","\\clubsuit",!0),re(ae,ie,ve,"℘","\\wp",!0),re(ae,ie,ve,"♯","\\sharp",!0),re(ae,ie,ve,"♢","\\diamondsuit",!0),re(ae,ie,ve,"ℜ","\\Re",!0),re(ae,ie,ve,"♡","\\heartsuit",!0),re(ae,ie,ve,"ℑ","\\Im",!0),re(ae,ie,ve,"♠","\\spadesuit",!0),re(ae,ie,ve,"§","\\S",!0),re(ne,ie,ve,"§","\\S"),re(ae,ie,ve,"¶","\\P",!0),re(ne,ie,ve,"¶","\\P"),re(ae,ie,ve,"†","\\dag"),re(ne,ie,ve,"†","\\dag"),re(ne,ie,ve,"†","\\textdagger"),re(ae,ie,ve,"‡","\\ddag"),re(ne,ie,ve,"‡","\\ddag"),re(ne,ie,ve,"‡","\\textdaggerdbl"),re(ae,ie,he,"⎱","\\rmoustache",!0),re(ae,ie,ue,"⎰","\\lmoustache",!0),re(ae,ie,he,"⟯","\\rgroup",!0),re(ae,ie,ue,"⟮","\\lgroup",!0),re(ae,ie,le,"∓","\\mp",!0),re(ae,ie,le,"⊖","\\ominus",!0),re(ae,ie,le,"⊎","\\uplus",!0),re(ae,ie,le,"⊓","\\sqcap",!0),re(ae,ie,le,"∗","\\ast"),re(ae,ie,le,"⊔","\\sqcup",!0),re(ae,ie,le,"◯","\\bigcirc",!0),re(ae,ie,le,"∙","\\bullet",!0),re(ae,ie,le,"‡","\\ddagger"),re(ae,ie,le,"≀","\\wr",!0),re(ae,ie,le,"⨿","\\amalg"),re(ae,ie,le,"&","\\And"),re(ae,ie,ge,"⟵","\\longleftarrow",!0),re(ae,ie,ge,"⇐","\\Leftarrow",!0),re(ae,ie,ge,"⟸","\\Longleftarrow",!0),re(ae,ie,ge,"⟶","\\longrightarrow",!0),re(ae,ie,ge,"⇒","\\Rightarrow",!0),re(ae,ie,ge,"⟹","\\Longrightarrow",!0),re(ae,ie,ge,"↔","\\leftrightarrow",!0),re(ae,ie,ge,"⟷","\\longleftrightarrow",!0),re(ae,ie,ge,"⇔","\\Leftrightarrow",!0),re(ae,ie,ge,"⟺","\\Longleftrightarrow",!0),re(ae,ie,ge,"↦","\\mapsto",!0),re(ae,ie,ge,"⟼","\\longmapsto",!0),re(ae,ie,ge,"↗","\\nearrow",!0),re(ae,ie,ge,"↩","\\hookleftarrow",!0),re(ae,ie,ge,"↪","\\hookrightarrow",!0),re(ae,ie,ge,"↘","\\searrow",!0),re(ae,ie,ge,"↼","\\leftharpoonup",!0),re(ae,ie,ge,"⇀","\\rightharpoonup",!0),re(ae,ie,ge,"↙","\\swarrow",!0),re(ae,ie,ge,"↽","\\leftharpoondown",!0),re(ae,ie,ge,"⇁","\\rightharpoondown",!0),re(ae,ie,ge,"↖","\\nwarrow",!0),re(ae,ie,ge,"⇌","\\rightleftharpoons",!0),re(ae,oe,ge,"≮","\\nless",!0),re(ae,oe,ge,"","\\@nleqslant"),re(ae,oe,ge,"","\\@nleqq"),re(ae,oe,ge,"⪇","\\lneq",!0),re(ae,oe,ge,"≨","\\lneqq",!0),re(ae,oe,ge,"","\\@lvertneqq"),re(ae,oe,ge,"⋦","\\lnsim",!0),re(ae,oe,ge,"⪉","\\lnapprox",!0),re(ae,oe,ge,"⊀","\\nprec",!0),re(ae,oe,ge,"⋠","\\npreceq",!0),re(ae,oe,ge,"⋨","\\precnsim",!0),re(ae,oe,ge,"⪹","\\precnapprox",!0),re(ae,oe,ge,"≁","\\nsim",!0),re(ae,oe,ge,"","\\@nshortmid"),re(ae,oe,ge,"∤","\\nmid",!0),re(ae,oe,ge,"⊬","\\nvdash",!0),re(ae,oe,ge,"⊭","\\nvDash",!0),re(ae,oe,ge,"⋪","\\ntriangleleft"),re(ae,oe,ge,"⋬","\\ntrianglelefteq",!0),re(ae,oe,ge,"⊊","\\subsetneq",!0),re(ae,oe,ge,"","\\@varsubsetneq"),re(ae,oe,ge,"⫋","\\subsetneqq",!0),re(ae,oe,ge,"","\\@varsubsetneqq"),re(ae,oe,ge,"≯","\\ngtr",!0),re(ae,oe,ge,"","\\@ngeqslant"),re(ae,oe,ge,"","\\@ngeqq"),re(ae,oe,ge,"⪈","\\gneq",!0),re(ae,oe,ge,"≩","\\gneqq",!0),re(ae,oe,ge,"","\\@gvertneqq"),re(ae,oe,ge,"⋧","\\gnsim",!0),re(ae,oe,ge,"⪊","\\gnapprox",!0),re(ae,oe,ge,"⊁","\\nsucc",!0),re(ae,oe,ge,"⋡","\\nsucceq",!0),re(ae,oe,ge,"⋩","\\succnsim",!0),re(ae,oe,ge,"⪺","\\succnapprox",!0),re(ae,oe,ge,"≆","\\ncong",!0),re(ae,oe,ge,"","\\@nshortparallel"),re(ae,oe,ge,"∦","\\nparallel",!0),re(ae,oe,ge,"⊯","\\nVDash",!0),re(ae,oe,ge,"⋫","\\ntriangleright"),re(ae,oe,ge,"⋭","\\ntrianglerighteq",!0),re(ae,oe,ge,"","\\@nsupseteqq"),re(ae,oe,ge,"⊋","\\supsetneq",!0),re(ae,oe,ge,"","\\@varsupsetneq"),re(ae,oe,ge,"⫌","\\supsetneqq",!0),re(ae,oe,ge,"","\\@varsupsetneqq"),re(ae,oe,ge,"⊮","\\nVdash",!0),re(ae,oe,ge,"⪵","\\precneqq",!0),re(ae,oe,ge,"⪶","\\succneqq",!0),re(ae,oe,ge,"","\\@nsubseteqq"),re(ae,oe,le,"⊴","\\unlhd"),re(ae,oe,le,"⊵","\\unrhd"),re(ae,oe,ge,"↚","\\nleftarrow",!0),re(ae,oe,ge,"↛","\\nrightarrow",!0),re(ae,oe,ge,"⇍","\\nLeftarrow",!0),re(ae,oe,ge,"⇏","\\nRightarrow",!0),re(ae,oe,ge,"↮","\\nleftrightarrow",!0),re(ae,oe,ge,"⇎","\\nLeftrightarrow",!0),re(ae,oe,ge,"△","\\vartriangle"),re(ae,oe,ve,"ℏ","\\hslash"),re(ae,oe,ve,"▽","\\triangledown"),re(ae,oe,ve,"◊","\\lozenge"),re(ae,oe,ve,"Ⓢ","\\circledS"),re(ae,oe,ve,"®","\\circledR"),re(ne,oe,ve,"®","\\circledR"),re(ae,oe,ve,"∡","\\measuredangle",!0),re(ae,oe,ve,"∄","\\nexists"),re(ae,oe,ve,"℧","\\mho"),re(ae,oe,ve,"Ⅎ","\\Finv",!0),re(ae,oe,ve,"⅁","\\Game",!0),re(ae,oe,ve,"‵","\\backprime"),re(ae,oe,ve,"▲","\\blacktriangle"),re(ae,oe,ve,"▼","\\blacktriangledown"),re(ae,oe,ve,"■","\\blacksquare"),re(ae,oe,ve,"⧫","\\blacklozenge"),re(ae,oe,ve,"★","\\bigstar"),re(ae,oe,ve,"∢","\\sphericalangle",!0),re(ae,oe,ve,"∁","\\complement",!0),re(ae,oe,ve,"ð","\\eth",!0),re(ne,ie,ve,"ð","ð"),re(ae,oe,ve,"╱","\\diagup"),re(ae,oe,ve,"╲","\\diagdown"),re(ae,oe,ve,"□","\\square"),re(ae,oe,ve,"□","\\Box"),re(ae,oe,ve,"◊","\\Diamond"),re(ae,oe,ve,"¥","\\yen",!0),re(ne,oe,ve,"¥","\\yen",!0),re(ae,oe,ve,"✓","\\checkmark",!0),re(ne,oe,ve,"✓","\\checkmark"),re(ae,oe,ve,"ℶ","\\beth",!0),re(ae,oe,ve,"ℸ","\\daleth",!0),re(ae,oe,ve,"ℷ","\\gimel",!0),re(ae,oe,ve,"ϝ","\\digamma",!0),re(ae,oe,ve,"ϰ","\\varkappa"),re(ae,oe,ue,"┌","\\@ulcorner",!0),re(ae,oe,he,"┐","\\@urcorner",!0),re(ae,oe,ue,"└","\\@llcorner",!0),re(ae,oe,he,"┘","\\@lrcorner",!0),re(ae,oe,ge,"≦","\\leqq",!0),re(ae,oe,ge,"⩽","\\leqslant",!0),re(ae,oe,ge,"⪕","\\eqslantless",!0),re(ae,oe,ge,"≲","\\lesssim",!0),re(ae,oe,ge,"⪅","\\lessapprox",!0),re(ae,oe,ge,"≊","\\approxeq",!0),re(ae,oe,le,"⋖","\\lessdot"),re(ae,oe,ge,"⋘","\\lll",!0),re(ae,oe,ge,"≶","\\lessgtr",!0),re(ae,oe,ge,"⋚","\\lesseqgtr",!0),re(ae,oe,ge,"⪋","\\lesseqqgtr",!0),re(ae,oe,ge,"≑","\\doteqdot"),re(ae,oe,ge,"≓","\\risingdotseq",!0),re(ae,oe,ge,"≒","\\fallingdotseq",!0),re(ae,oe,ge,"∽","\\backsim",!0),re(ae,oe,ge,"⋍","\\backsimeq",!0),re(ae,oe,ge,"⫅","\\subseteqq",!0),re(ae,oe,ge,"⋐","\\Subset",!0),re(ae,oe,ge,"⊏","\\sqsubset",!0),re(ae,oe,ge,"≼","\\preccurlyeq",!0),re(ae,oe,ge,"⋞","\\curlyeqprec",!0),re(ae,oe,ge,"≾","\\precsim",!0),re(ae,oe,ge,"⪷","\\precapprox",!0),re(ae,oe,ge,"⊲","\\vartriangleleft"),re(ae,oe,ge,"⊴","\\trianglelefteq"),re(ae,oe,ge,"⊨","\\vDash",!0),re(ae,oe,ge,"⊪","\\Vvdash",!0),re(ae,oe,ge,"⌣","\\smallsmile"),re(ae,oe,ge,"⌢","\\smallfrown"),re(ae,oe,ge,"≏","\\bumpeq",!0),re(ae,oe,ge,"≎","\\Bumpeq",!0),re(ae,oe,ge,"≧","\\geqq",!0),re(ae,oe,ge,"⩾","\\geqslant",!0),re(ae,oe,ge,"⪖","\\eqslantgtr",!0),re(ae,oe,ge,"≳","\\gtrsim",!0),re(ae,oe,ge,"⪆","\\gtrapprox",!0),re(ae,oe,le,"⋗","\\gtrdot"),re(ae,oe,ge,"⋙","\\ggg",!0),re(ae,oe,ge,"≷","\\gtrless",!0),re(ae,oe,ge,"⋛","\\gtreqless",!0),re(ae,oe,ge,"⪌","\\gtreqqless",!0),re(ae,oe,ge,"≖","\\eqcirc",!0),re(ae,oe,ge,"≗","\\circeq",!0),re(ae,oe,ge,"≜","\\triangleq",!0),re(ae,oe,ge,"∼","\\thicksim"),re(ae,oe,ge,"≈","\\thickapprox"),re(ae,oe,ge,"⫆","\\supseteqq",!0),re(ae,oe,ge,"⋑","\\Supset",!0),re(ae,oe,ge,"⊐","\\sqsupset",!0),re(ae,oe,ge,"≽","\\succcurlyeq",!0),re(ae,oe,ge,"⋟","\\curlyeqsucc",!0),re(ae,oe,ge,"≿","\\succsim",!0),re(ae,oe,ge,"⪸","\\succapprox",!0),re(ae,oe,ge,"⊳","\\vartriangleright"),re(ae,oe,ge,"⊵","\\trianglerighteq"),re(ae,oe,ge,"⊩","\\Vdash",!0),re(ae,oe,ge,"∣","\\shortmid"),re(ae,oe,ge,"∥","\\shortparallel"),re(ae,oe,ge,"≬","\\between",!0),re(ae,oe,ge,"⋔","\\pitchfork",!0),re(ae,oe,ge,"∝","\\varpropto"),re(ae,oe,ge,"◀","\\blacktriangleleft"),re(ae,oe,ge,"∴","\\therefore",!0),re(ae,oe,ge,"∍","\\backepsilon"),re(ae,oe,ge,"▶","\\blacktriangleright"),re(ae,oe,ge,"∵","\\because",!0),re(ae,oe,ge,"⋘","\\llless"),re(ae,oe,ge,"⋙","\\gggtr"),re(ae,oe,le,"⊲","\\lhd"),re(ae,oe,le,"⊳","\\rhd"),re(ae,oe,ge,"≂","\\eqsim",!0),re(ae,ie,ge,"⋈","\\Join"),re(ae,oe,ge,"≑","\\Doteq",!0),re(ae,oe,le,"∔","\\dotplus",!0),re(ae,oe,le,"∖","\\smallsetminus"),re(ae,oe,le,"⋒","\\Cap",!0),re(ae,oe,le,"⋓","\\Cup",!0),re(ae,oe,le,"⩞","\\doublebarwedge",!0),re(ae,oe,le,"⊟","\\boxminus",!0),re(ae,oe,le,"⊞","\\boxplus",!0),re(ae,oe,le,"⋇","\\divideontimes",!0),re(ae,oe,le,"⋉","\\ltimes",!0),re(ae,oe,le,"⋊","\\rtimes",!0),re(ae,oe,le,"⋋","\\leftthreetimes",!0),re(ae,oe,le,"⋌","\\rightthreetimes",!0),re(ae,oe,le,"⋏","\\curlywedge",!0),re(ae,oe,le,"⋎","\\curlyvee",!0),re(ae,oe,le,"⊝","\\circleddash",!0),re(ae,oe,le,"⊛","\\circledast",!0),re(ae,oe,le,"⋅","\\centerdot"),re(ae,oe,le,"⊺","\\intercal",!0),re(ae,oe,le,"⋒","\\doublecap"),re(ae,oe,le,"⋓","\\doublecup"),re(ae,oe,le,"⊠","\\boxtimes",!0),re(ae,oe,ge,"⇢","\\dashrightarrow",!0),re(ae,oe,ge,"⇠","\\dashleftarrow",!0),re(ae,oe,ge,"⇇","\\leftleftarrows",!0),re(ae,oe,ge,"⇆","\\leftrightarrows",!0),re(ae,oe,ge,"⇚","\\Lleftarrow",!0),re(ae,oe,ge,"↞","\\twoheadleftarrow",!0),re(ae,oe,ge,"↢","\\leftarrowtail",!0),re(ae,oe,ge,"↫","\\looparrowleft",!0),re(ae,oe,ge,"⇋","\\leftrightharpoons",!0),re(ae,oe,ge,"↶","\\curvearrowleft",!0),re(ae,oe,ge,"↺","\\circlearrowleft",!0),re(ae,oe,ge,"↰","\\Lsh",!0),re(ae,oe,ge,"⇈","\\upuparrows",!0),re(ae,oe,ge,"↿","\\upharpoonleft",!0),re(ae,oe,ge,"⇃","\\downharpoonleft",!0),re(ae,ie,ge,"⊶","\\origof",!0),re(ae,ie,ge,"⊷","\\imageof",!0),re(ae,oe,ge,"⊸","\\multimap",!0),re(ae,oe,ge,"↭","\\leftrightsquigarrow",!0),re(ae,oe,ge,"⇉","\\rightrightarrows",!0),re(ae,oe,ge,"⇄","\\rightleftarrows",!0),re(ae,oe,ge,"↠","\\twoheadrightarrow",!0),re(ae,oe,ge,"↣","\\rightarrowtail",!0),re(ae,oe,ge,"↬","\\looparrowright",!0),re(ae,oe,ge,"↷","\\curvearrowright",!0),re(ae,oe,ge,"↻","\\circlearrowright",!0),re(ae,oe,ge,"↱","\\Rsh",!0),re(ae,oe,ge,"⇊","\\downdownarrows",!0),re(ae,oe,ge,"↾","\\upharpoonright",!0),re(ae,oe,ge,"⇂","\\downharpoonright",!0),re(ae,oe,ge,"⇝","\\rightsquigarrow",!0),re(ae,oe,ge,"⇝","\\leadsto"),re(ae,oe,ge,"⇛","\\Rrightarrow",!0),re(ae,oe,ge,"↾","\\restriction"),re(ae,ie,ve,"‘","`"),re(ae,ie,ve,"$","\\$"),re(ne,ie,ve,"$","\\$"),re(ne,ie,ve,"$","\\textdollar"),re(ae,ie,ve,"%","\\%"),re(ne,ie,ve,"%","\\%"),re(ae,ie,ve,"_","\\_"),re(ne,ie,ve,"_","\\_"),re(ne,ie,ve,"_","\\textunderscore"),re(ae,ie,ve,"∠","\\angle",!0),re(ae,ie,ve,"∞","\\infty",!0),re(ae,ie,ve,"′","\\prime"),re(ae,ie,ve,"△","\\triangle"),re(ae,ie,ve,"Γ","\\Gamma",!0),re(ae,ie,ve,"Δ","\\Delta",!0),re(ae,ie,ve,"Θ","\\Theta",!0),re(ae,ie,ve,"Λ","\\Lambda",!0),re(ae,ie,ve,"Ξ","\\Xi",!0),re(ae,ie,ve,"Π","\\Pi",!0),re(ae,ie,ve,"Σ","\\Sigma",!0),re(ae,ie,ve,"Υ","\\Upsilon",!0),re(ae,ie,ve,"Φ","\\Phi",!0),re(ae,ie,ve,"Ψ","\\Psi",!0),re(ae,ie,ve,"Ω","\\Omega",!0),re(ae,ie,ve,"A","Α"),re(ae,ie,ve,"B","Β"),re(ae,ie,ve,"E","Ε"),re(ae,ie,ve,"Z","Ζ"),re(ae,ie,ve,"H","Η"),re(ae,ie,ve,"I","Ι"),re(ae,ie,ve,"K","Κ"),re(ae,ie,ve,"M","Μ"),re(ae,ie,ve,"N","Ν"),re(ae,ie,ve,"O","Ο"),re(ae,ie,ve,"P","Ρ"),re(ae,ie,ve,"T","Τ"),re(ae,ie,ve,"X","Χ"),re(ae,ie,ve,"¬","\\neg",!0),re(ae,ie,ve,"¬","\\lnot"),re(ae,ie,ve,"⊤","\\top"),re(ae,ie,ve,"⊥","\\bot"),re(ae,ie,ve,"∅","\\emptyset"),re(ae,oe,ve,"∅","\\varnothing"),re(ae,ie,ce,"α","\\alpha",!0),re(ae,ie,ce,"β","\\beta",!0),re(ae,ie,ce,"γ","\\gamma",!0),re(ae,ie,ce,"δ","\\delta",!0),re(ae,ie,ce,"ϵ","\\epsilon",!0),re(ae,ie,ce,"ζ","\\zeta",!0),re(ae,ie,ce,"η","\\eta",!0),re(ae,ie,ce,"θ","\\theta",!0),re(ae,ie,ce,"ι","\\iota",!0),re(ae,ie,ce,"κ","\\kappa",!0),re(ae,ie,ce,"λ","\\lambda",!0),re(ae,ie,ce,"μ","\\mu",!0),re(ae,ie,ce,"ν","\\nu",!0),re(ae,ie,ce,"ξ","\\xi",!0),re(ae,ie,ce,"ο","\\omicron",!0),re(ae,ie,ce,"π","\\pi",!0),re(ae,ie,ce,"ρ","\\rho",!0),re(ae,ie,ce,"σ","\\sigma",!0),re(ae,ie,ce,"τ","\\tau",!0),re(ae,ie,ce,"υ","\\upsilon",!0),re(ae,ie,ce,"ϕ","\\phi",!0),re(ae,ie,ce,"χ","\\chi",!0),re(ae,ie,ce,"ψ","\\psi",!0),re(ae,ie,ce,"ω","\\omega",!0),re(ae,ie,ce,"ε","\\varepsilon",!0),re(ae,ie,ce,"ϑ","\\vartheta",!0),re(ae,ie,ce,"ϖ","\\varpi",!0),re(ae,ie,ce,"ϱ","\\varrho",!0),re(ae,ie,ce,"ς","\\varsigma",!0),re(ae,ie,ce,"φ","\\varphi",!0),re(ae,ie,le,"∗","*",!0),re(ae,ie,le,"+","+"),re(ae,ie,le,"−","-",!0),re(ae,ie,le,"⋅","\\cdot",!0),re(ae,ie,le,"∘","\\circ",!0),re(ae,ie,le,"÷","\\div",!0),re(ae,ie,le,"±","\\pm",!0),re(ae,ie,le,"×","\\times",!0),re(ae,ie,le,"∩","\\cap",!0),re(ae,ie,le,"∪","\\cup",!0),re(ae,ie,le,"∖","\\setminus",!0),re(ae,ie,le,"∧","\\land"),re(ae,ie,le,"∨","\\lor"),re(ae,ie,le,"∧","\\wedge",!0),re(ae,ie,le,"∨","\\vee",!0),re(ae,ie,ve,"√","\\surd"),re(ae,ie,ue,"⟨","\\langle",!0),re(ae,ie,ue,"∣","\\lvert"),re(ae,ie,ue,"∥","\\lVert"),re(ae,ie,he,"?","?"),re(ae,ie,he,"!","!"),re(ae,ie,he,"⟩","\\rangle",!0),re(ae,ie,he,"∣","\\rvert"),re(ae,ie,he,"∥","\\rVert"),re(ae,ie,ge,"=","="),re(ae,ie,ge,":",":"),re(ae,ie,ge,"≈","\\approx",!0),re(ae,ie,ge,"≅","\\cong",!0),re(ae,ie,ge,"≥","\\ge"),re(ae,ie,ge,"≥","\\geq",!0),re(ae,ie,ge,"←","\\gets"),re(ae,ie,ge,">","\\gt",!0),re(ae,ie,ge,"∈","\\in",!0),re(ae,ie,ge,"","\\@not"),re(ae,ie,ge,"⊂","\\subset",!0),re(ae,ie,ge,"⊃","\\supset",!0),re(ae,ie,ge,"⊆","\\subseteq",!0),re(ae,ie,ge,"⊇","\\supseteq",!0),re(ae,oe,ge,"⊈","\\nsubseteq",!0),re(ae,oe,ge,"⊉","\\nsupseteq",!0),re(ae,ie,ge,"⊨","\\models"),re(ae,ie,ge,"←","\\leftarrow",!0),re(ae,ie,ge,"≤","\\le"),re(ae,ie,ge,"≤","\\leq",!0),re(ae,ie,ge,"<","\\lt",!0),re(ae,ie,ge,"→","\\rightarrow",!0),re(ae,ie,ge,"→","\\to"),re(ae,oe,ge,"≱","\\ngeq",!0),re(ae,oe,ge,"≰","\\nleq",!0),re(ae,ie,fe," ","\\ "),re(ae,ie,fe," ","\\space"),re(ae,ie,fe," ","\\nobreakspace"),re(ne,ie,fe," ","\\ "),re(ne,ie,fe," "," "),re(ne,ie,fe," ","\\space"),re(ne,ie,fe," ","\\nobreakspace"),re(ae,ie,fe,null,"\\nobreak"),re(ae,ie,fe,null,"\\allowbreak"),re(ae,ie,de,",",","),re(ae,ie,de,";",";"),re(ae,oe,le,"⊼","\\barwedge",!0),re(ae,oe,le,"⊻","\\veebar",!0),re(ae,ie,le,"⊙","\\odot",!0),re(ae,ie,le,"⊕","\\oplus",!0),re(ae,ie,le,"⊗","\\otimes",!0),re(ae,ie,ve,"∂","\\partial",!0),re(ae,ie,le,"⊘","\\oslash",!0),re(ae,oe,le,"⊚","\\circledcirc",!0),re(ae,oe,le,"⊡","\\boxdot",!0),re(ae,ie,le,"△","\\bigtriangleup"),re(ae,ie,le,"▽","\\bigtriangledown"),re(ae,ie,le,"†","\\dagger"),re(ae,ie,le,"⋄","\\diamond"),re(ae,ie,le,"⋆","\\star"),re(ae,ie,le,"◃","\\triangleleft"),re(ae,ie,le,"▹","\\triangleright"),re(ae,ie,ue,"{","\\{"),re(ne,ie,ve,"{","\\{"),re(ne,ie,ve,"{","\\textbraceleft"),re(ae,ie,he,"}","\\}"),re(ne,ie,ve,"}","\\}"),re(ne,ie,ve,"}","\\textbraceright"),re(ae,ie,ue,"{","\\lbrace"),re(ae,ie,he,"}","\\rbrace"),re(ae,ie,ue,"[","\\lbrack",!0),re(ne,ie,ve,"[","\\lbrack",!0),re(ae,ie,he,"]","\\rbrack",!0),re(ne,ie,ve,"]","\\rbrack",!0),re(ae,ie,ue,"(","\\lparen",!0),re(ae,ie,he,")","\\rparen",!0),re(ne,ie,ve,"<","\\textless",!0),re(ne,ie,ve,">","\\textgreater",!0),re(ae,ie,ue,"⌊","\\lfloor",!0),re(ae,ie,he,"⌋","\\rfloor",!0),re(ae,ie,ue,"⌈","\\lceil",!0),re(ae,ie,he,"⌉","\\rceil",!0),re(ae,ie,ve,"\\","\\backslash"),re(ae,ie,ve,"∣","|"),re(ae,ie,ve,"∣","\\vert"),re(ne,ie,ve,"|","\\textbar",!0),re(ae,ie,ve,"∥","\\|"),re(ae,ie,ve,"∥","\\Vert"),re(ne,ie,ve,"∥","\\textbardbl"),re(ne,ie,ve,"~","\\textasciitilde"),re(ne,ie,ve,"\\","\\textbackslash"),re(ne,ie,ve,"^","\\textasciicircum"),re(ae,ie,ge,"↑","\\uparrow",!0),re(ae,ie,ge,"⇑","\\Uparrow",!0),re(ae,ie,ge,"↓","\\downarrow",!0),re(ae,ie,ge,"⇓","\\Downarrow",!0),re(ae,ie,ge,"↕","\\updownarrow",!0),re(ae,ie,ge,"⇕","\\Updownarrow",!0),re(ae,ie,pe,"∐","\\coprod"),re(ae,ie,pe,"⋁","\\bigvee"),re(ae,ie,pe,"⋀","\\bigwedge"),re(ae,ie,pe,"⨄","\\biguplus"),re(ae,ie,pe,"⋂","\\bigcap"),re(ae,ie,pe,"⋃","\\bigcup"),re(ae,ie,pe,"∫","\\int"),re(ae,ie,pe,"∫","\\intop"),re(ae,ie,pe,"∬","\\iint"),re(ae,ie,pe,"∭","\\iiint"),re(ae,ie,pe,"∏","\\prod"),re(ae,ie,pe,"∑","\\sum"),re(ae,ie,pe,"⨂","\\bigotimes"),re(ae,ie,pe,"⨁","\\bigoplus"),re(ae,ie,pe,"⨀","\\bigodot"),re(ae,ie,pe,"∮","\\oint"),re(ae,ie,pe,"∯","\\oiint"),re(ae,ie,pe,"∰","\\oiiint"),re(ae,ie,pe,"⨆","\\bigsqcup"),re(ae,ie,pe,"∫","\\smallint"),re(ne,ie,me,"…","\\textellipsis"),re(ae,ie,me,"…","\\mathellipsis"),re(ne,ie,me,"…","\\ldots",!0),re(ae,ie,me,"…","\\ldots",!0),re(ae,ie,me,"⋯","\\@cdots",!0),re(ae,ie,me,"⋱","\\ddots",!0),re(ae,ie,ve,"⋮","\\varvdots"),re(ae,ie,se,"ˊ","\\acute"),re(ae,ie,se,"ˋ","\\grave"),re(ae,ie,se,"¨","\\ddot"),re(ae,ie,se,"~","\\tilde"),re(ae,ie,se,"ˉ","\\bar"),re(ae,ie,se,"˘","\\breve"),re(ae,ie,se,"ˇ","\\check"),re(ae,ie,se,"^","\\hat"),re(ae,ie,se,"⃗","\\vec"),re(ae,ie,se,"˙","\\dot"),re(ae,ie,se,"˚","\\mathring"),re(ae,ie,ce,"","\\@imath"),re(ae,ie,ce,"","\\@jmath"),re(ae,ie,ve,"ı","ı"),re(ae,ie,ve,"ȷ","ȷ"),re(ne,ie,ve,"ı","\\i",!0),re(ne,ie,ve,"ȷ","\\j",!0),re(ne,ie,ve,"ß","\\ss",!0),re(ne,ie,ve,"æ","\\ae",!0),re(ne,ie,ve,"œ","\\oe",!0),re(ne,ie,ve,"ø","\\o",!0),re(ne,ie,ve,"Æ","\\AE",!0),re(ne,ie,ve,"Œ","\\OE",!0),re(ne,ie,ve,"Ø","\\O",!0),re(ne,ie,se,"ˊ","\\'"),re(ne,ie,se,"ˋ","\\`"),re(ne,ie,se,"ˆ","\\^"),re(ne,ie,se,"˜","\\~"),re(ne,ie,se,"ˉ","\\="),re(ne,ie,se,"˘","\\u"),re(ne,ie,se,"˙","\\."),re(ne,ie,se,"¸","\\c"),re(ne,ie,se,"˚","\\r"),re(ne,ie,se,"ˇ","\\v"),re(ne,ie,se,"¨",'\\"'),re(ne,ie,se,"˝","\\H"),re(ne,ie,se,"◯","\\textcircled");var be={"--":!0,"---":!0,"``":!0,"''":!0};re(ne,ie,ve,"–","--",!0),re(ne,ie,ve,"–","\\textendash"),re(ne,ie,ve,"—","---",!0),re(ne,ie,ve,"—","\\textemdash"),re(ne,ie,ve,"‘","`",!0),re(ne,ie,ve,"‘","\\textquoteleft"),re(ne,ie,ve,"’","'",!0),re(ne,ie,ve,"’","\\textquoteright"),re(ne,ie,ve,"“","``",!0),re(ne,ie,ve,"“","\\textquotedblleft"),re(ne,ie,ve,"”","''",!0),re(ne,ie,ve,"”","\\textquotedblright"),re(ae,ie,ve,"°","\\degree",!0),re(ne,ie,ve,"°","\\degree"),re(ne,ie,ve,"°","\\textdegree",!0),re(ae,ie,ve,"£","\\pounds"),re(ae,ie,ve,"£","\\mathsterling",!0),re(ne,ie,ve,"£","\\pounds"),re(ne,ie,ve,"£","\\textsterling",!0),re(ae,oe,ve,"✠","\\maltese"),re(ne,oe,ve,"✠","\\maltese");for(var ye='0123456789/@."',xe=0;xe<14;xe++){var we=ye.charAt(xe);re(ae,ie,ve,we,we)}for(var ke='0123456789!@*()-=+";:?/.,',Se=0;Se<25;Se++){var Me=ke.charAt(Se);re(ne,ie,ve,Me,Me)}for(var ze="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",Ae=0;Ae<52;Ae++){var Te=ze.charAt(Ae);re(ae,ie,ce,Te,Te),re(ne,ie,ve,Te,Te)}re(ae,oe,ve,"C","ℂ"),re(ne,oe,ve,"C","ℂ"),re(ae,oe,ve,"H","ℍ"),re(ne,oe,ve,"H","ℍ"),re(ae,oe,ve,"N","ℕ"),re(ne,oe,ve,"N","ℕ"),re(ae,oe,ve,"P","ℙ"),re(ne,oe,ve,"P","ℙ"),re(ae,oe,ve,"Q","ℚ"),re(ne,oe,ve,"Q","ℚ"),re(ae,oe,ve,"R","ℝ"),re(ne,oe,ve,"R","ℝ"),re(ae,oe,ve,"Z","ℤ"),re(ne,oe,ve,"Z","ℤ"),re(ae,ie,ce,"h","ℎ"),re(ne,ie,ce,"h","ℎ");for(var Be="",Ce=0;Ce<52;Ce++){var Ne=ze.charAt(Ce);re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56320+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56372+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56424+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56580+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56684+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56736+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56788+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56840+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56944+Ce)),re(ne,ie,ve,Ne,Be),Ce<26&&(re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56632+Ce)),re(ne,ie,ve,Ne,Be),re(ae,ie,ce,Ne,Be=String.fromCharCode(55349,56476+Ce)),re(ne,ie,ve,Ne,Be))}re(ae,ie,ce,"k",Be=String.fromCharCode(55349,56668)),re(ne,ie,ve,"k",Be);for(var qe=0;qe<10;qe++){var Ie=qe.toString();re(ae,ie,ce,Ie,Be=String.fromCharCode(55349,57294+qe)),re(ne,ie,ve,Ie,Be),re(ae,ie,ce,Ie,Be=String.fromCharCode(55349,57314+qe)),re(ne,ie,ve,Ie,Be),re(ae,ie,ce,Ie,Be=String.fromCharCode(55349,57324+qe)),re(ne,ie,ve,Ie,Be),re(ae,ie,ce,Ie,Be=String.fromCharCode(55349,57334+qe)),re(ne,ie,ve,Ie,Be)}for(var Re="ÐÞþ",He=0;He<3;He++){var Oe=Re.charAt(He);re(ae,ie,ce,Oe,Oe),re(ne,ie,ve,Oe,Oe)}var Ee=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Le=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],De=function(e,t,r){return te[r][e]&&te[r][e].replace&&(e=te[r][e].replace),{value:e,metrics:C(e,t,r)}},Ve=function(e,t,r,a,n){var i,o=De(e,t,r),s=o.metrics;if(e=o.value,s){var l=s.italic;("text"===r||a&&"mathit"===a.font)&&(l=0),i=new j(e,s.height,s.depth,l,s.skew,s.width,n)}else i=new j(e,0,0,0,0,0,n);if(a){i.maxFontSize=a.sizeMultiplier,a.style.isTight()&&i.classes.push("mtight");var h=a.getColor();h&&(i.style.color=h)}return i},Pe=(e,t)=>{if(P(e.classes)!==P(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){var r=e.classes[0];if("mbin"===r||"mord"===r)return!1}for(var a in e.style)if(e.style.hasOwnProperty(a)&&e.style[a]!==t.style[a])return!1;for(var n in t.style)if(t.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;return!0},Fe=function(e){for(var t=0,r=0,a=0,n=0;n<e.children.length;n++){var i=e.children[n];i.height>t&&(t=i.height),i.depth>r&&(r=i.depth),i.maxFontSize>a&&(a=i.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=a},Ge=function(e,t,r,a){var n=new Y(e,t,r,a);return Fe(n),n},Ue=(e,t,r,a)=>new Y(e,t,r,a),Ye=function(e){var t=new z(e);return Fe(t),t},We=function(e,t,r){var a="";switch(e){case"amsrm":a="AMS";break;case"textrm":a="Main";break;case"textsf":a="SansSerif";break;case"texttt":a="Typewriter";break;default:a=e}return a+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},Xe={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},_e={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},je={fontMap:Xe,makeSymbol:Ve,mathsym:function(e,t,r,a){return void 0===a&&(a=[]),"boldsymbol"===r.font&&De(e,"Main-Bold",t).metrics?Ve(e,"Main-Bold",t,r,a.concat(["mathbf"])):"\\"===e||"main"===te[t][e].font?Ve(e,"Main-Regular",t,r,a):Ve(e,"AMS-Regular",t,r,a.concat(["amsrm"]))},makeSpan:Ge,makeSvgSpan:Ue,makeLineSpan:function(e,t,r){var a=Ge([e],[],t);return a.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),a.style.borderBottomWidth=V(a.height),a.maxFontSize=1,a},makeAnchor:function(e,t,r,a){var n=new W(e,t,r,a);return Fe(n),n},makeFragment:Ye,wrapFragment:function(e,t){return e instanceof z?Ge([],[e],t):e},makeVList:function(e,t){for(var{children:r,depth:a}=function(e){if("individualShift"===e.positionType){for(var t=e.children,r=[t[0]],a=-t[0].shift-t[0].elem.depth,n=a,i=1;i<t.length;i++){var o=-t[i].shift-n-t[i].elem.depth,s=o-(t[i-1].elem.height+t[i-1].elem.depth);n+=o,r.push({type:"kern",size:s}),r.push(t[i])}return{children:r,depth:a}}var l;if("top"===e.positionType){for(var h=e.positionData,m=0;m<e.children.length;m++){var c=e.children[m];h-="kern"===c.type?c.size:c.elem.height+c.elem.depth}l=h}else if("bottom"===e.positionType)l=-e.positionData;else{var p=e.children[0];if("elem"!==p.type)throw new Error('First child must have type "elem".');if("shift"===e.positionType)l=-p.elem.depth-e.positionData;else{if("firstBaseline"!==e.positionType)throw new Error("Invalid positionType "+e.positionType+".");l=-p.elem.depth}}return{children:e.children,depth:l}}(e),n=0,i=0;i<r.length;i++){var o=r[i];if("elem"===o.type){var s=o.elem;n=Math.max(n,s.maxFontSize,s.height)}}n+=2;var l=Ge(["pstrut"],[]);l.style.height=V(n);for(var h=[],m=a,c=a,p=a,u=0;u<r.length;u++){var d=r[u];if("kern"===d.type)p+=d.size;else{var g=d.elem,f=d.wrapperClasses||[],v=d.wrapperStyle||{},b=Ge(f,[l,g],void 0,v);b.style.top=V(-n-p-g.depth),d.marginLeft&&(b.style.marginLeft=d.marginLeft),d.marginRight&&(b.style.marginRight=d.marginRight),h.push(b),p+=g.height+g.depth}m=Math.min(m,p),c=Math.max(c,p)}var y,x=Ge(["vlist"],h);if(x.style.height=V(c),m<0){var w=Ge([],[]),k=Ge(["vlist"],[w]);k.style.height=V(-m);var S=Ge(["vlist-s"],[new j("​")]);y=[Ge(["vlist-r"],[x,S]),Ge(["vlist-r"],[k])]}else y=[Ge(["vlist-r"],[x])];var M=Ge(["vlist-t"],y);return 2===y.length&&M.classes.push("vlist-t2"),M.height=c,M.depth=-m,M},makeOrd:function(e,t,a){var n=e.mode,i=e.text,o=["mord"],s="math"===n||"text"===n&&t.font,l=s?t.font:t.fontFamily,h="",m="";if(55349===i.charCodeAt(0)&&([h,m]=function(e,t){var a=1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536,n="math"===t?0:1;if(119808<=a&&a<120484){var i=Math.floor((a-119808)/26);return[Ee[i][2],Ee[i][n]]}if(120782<=a&&a<=120831){var o=Math.floor((a-120782)/10);return[Le[o][2],Le[o][n]]}if(120485===a||120486===a)return[Ee[0][2],Ee[0][n]];if(120486<a&&a<120782)return["",""];throw new r("Unsupported character: "+e)}(i,n)),h.length>0)return Ve(i,h,n,t,o.concat(m));if(l){var c,p;if("boldsymbol"===l){var u=function(e,t,r,a,n){return"textord"!==n&&De(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}}(i,n,0,0,a);c=u.fontName,p=[u.fontClass]}else s?(c=Xe[l].fontName,p=[l]):(c=We(l,t.fontWeight,t.fontShape),p=[l,t.fontWeight,t.fontShape]);if(De(i,c,n).metrics)return Ve(i,c,n,t,o.concat(p));if(be.hasOwnProperty(i)&&"Typewriter"===c.slice(0,10)){for(var d=[],g=0;g<i.length;g++)d.push(Ve(i[g],c,n,t,o.concat(p)));return Ye(d)}}if("mathord"===a)return Ve(i,"Math-Italic",n,t,o.concat(["mathnormal"]));if("textord"===a){var f=te[n][i]&&te[n][i].font;if("ams"===f){var v=We("amsrm",t.fontWeight,t.fontShape);return Ve(i,v,n,t,o.concat("amsrm",t.fontWeight,t.fontShape))}if("main"!==f&&f){var b=We(f,t.fontWeight,t.fontShape);return Ve(i,b,n,t,o.concat(b,t.fontWeight,t.fontShape))}var y=We("textrm",t.fontWeight,t.fontShape);return Ve(i,y,n,t,o.concat(t.fontWeight,t.fontShape))}throw new Error("unexpected type: "+a+" in makeOrd")},makeGlue:(e,t)=>{var r=Ge(["mspace"],[],t),a=D(e,t);return r.style.marginRight=V(a),r},staticSvg:function(e,t){var[r,a,n]=_e[e],i=new Z(r),o=new $([i],{width:V(a),height:V(n),style:"width:"+V(a),viewBox:"0 0 "+1e3*a+" "+1e3*n,preserveAspectRatio:"xMinYMin"}),s=Ue(["overlay"],[o],t);return s.height=n,s.style.height=V(n),s.style.width=V(a),s},svgData:_e,tryCombineChars:e=>{for(var t=0;t<e.length-1;t++){var r=e[t],a=e[t+1];r instanceof j&&a instanceof j&&Pe(r,a)&&(r.text+=a.text,r.height=Math.max(r.height,a.height),r.depth=Math.max(r.depth,a.depth),r.italic=a.italic,e.splice(t+1,1),t--)}return e}},$e={number:3,unit:"mu"},Ze={number:4,unit:"mu"},Ke={number:5,unit:"mu"},Je={mord:{mop:$e,mbin:Ze,mrel:Ke,minner:$e},mop:{mord:$e,mop:$e,mrel:Ke,minner:$e},mbin:{mord:Ze,mop:Ze,mopen:Ze,minner:Ze},mrel:{mord:Ke,mop:Ke,mopen:Ke,minner:Ke},mopen:{},mclose:{mop:$e,mbin:Ze,mrel:Ke,minner:$e},mpunct:{mord:$e,mop:$e,mrel:Ke,mopen:$e,mclose:$e,mpunct:$e,minner:$e},minner:{mord:$e,mop:$e,mbin:Ze,mrel:Ke,mopen:$e,mpunct:$e,minner:$e}},Qe={mord:{mop:$e},mop:{mord:$e,mop:$e},mbin:{},mrel:{},mopen:{},mclose:{mop:$e},mpunct:{},minner:{mop:$e}},et={},tt={},rt={};function at(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:o}=e,s={type:t,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:void 0===a.allowedInMath||a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:n},l=0;l<r.length;++l)et[r[l]]=s;t&&(i&&(tt[t]=i),o&&(rt[t]=o))}function nt(e){var{type:t,htmlBuilder:r,mathmlBuilder:a}=e;at({type:t,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:a})}var it=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},ot=function(e){return"ordgroup"===e.type?e.body:[e]},st=je.makeSpan,lt=["leftmost","mbin","mopen","mrel","mop","mpunct"],ht=["rightmost","mrel","mclose","mpunct"],mt={display:y.DISPLAY,text:y.TEXT,script:y.SCRIPT,scriptscript:y.SCRIPTSCRIPT},ct={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},pt=function(e,t,r,a){void 0===a&&(a=[null,null]);for(var n=[],i=0;i<e.length;i++){var o=bt(e[i],t);if(o instanceof z){var l=o.children;n.push(...l)}else n.push(o)}if(je.tryCombineChars(n),!r)return n;var h=t;if(1===e.length){var m=e[0];"sizing"===m.type?h=t.havingSize(m.size):"styling"===m.type&&(h=t.havingStyle(mt[m.style]))}var c=st([a[0]||"leftmost"],[],t),p=st([a[1]||"rightmost"],[],t),u="root"===r;return ut(n,((e,t)=>{var r=t.classes[0],a=e.classes[0];"mbin"===r&&s.contains(ht,a)?t.classes[0]="mord":"mbin"===a&&s.contains(lt,r)&&(e.classes[0]="mord")}),{node:c},p,u),ut(n,((e,t)=>{var r=ft(t),a=ft(e),n=r&&a?e.hasClass("mtight")?Qe[r][a]:Je[r][a]:null;if(n)return je.makeGlue(n,h)}),{node:c},p,u),n},ut=function e(t,r,a,n,i){n&&t.push(n);for(var o=0;o<t.length;o++){var s=t[o],l=dt(s);if(l)e(l.children,r,a,null,i);else{var h=!s.hasClass("mspace");if(h){var m=r(s,a.node);m&&(a.insertAfter?a.insertAfter(m):(t.unshift(m),o++))}h?a.node=s:i&&s.hasClass("newline")&&(a.node=st(["leftmost"])),a.insertAfter=(e=>r=>{t.splice(e+1,0,r),o++})(o)}}n&&t.pop()},dt=function(e){return e instanceof z||e instanceof W||e instanceof Y&&e.hasClass("enclosing")?e:null},gt=function e(t,r){var a=dt(t);if(a){var n=a.children;if(n.length){if("right"===r)return e(n[n.length-1],"right");if("left"===r)return e(n[0],"left")}}return t},ft=function(e,t){return e?(t&&(e=gt(e,t)),ct[e.classes[0]]||null):null},vt=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return st(t.concat(r))},bt=function(e,t,a){if(!e)return st();if(tt[e.type]){var n=tt[e.type](e,t);if(a&&t.size!==a.size){n=st(t.sizingClasses(a),[n],t);var i=t.sizeMultiplier/a.sizeMultiplier;n.height*=i,n.depth*=i}return n}throw new r("Got group of unknown type: '"+e.type+"'")};function yt(e,t){var r=st(["base"],e,t),a=st(["strut"]);return a.style.height=V(r.height+r.depth),r.depth&&(a.style.verticalAlign=V(-r.depth)),r.children.unshift(a),r}function xt(e,t){var r=null;1===e.length&&"tag"===e[0].type&&(r=e[0].tag,e=e[0].body);var a,n=pt(e,t,"root");2===n.length&&n[1].hasClass("tag")&&(a=n.pop());for(var i,o=[],s=[],l=0;l<n.length;l++)if(s.push(n[l]),n[l].hasClass("mbin")||n[l].hasClass("mrel")||n[l].hasClass("allowbreak")){for(var h=!1;l<n.length-1&&n[l+1].hasClass("mspace")&&!n[l+1].hasClass("newline");)l++,s.push(n[l]),n[l].hasClass("nobreak")&&(h=!0);h||(o.push(yt(s,t)),s=[])}else n[l].hasClass("newline")&&(s.pop(),s.length>0&&(o.push(yt(s,t)),s=[]),o.push(n[l]));s.length>0&&o.push(yt(s,t)),r?((i=yt(pt(r,t,!0))).classes=["tag"],o.push(i)):a&&o.push(a);var m=st(["katex-html"],o);if(m.setAttribute("aria-hidden","true"),i){var c=i.children[0];c.style.height=V(m.height+m.depth),m.depth&&(c.style.verticalAlign=V(-m.depth))}return m}function wt(e){return new z(e)}class kt{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=P(this.classes));for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=s.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+s.escape(P(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">"}toText(){return this.children.map((e=>e.toText())).join("")}}class St{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return s.escape(this.toText())}toText(){return this.text}}var Mt={MathNode:kt,TextNode:St,SpaceNode:class{constructor(e){this.width=void 0,this.character=void 0,this.width=e,this.character=e>=.05555&&e<=.05556?" ":e>=.1666&&e<=.1667?" ":e>=.2222&&e<=.2223?" ":e>=.2777&&e<=.2778?"  ":e>=-.05556&&e<=-.05555?" ⁣":e>=-.1667&&e<=-.1666?" ⁣":e>=-.2223&&e<=-.2222?" ⁣":e>=-.2778&&e<=-.2777?" ⁣":null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",V(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+V(this.width)+'"/>'}toText(){return this.character?this.character:" "}},newDocumentFragment:wt},zt=function(e,t,r){return!te[t][e]||!te[t][e].replace||55349===e.charCodeAt(0)||be.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6))||(e=te[t][e].replace),new Mt.TextNode(e)},At=function(e){return 1===e.length?e[0]:new Mt.MathNode("mrow",e)},Tt=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";var r=t.font;if(!r||"mathnormal"===r)return null;var a=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathfrak"===r)return"fraktur";if("mathscr"===r||"mathcal"===r)return"script";if("mathsf"===r)return"sans-serif";if("mathtt"===r)return"monospace";var n=e.text;return s.contains(["\\imath","\\jmath"],n)?null:(te[a][n]&&te[a][n].replace&&(n=te[a][n].replace),C(n,je.fontMap[r].fontName,a)?je.fontMap[r].variant:null)},Bt=function(e,t,r){if(1===e.length){var a=Nt(e[0],t);return r&&a instanceof kt&&"mo"===a.type&&(a.setAttribute("lspace","0em"),a.setAttribute("rspace","0em")),[a]}for(var n,i=[],o=0;o<e.length;o++){var s=Nt(e[o],t);if(s instanceof kt&&n instanceof kt){if("mtext"===s.type&&"mtext"===n.type&&s.getAttribute("mathvariant")===n.getAttribute("mathvariant")){n.children.push(...s.children);continue}if("mn"===s.type&&"mn"===n.type){n.children.push(...s.children);continue}if("mi"===s.type&&1===s.children.length&&"mn"===n.type){var l=s.children[0];if(l instanceof St&&"."===l.text){n.children.push(...s.children);continue}}else if("mi"===n.type&&1===n.children.length){var h=n.children[0];if(h instanceof St&&"̸"===h.text&&("mo"===s.type||"mi"===s.type||"mn"===s.type)){var m=s.children[0];m instanceof St&&m.text.length>0&&(m.text=m.text.slice(0,1)+"̸"+m.text.slice(1),i.pop())}}}i.push(s),n=s}return i},Ct=function(e,t,r){return At(Bt(e,t,r))},Nt=function(e,t){if(!e)return new Mt.MathNode("mrow");if(rt[e.type])return rt[e.type](e,t);throw new r("Got group of unknown type: '"+e.type+"'")};function qt(e,t,r,a,n){var i,o=Bt(e,r);i=1===o.length&&o[0]instanceof kt&&s.contains(["mrow","mtable"],o[0].type)?o[0]:new Mt.MathNode("mrow",o);var l=new Mt.MathNode("annotation",[new Mt.TextNode(t)]);l.setAttribute("encoding","application/x-tex");var h=new Mt.MathNode("semantics",[i,l]),m=new Mt.MathNode("math",[h]);m.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&m.setAttribute("display","block");var c=n?"katex":"katex-mathml";return je.makeSpan([c],[m])}var It=function(e){return new H({style:e.displayMode?y.DISPLAY:y.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},Rt=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=je.makeSpan(r,[e])}return e},Ht={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},Ot={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Et=function(e,t,r,a,n){var i,o=e.height+e.depth+r+a;if(/fbox|color|angl/.test(t)){if(i=je.makeSpan(["stretchy",t],[],n),"fbox"===t){var s=n.color&&n.getColor();s&&(i.style.borderColor=s)}}else{var l=[];/^[bx]cancel$/.test(t)&&l.push(new K({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&l.push(new K({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var h=new $(l,{width:"100%",height:V(o)});i=je.makeSvgSpan([],[h],n)}return i.height=o,i.style.height=V(o),i},Lt=function(e){var t=new Mt.MathNode("mo",[new Mt.TextNode(Ht[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},Dt=function(e,t){var{span:r,minWidth:a,height:n}=function(){var r=4e5,a=e.label.slice(1);if(s.contains(["widehat","widecheck","widetilde","utilde"],a)){var n,i,o,l="ordgroup"===(d=e.base).type?d.body.length:1;if(l>5)"widehat"===a||"widecheck"===a?(n=420,r=2364,o=.42,i=a+"4"):(n=312,r=2340,o=.34,i="tilde4");else{var h=[1,1,2,2,3,3][l];"widehat"===a||"widecheck"===a?(r=[0,1062,2364,2364,2364][h],n=[0,239,300,360,420][h],o=[0,.24,.3,.3,.36,.42][h],i=a+h):(r=[0,600,1033,2339,2340][h],n=[0,260,286,306,312][h],o=[0,.26,.286,.3,.306,.34][h],i="tilde"+h)}var m=new Z(i),c=new $([m],{width:"100%",height:V(o),viewBox:"0 0 "+r+" "+n,preserveAspectRatio:"none"});return{span:je.makeSvgSpan([],[c],t),minWidth:0,height:o}}var p,u,d,g=[],f=Ot[a],[v,b,y]=f,x=y/1e3,w=v.length;if(1===w)p=["hide-tail"],u=[f[3]];else if(2===w)p=["halfarrow-left","halfarrow-right"],u=["xMinYMin","xMaxYMin"];else{if(3!==w)throw new Error("Correct katexImagesData or update code here to support\n                    "+w+" children.");p=["brace-left","brace-center","brace-right"],u=["xMinYMin","xMidYMin","xMaxYMin"]}for(var k=0;k<w;k++){var S=new Z(v[k]),M=new $([S],{width:"400em",height:V(x),viewBox:"0 0 "+r+" "+y,preserveAspectRatio:u[k]+" slice"}),z=je.makeSvgSpan([p[k]],[M],t);if(1===w)return{span:z,minWidth:b,height:x};z.style.height=V(x),g.push(z)}return{span:je.makeSpan(["stretchy"],g,t),minWidth:b,height:x}}();return r.height=n,r.style.height=V(n),a>0&&(r.style.minWidth=V(a)),r};function Vt(e,t){if(!e||e.type!==t)throw new Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function Pt(e){var t=Ft(e);if(!t)throw new Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function Ft(e){return e&&("atom"===e.type||ee.hasOwnProperty(e.type))?e:null}var Gt=(e,t)=>{var r,a,n;e&&"supsub"===e.type?(r=(a=Vt(e.base,"accent")).base,e.base=r,n=function(e){if(e instanceof Y)return e;throw new Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(bt(e,t)),e.base=a):r=(a=Vt(e,"accent")).base;var i=bt(r,t.havingCrampedStyle()),o=0;if(a.isShifty&&s.isCharacterBox(r)){var l=s.getBaseElem(r);o=J(bt(l,t.havingCrampedStyle())).skew}var h,m="\\c"===a.label,c=m?i.height+i.depth:Math.min(i.height,t.fontMetrics().xHeight);if(a.isStretchy)h=Dt(a,t),h=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"elem",elem:h,wrapperClasses:["svg-align"],wrapperStyle:o>0?{width:"calc(100% - "+V(2*o)+")",marginLeft:V(2*o)}:void 0}]},t);else{var p,u;"\\vec"===a.label?(p=je.staticSvg("vec",t),u=je.svgData.vec[1]):((p=J(p=je.makeOrd({mode:a.mode,text:a.label},t,"textord"))).italic=0,u=p.width,m&&(c+=p.depth)),h=je.makeSpan(["accent-body"],[p]);var d="\\textcircled"===a.label;d&&(h.classes.push("accent-full"),c=i.height);var g=o;d||(g-=u/2),h.style.left=V(g),"\\textcircled"===a.label&&(h.style.top=".2em"),h=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"kern",size:-c},{type:"elem",elem:h}]},t)}var f=je.makeSpan(["mord","accent"],[h],t);return n?(n.children[0]=f,n.height=Math.max(f.height,n.height),n.classes[0]="mord",n):f},Ut=(e,t)=>{var r=e.isStretchy?Lt(e.label):new Mt.MathNode("mo",[zt(e.label,e.mode)]),a=new Mt.MathNode("mover",[Nt(e.base,t),r]);return a.setAttribute("accent","true"),a},Yt=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map((e=>"\\"+e)).join("|"));at({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{var r=it(t[0]),a=!Yt.test(e.funcName),n=!a||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:a,isShifty:n,base:r}},htmlBuilder:Gt,mathmlBuilder:Ut}),at({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{var r=t[0],a=e.parser.mode;return"math"===a&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:Gt,mathmlBuilder:Ut}),at({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"accentUnder",mode:r.mode,label:a,base:n}},htmlBuilder:(e,t)=>{var r=bt(e.base,t),a=Dt(e,t),n="\\utilde"===e.label?.12:0,i=je.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:r}]},t);return je.makeSpan(["mord","accentunder"],[i],t)},mathmlBuilder:(e,t)=>{var r=Lt(e.label),a=new Mt.MathNode("munder",[Nt(e.base,t),r]);return a.setAttribute("accentunder","true"),a}});var Wt=e=>{var t=new Mt.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};at({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a,funcName:n}=e;return{type:"xArrow",mode:a.mode,label:n,body:t[0],below:r[0]}},htmlBuilder(e,t){var r,a=t.style,n=t.havingStyle(a.sup()),i=je.wrapFragment(bt(e.body,n,t),t),o="\\x"===e.label.slice(0,2)?"x":"cd";i.classes.push(o+"-arrow-pad"),e.below&&(n=t.havingStyle(a.sub()),(r=je.wrapFragment(bt(e.below,n,t),t)).classes.push(o+"-arrow-pad"));var s,l=Dt(e,t),h=-t.fontMetrics().axisHeight+.5*l.height,m=-t.fontMetrics().axisHeight-.5*l.height-.111;if((i.depth>.25||"\\xleftequilibrium"===e.label)&&(m-=i.depth),r){var c=-t.fontMetrics().axisHeight+r.height+.5*l.height+.111;s=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:m},{type:"elem",elem:l,shift:h},{type:"elem",elem:r,shift:c}]},t)}else s=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:m},{type:"elem",elem:l,shift:h}]},t);return s.children[0].children[0].children[1].classes.push("svg-align"),je.makeSpan(["mrel","x-arrow"],[s],t)},mathmlBuilder(e,t){var r,a=Lt(e.label);if(a.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){var n=Wt(Nt(e.body,t));if(e.below){var i=Wt(Nt(e.below,t));r=new Mt.MathNode("munderover",[a,i,n])}else r=new Mt.MathNode("mover",[a,n])}else if(e.below){var o=Wt(Nt(e.below,t));r=new Mt.MathNode("munder",[a,o])}else r=Wt(),r=new Mt.MathNode("mover",[a,r]);return r}});var Xt=je.makeSpan;function _t(e,t){var r=pt(e.body,t,!0);return Xt([e.mclass],r,t)}function jt(e,t){var r,a=Bt(e.body,t);return"minner"===e.mclass?r=new Mt.MathNode("mpadded",a):"mord"===e.mclass?e.isCharacterBox?(r=a[0]).type="mi":r=new Mt.MathNode("mi",a):(e.isCharacterBox?(r=a[0]).type="mo":r=new Mt.MathNode("mo",a),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}at({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+a.slice(5),body:ot(n),isCharacterBox:s.isCharacterBox(n)}},htmlBuilder:_t,mathmlBuilder:jt});var $t=e=>{var t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"!==t.type||"bin"!==t.family&&"rel"!==t.family?"mord":"m"+t.family};at({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){var{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:$t(t[0]),body:ot(t[1]),isCharacterBox:s.isCharacterBox(t[1])}}}),at({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){var r,{parser:a,funcName:n}=e,i=t[1],o=t[0];r="\\stackrel"!==n?$t(i):"mrel";var l={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==n,body:ot(i)},h={type:"supsub",mode:o.mode,base:l,sup:"\\underset"===n?null:o,sub:"\\underset"===n?o:null};return{type:"mclass",mode:a.mode,mclass:r,body:[h],isCharacterBox:s.isCharacterBox(h)}},htmlBuilder:_t,mathmlBuilder:jt}),at({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:$t(t[0]),body:ot(t[0])}},htmlBuilder(e,t){var r=pt(e.body,t,!0),a=je.makeSpan([e.mclass],r,t);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(e,t){var r=Bt(e.body,t),a=new Mt.MathNode("mstyle",r);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var Zt={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},Kt=e=>"textord"===e.type&&"@"===e.text;function Jt(e,t,r){var a=Zt[e];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(a,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":var n={type:"atom",text:a,mode:"math",family:"rel"},i={type:"ordgroup",mode:"math",body:[r.callFunction("\\\\cdleft",[t[0]],[]),r.callFunction("\\Big",[n],[]),r.callFunction("\\\\cdright",[t[1]],[])]};return r.callFunction("\\\\cdparent",[i],[]);case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}at({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"cdlabel",mode:r.mode,side:a.slice(4),label:t[0]}},htmlBuilder(e,t){var r=t.havingStyle(t.style.sup()),a=je.wrapFragment(bt(e.label,r,t),t);return a.classes.push("cd-label-"+e.side),a.style.bottom=V(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(e,t){var r=new Mt.MathNode("mrow",[Nt(e.label,t)]);return(r=new Mt.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new Mt.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),at({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){var{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){var r=je.wrapFragment(bt(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(e,t)=>new Mt.MathNode("mrow",[Nt(e.fragment,t)])}),at({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){for(var{parser:a}=e,n=Vt(t[0],"ordgroup").body,i="",o=0;o<n.length;o++){i+=Vt(n[o],"textord").text}var s,l=parseInt(i);if(isNaN(l))throw new r("\\@char has non-numeric argument "+i);if(l<0||l>=1114111)throw new r("\\@char with invalid code point "+i);return l<=65535?s=String.fromCharCode(l):(l-=65536,s=String.fromCharCode(55296+(l>>10),56320+(1023&l))),{type:"textord",mode:a.mode,text:s}}});var Qt=(e,t)=>{var r=pt(e.body,t.withColor(e.color),!1);return je.makeFragment(r)},er=(e,t)=>{var r=Bt(e.body,t.withColor(e.color)),a=new Mt.MathNode("mstyle",r);return a.setAttribute("mathcolor",e.color),a};at({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){var{parser:r}=e,a=Vt(t[0],"color-token").color,n=t[1];return{type:"color",mode:r.mode,color:a,body:ot(n)}},htmlBuilder:Qt,mathmlBuilder:er}),at({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){var{parser:r,breakOnTokenText:a}=e,n=Vt(t[0],"color-token").color;r.gullet.macros.set("\\current@color",n);var i=r.parseExpression(!0,a);return{type:"color",mode:r.mode,color:n,body:i}},htmlBuilder:Qt,mathmlBuilder:er}),at({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(e,t,r){var{parser:a}=e,n="["===a.gullet.future().text?a.parseSizeGroup(!0):null,i=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:i,size:n&&Vt(n,"size").value}},htmlBuilder(e,t){var r=je.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=V(D(e.size,t)))),r},mathmlBuilder(e,t){var r=new Mt.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",V(D(e.size,t)))),r}});var tr={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},rr=e=>{var t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new r("Expected a control sequence",e);return t},ar=(e,t,r,a)=>{var n=e.gullet.macros.get(r.text);null==n&&(r.noexpand=!0,n={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,n,a)};at({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t,funcName:a}=e;t.consumeSpaces();var n=t.fetch();if(tr[n.text])return"\\global"!==a&&"\\\\globallong"!==a||(n.text=tr[n.text]),Vt(t.parseFunction(),"internal");throw new r("Invalid token after macro prefix",n)}}),at({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:a}=e,n=t.gullet.popToken(),i=n.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(i))throw new r("Expected a control sequence",n);for(var o,s=0,l=[[]];"{"!==t.gullet.future().text;)if("#"===(n=t.gullet.popToken()).text){if("{"===t.gullet.future().text){o=t.gullet.future(),l[s].push("{");break}if(n=t.gullet.popToken(),!/^[1-9]$/.test(n.text))throw new r('Invalid argument number "'+n.text+'"');if(parseInt(n.text)!==s+1)throw new r('Argument number "'+n.text+'" out of order');s++,l.push([])}else{if("EOF"===n.text)throw new r("Expected a macro definition");l[s].push(n.text)}var{tokens:h}=t.gullet.consumeArg();return o&&h.unshift(o),"\\edef"!==a&&"\\xdef"!==a||(h=t.gullet.expandTokens(h)).reverse(),t.gullet.macros.set(i,{tokens:h,numArgs:s,delimiters:l},a===tr[a]),{type:"internal",mode:t.mode}}}),at({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=rr(t.gullet.popToken());t.gullet.consumeSpaces();var n=(e=>{var t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t})(t);return ar(t,a,n,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),at({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=rr(t.gullet.popToken()),n=t.gullet.popToken(),i=t.gullet.popToken();return ar(t,a,i,"\\\\globalfuture"===r),t.gullet.pushToken(i),t.gullet.pushToken(n),{type:"internal",mode:t.mode}}});var nr=function(e,t,r){var a=C(te.math[e]&&te.math[e].replace||e,t,r);if(!a)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return a},ir=function(e,t,r,a){var n=r.havingBaseStyle(t),i=je.makeSpan(a.concat(n.sizingClasses(r)),[e],r),o=n.sizeMultiplier/r.sizeMultiplier;return i.height*=o,i.depth*=o,i.maxFontSize=n.sizeMultiplier,i},or=function(e,t,r){var a=t.havingBaseStyle(r),n=(1-t.sizeMultiplier/a.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=V(n),e.height-=n,e.depth+=n},sr=function(e,t,r,a,n,i){var o=function(e,t,r,a){return je.makeSymbol(e,"Size"+t+"-Regular",r,a)}(e,t,n,a),s=ir(je.makeSpan(["delimsizing","size"+t],[o],a),y.TEXT,a,i);return r&&or(s,a,y.TEXT),s},lr=function(e,t,r){var a;return a="Size1-Regular"===t?"delim-size1":"delim-size4",{type:"elem",elem:je.makeSpan(["delimsizinginner",a],[je.makeSpan([],[je.makeSymbol(e,t,r)])])}},hr=function(e,t,r){var a=A["Size4-Regular"][e.charCodeAt(0)]?A["Size4-Regular"][e.charCodeAt(0)][4]:A["Size1-Regular"][e.charCodeAt(0)][4],n=new Z("inner",function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145zM367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257zM478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z";default:return""}}(e,Math.round(1e3*t))),i=new $([n],{width:V(a),height:V(t),style:"width:"+V(a),viewBox:"0 0 "+1e3*a+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),o=je.makeSvgSpan([],[i],r);return o.height=t,o.style.height=V(t),o.style.width=V(a),{type:"elem",elem:o}},mr={type:"kern",size:-.008},cr=["|","\\lvert","\\rvert","\\vert"],pr=["\\|","\\lVert","\\rVert","\\Vert"],ur=function(e,t,r,a,n,i){var o,l,h,m,c="",p=0;o=h=m=e,l=null;var u="Size1-Regular";"\\uparrow"===e?h=m="⏐":"\\Uparrow"===e?h=m="‖":"\\downarrow"===e?o=h="⏐":"\\Downarrow"===e?o=h="‖":"\\updownarrow"===e?(o="\\uparrow",h="⏐",m="\\downarrow"):"\\Updownarrow"===e?(o="\\Uparrow",h="‖",m="\\Downarrow"):s.contains(cr,e)?(h="∣",c="vert",p=333):s.contains(pr,e)?(h="∥",c="doublevert",p=556):"["===e||"\\lbrack"===e?(o="⎡",h="⎢",m="⎣",u="Size4-Regular",c="lbrack",p=667):"]"===e||"\\rbrack"===e?(o="⎤",h="⎥",m="⎦",u="Size4-Regular",c="rbrack",p=667):"\\lfloor"===e||"⌊"===e?(h=o="⎢",m="⎣",u="Size4-Regular",c="lfloor",p=667):"\\lceil"===e||"⌈"===e?(o="⎡",h=m="⎢",u="Size4-Regular",c="lceil",p=667):"\\rfloor"===e||"⌋"===e?(h=o="⎥",m="⎦",u="Size4-Regular",c="rfloor",p=667):"\\rceil"===e||"⌉"===e?(o="⎤",h=m="⎥",u="Size4-Regular",c="rceil",p=667):"("===e||"\\lparen"===e?(o="⎛",h="⎜",m="⎝",u="Size4-Regular",c="lparen",p=875):")"===e||"\\rparen"===e?(o="⎞",h="⎟",m="⎠",u="Size4-Regular",c="rparen",p=875):"\\{"===e||"\\lbrace"===e?(o="⎧",l="⎨",m="⎩",h="⎪",u="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(o="⎫",l="⎬",m="⎭",h="⎪",u="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(o="⎧",m="⎩",h="⎪",u="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(o="⎫",m="⎭",h="⎪",u="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(o="⎧",m="⎭",h="⎪",u="Size4-Regular"):"\\rmoustache"!==e&&"⎱"!==e||(o="⎫",m="⎩",h="⎪",u="Size4-Regular");var d=nr(o,u,n),g=d.height+d.depth,f=nr(h,u,n),v=f.height+f.depth,b=nr(m,u,n),x=b.height+b.depth,w=0,k=1;if(null!==l){var S=nr(l,u,n);w=S.height+S.depth,k=2}var M=g+x+w,z=M+Math.max(0,Math.ceil((t-M)/(k*v)))*k*v,A=a.fontMetrics().axisHeight;r&&(A*=a.sizeMultiplier);var T=z/2-A,B=[];if(c.length>0){var C=z-g-x,N=Math.round(1e3*z),q=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw new Error("Unknown stretchy delimiter.")}}(c,Math.round(1e3*C)),I=new Z(c,q),R=(p/1e3).toFixed(3)+"em",H=(N/1e3).toFixed(3)+"em",O=new $([I],{width:R,height:H,viewBox:"0 0 "+p+" "+N}),E=je.makeSvgSpan([],[O],a);E.height=N/1e3,E.style.width=R,E.style.height=H,B.push({type:"elem",elem:E})}else{if(B.push(lr(m,u,n)),B.push(mr),null===l){var L=z-g-x+.016;B.push(hr(h,L,a))}else{var D=(z-g-x-w)/2+.016;B.push(hr(h,D,a)),B.push(mr),B.push(lr(l,u,n)),B.push(mr),B.push(hr(h,D,a))}B.push(mr),B.push(lr(o,u,n))}var V=a.havingBaseStyle(y.TEXT),P=je.makeVList({positionType:"bottom",positionData:T,children:B},V);return ir(je.makeSpan(["delimsizing","mult"],[P],V),y.TEXT,a,i)},dr=.08,gr=function(e,t,r,a,n){var i=function(e,t,r){t*=1e3;var a="";switch(e){case"sqrtMain":a=function(e){return"M95,"+(622+e+S)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+e/2.075+" -"+e+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+e)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+e)+" "+S+"h400000v"+(40+e)+"h-400000z"}(t);break;case"sqrtSize1":a=function(e){return"M263,"+(601+e+S)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+e/2.084+" -"+e+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+e)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+e)+" "+S+"h400000v"+(40+e)+"h-400000z"}(t);break;case"sqrtSize2":a=function(e){return"M983 "+(10+e+S)+"\nl"+e/3.13+" -"+e+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+e)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+e)+" "+S+"h400000v"+(40+e)+"h-400000z"}(t);break;case"sqrtSize3":a=function(e){return"M424,"+(2398+e+S)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+e/4.223+" -"+e+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+e)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+e)+" "+S+"\nh400000v"+(40+e)+"h-400000z"}(t);break;case"sqrtSize4":a=function(e){return"M473,"+(2713+e+S)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+e/5.298+" -"+e+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+e)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+e)+" "+S+"h400000v"+(40+e)+"H1017.7z"}(t);break;case"sqrtTall":a=function(e,t,r){return"M702 "+(e+S)+"H400000"+(40+e)+"\nH742v"+(r-54-S-e)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 "+S+"H400000v"+(40+e)+"H742z"}(t,0,r)}return a}(e,a,r),o=new Z(e,i),s=new $([o],{width:"400em",height:V(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return je.makeSvgSpan(["hide-tail"],[s],n)},fr=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],vr=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],br=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],yr=[0,1.2,1.8,2.4,3],xr=[{type:"small",style:y.SCRIPTSCRIPT},{type:"small",style:y.SCRIPT},{type:"small",style:y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],wr=[{type:"small",style:y.SCRIPTSCRIPT},{type:"small",style:y.SCRIPT},{type:"small",style:y.TEXT},{type:"stack"}],kr=[{type:"small",style:y.SCRIPTSCRIPT},{type:"small",style:y.SCRIPT},{type:"small",style:y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],Sr=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},Mr=function(e,t,r,a){for(var n=Math.min(2,3-a.style.size);n<r.length&&"stack"!==r[n].type;n++){var i=nr(e,Sr(r[n]),"math"),o=i.height+i.depth;if("small"===r[n].type&&(o*=a.havingBaseStyle(r[n].style).sizeMultiplier),o>t)return r[n]}return r[r.length-1]},zr=function(e,t,r,a,n,i){var o;"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"⟩"!==e||(e="\\rangle"),o=s.contains(br,e)?xr:s.contains(fr,e)?kr:wr;var l=Mr(e,t,o,a);return"small"===l.type?function(e,t,r,a,n,i){var o=je.makeSymbol(e,"Main-Regular",n,a),s=ir(o,t,a,i);return r&&or(s,a,t),s}(e,l.style,r,a,n,i):"large"===l.type?sr(e,l.size,r,a,n,i):ur(e,t,r,a,n,i)},Ar={sqrtImage:function(e,t){var r,a,n=t.havingBaseSizing(),i=Mr("\\surd",e*n.sizeMultiplier,kr,n),o=n.sizeMultiplier,s=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),l=0,h=0,m=0;return"small"===i.type?(e<1?o=1:e<1.4&&(o=.7),h=(1+s)/o,(r=gr("sqrtMain",l=(1+s+dr)/o,m=1e3+1e3*s+80,s,t)).style.minWidth="0.853em",a=.833/o):"large"===i.type?(m=1080*yr[i.size],h=(yr[i.size]+s)/o,l=(yr[i.size]+s+dr)/o,(r=gr("sqrtSize"+i.size,l,m,s,t)).style.minWidth="1.02em",a=1/o):(l=e+s+dr,h=e+s,m=Math.floor(1e3*e+s)+80,(r=gr("sqrtTall",l,m,s,t)).style.minWidth="0.742em",a=1.056),r.height=h,r.style.height=V(l),{span:r,advanceWidth:a,ruleWidth:(t.fontMetrics().sqrtRuleThickness+s)*o}},sizedDelim:function(e,t,a,n,i){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"⟩"!==e||(e="\\rangle"),s.contains(fr,e)||s.contains(br,e))return sr(e,t,!1,a,n,i);if(s.contains(vr,e))return ur(e,yr[t],!1,a,n,i);throw new r("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:yr,customSizedDelim:zr,leftRightDelim:function(e,t,r,a,n,i){var o=a.fontMetrics().axisHeight*a.sizeMultiplier,s=5/a.fontMetrics().ptPerEm,l=Math.max(t-o,r+o),h=Math.max(l/500*901,2*l-s);return zr(e,h,!0,a,n,i)}},Tr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},Br=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Cr(e,t){var a=Ft(e);if(a&&s.contains(Br,a.text))return a;throw new r(a?"Invalid delimiter '"+a.text+"' after '"+t.funcName+"'":"Invalid delimiter type '"+e.type+"'",e)}function Nr(e){if(!e.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}at({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{var r=Cr(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:Tr[e.funcName].size,mclass:Tr[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?je.makeSpan([e.mclass]):Ar.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{var t=[];"."!==e.delim&&t.push(zt(e.delim,e.mode));var r=new Mt.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var a=V(Ar.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",a),r.setAttribute("maxsize",a),r}}),at({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var a=e.parser.gullet.macros.get("\\current@color");if(a&&"string"!=typeof a)throw new r("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:Cr(t[0],e).text,color:a}}}),at({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=Cr(t[0],e),a=e.parser;++a.leftrightDepth;var n=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var i=Vt(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:n,left:r.text,right:i.delim,rightColor:i.color}},htmlBuilder:(e,t)=>{Nr(e);for(var r,a,n=pt(e.body,t,!0,["mopen","mclose"]),i=0,o=0,s=!1,l=0;l<n.length;l++)n[l].isMiddle?s=!0:(i=Math.max(n[l].height,i),o=Math.max(n[l].depth,o));if(i*=t.sizeMultiplier,o*=t.sizeMultiplier,r="."===e.left?vt(t,["mopen"]):Ar.leftRightDelim(e.left,i,o,t,e.mode,["mopen"]),n.unshift(r),s)for(var h=1;h<n.length;h++){var m=n[h].isMiddle;m&&(n[h]=Ar.leftRightDelim(m.delim,i,o,m.options,e.mode,[]))}if("."===e.right)a=vt(t,["mclose"]);else{var c=e.rightColor?t.withColor(e.rightColor):t;a=Ar.leftRightDelim(e.right,i,o,c,e.mode,["mclose"])}return n.push(a),je.makeSpan(["minner"],n,t)},mathmlBuilder:(e,t)=>{Nr(e);var r=Bt(e.body,t);if("."!==e.left){var a=new Mt.MathNode("mo",[zt(e.left,e.mode)]);a.setAttribute("fence","true"),r.unshift(a)}if("."!==e.right){var n=new Mt.MathNode("mo",[zt(e.right,e.mode)]);n.setAttribute("fence","true"),e.rightColor&&n.setAttribute("mathcolor",e.rightColor),r.push(n)}return At(r)}}),at({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var a=Cr(t[0],e);if(!e.parser.leftrightDepth)throw new r("\\middle without preceding \\left",a);return{type:"middle",mode:e.parser.mode,delim:a.text}},htmlBuilder:(e,t)=>{var r;if("."===e.delim)r=vt(t,[]);else{r=Ar.sizedDelim(e.delim,1,t,e.mode,[]);var a={delim:e.delim,options:t};r.isMiddle=a}return r},mathmlBuilder:(e,t)=>{var r="\\vert"===e.delim||"|"===e.delim?zt("|","text"):zt(e.delim,e.mode),a=new Mt.MathNode("mo",[r]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var qr=(e,t)=>{var r,a,n,i=je.wrapFragment(bt(e.body,t),t),o=e.label.slice(1),l=t.sizeMultiplier,h=0,m=s.isCharacterBox(e.body);if("sout"===o)(r=je.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/l,h=-.5*t.fontMetrics().xHeight;else if("phase"===o){var c=D({number:.6,unit:"pt"},t),p=D({number:.35,unit:"ex"},t);l/=t.havingBaseSizing().sizeMultiplier;var u=i.height+i.depth+c+p;i.style.paddingLeft=V(u/2+c);var d=Math.floor(1e3*u*l),g="M400000 "+(a=d)+" H0 L"+a/2+" 0 l65 45 L145 "+(a-80)+" H400000z",f=new $([new Z("phase",g)],{width:"400em",height:V(d/1e3),viewBox:"0 0 400000 "+d,preserveAspectRatio:"xMinYMin slice"});(r=je.makeSvgSpan(["hide-tail"],[f],t)).style.height=V(u),h=i.depth+c+p}else{/cancel/.test(o)?m||i.classes.push("cancel-pad"):"angl"===o?i.classes.push("anglpad"):i.classes.push("boxpad");var v=0,b=0,y=0;/box/.test(o)?(y=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),b=v=t.fontMetrics().fboxsep+("colorbox"===o?0:y)):"angl"===o?(v=4*(y=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),b=Math.max(0,.25-i.depth)):b=v=m?.2:0,r=Et(i,o,v,b,t),/fbox|boxed|fcolorbox/.test(o)?(r.style.borderStyle="solid",r.style.borderWidth=V(y)):"angl"===o&&.049!==y&&(r.style.borderTopWidth=V(y),r.style.borderRightWidth=V(y)),h=i.depth+b,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)n=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:h},{type:"elem",elem:i,shift:0}]},t);else{var x=/cancel|phase/.test(o)?["svg-align"]:[];n=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:r,shift:h,wrapperClasses:x}]},t)}return/cancel/.test(o)&&(n.height=i.height,n.depth=i.depth),/cancel/.test(o)&&!m?je.makeSpan(["mord","cancel-lap"],[n],t):je.makeSpan(["mord"],[n],t)},Ir=(e,t)=>{var r=0,a=new Mt.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[Nt(e.body,t)]);switch(e.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*r+"pt"),a.setAttribute("height","+"+2*r+"pt"),a.setAttribute("lspace",r+"pt"),a.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){var n=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);a.setAttribute("style","border: "+n+"em solid "+String(e.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&a.setAttribute("mathbackground",e.backgroundColor),a};at({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=Vt(t[0],"color-token").color,o=t[1];return{type:"enclose",mode:a.mode,label:n,backgroundColor:i,body:o}},htmlBuilder:qr,mathmlBuilder:Ir}),at({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=Vt(t[0],"color-token").color,o=Vt(t[1],"color-token").color,s=t[2];return{type:"enclose",mode:a.mode,label:n,backgroundColor:o,borderColor:i,body:s}},htmlBuilder:qr,mathmlBuilder:Ir}),at({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),at({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"enclose",mode:r.mode,label:a,body:n}},htmlBuilder:qr,mathmlBuilder:Ir}),at({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});var Rr={};function Hr(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:o}=e,s={type:t,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},l=0;l<r.length;++l)Rr[r[l]]=s;i&&(tt[t]=i),o&&(rt[t]=o)}var Or={};function Er(e,t){Or[e]=t}function Lr(e){var t=[];e.consumeSpaces();var r=e.fetch().text;for("\\relax"===r&&(e.consume(),e.consumeSpaces(),r=e.fetch().text);"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}var Dr=e=>{if(!e.parser.settings.displayMode)throw new r("{"+e.envName+"} can be used only in display mode.")};function Vr(e){if(-1===e.indexOf("ed"))return-1===e.indexOf("*")}function Pr(e,a,n){var{hskipBeforeAndAfter:i,addJot:o,cols:s,arraystretch:l,colSeparationType:h,autoTag:m,singleRow:c,emptySingleRow:p,maxNumCols:u,leqno:d}=a;if(e.gullet.beginGroup(),c||e.gullet.macros.set("\\cr","\\\\\\relax"),!l){var g=e.gullet.expandMacroAsText("\\arraystretch");if(null==g)l=1;else if(!(l=parseFloat(g))||l<0)throw new r("Invalid \\arraystretch: "+g)}e.gullet.beginGroup();var f=[],v=[f],b=[],y=[],x=null!=m?[]:void 0;function w(){m&&e.gullet.macros.set("\\@eqnsw","1",!0)}function k(){x&&(e.gullet.macros.get("\\df@tag")?(x.push(e.subparse([new t("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):x.push(Boolean(m)&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(w(),y.push(Lr(e));;){var S=e.parseExpression(!1,c?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),S={type:"ordgroup",mode:e.mode,body:S},n&&(S={type:"styling",mode:e.mode,style:n,body:[S]}),f.push(S);var M=e.fetch().text;if("&"===M){if(u&&f.length===u){if(c||h)throw new r("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else{if("\\end"===M){k(),1===f.length&&"styling"===S.type&&0===S.body[0].body.length&&(v.length>1||!p)&&v.pop(),y.length<v.length+1&&y.push([]);break}if("\\\\"!==M)throw new r("Expected & or \\\\ or \\cr or \\end",e.nextToken);e.consume();var z=void 0;" "!==e.gullet.future().text&&(z=e.parseSizeGroup(!0)),b.push(z?z.value:null),k(),y.push(Lr(e)),f=[],v.push(f),w()}}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:o,arraystretch:l,body:v,cols:s,rowGaps:b,hskipBeforeAndAfter:i,hLinesBeforeRow:y,colSeparationType:h,tags:x,leqno:d}}function Fr(e){return"d"===e.slice(0,1)?"display":"text"}var Gr=function(e,t){var a,n,i=e.body.length,o=e.hLinesBeforeRow,l=0,h=new Array(i),m=[],c=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),p=1/t.fontMetrics().ptPerEm,u=5*p;e.colSeparationType&&"small"===e.colSeparationType&&(u=t.havingStyle(y.SCRIPT).sizeMultiplier/t.sizeMultiplier*.2778);var d="CD"===e.colSeparationType?D({number:3,unit:"ex"},t):12*p,g=3*p,f=e.arraystretch*d,v=.7*f,b=.3*f,x=0;function w(e){for(var t=0;t<e.length;++t)t>0&&(x+=.25),m.push({pos:x,isDashed:e[t]})}for(w(o[0]),a=0;a<e.body.length;++a){var k=e.body[a],S=v,M=b;l<k.length&&(l=k.length);var z=new Array(k.length);for(n=0;n<k.length;++n){var A=bt(k[n],t);M<A.depth&&(M=A.depth),S<A.height&&(S=A.height),z[n]=A}var T=e.rowGaps[a],B=0;T&&(B=D(T,t))>0&&(M<(B+=b)&&(M=B),B=0),e.addJot&&(M+=g),z.height=S,z.depth=M,x+=S,z.pos=x,x+=M+B,h[a]=z,w(o[a+1])}var C,N,q=x/2+t.fontMetrics().axisHeight,I=e.cols||[],R=[],H=[];if(e.tags&&e.tags.some((e=>e)))for(a=0;a<i;++a){var O=h[a],E=O.pos-q,L=e.tags[a],P=void 0;(P=!0===L?je.makeSpan(["eqn-num"],[],t):!1===L?je.makeSpan([],[],t):je.makeSpan([],pt(L,t,!0),t)).depth=O.depth,P.height=O.height,H.push({type:"elem",elem:P,shift:E})}for(n=0,N=0;n<l||N<I.length;++n,++N){for(var F=I[N]||{},G=!0;"separator"===F.type;){if(G||((C=je.makeSpan(["arraycolsep"],[])).style.width=V(t.fontMetrics().doubleRuleSep),R.push(C)),"|"!==F.separator&&":"!==F.separator)throw new r("Invalid separator type: "+F.separator);var U="|"===F.separator?"solid":"dashed",Y=je.makeSpan(["vertical-separator"],[],t);Y.style.height=V(x),Y.style.borderRightWidth=V(c),Y.style.borderRightStyle=U,Y.style.margin="0 "+V(-c/2);var W=x-q;W&&(Y.style.verticalAlign=V(-W)),R.push(Y),F=I[++N]||{},G=!1}if(!(n>=l)){var X=void 0;(n>0||e.hskipBeforeAndAfter)&&0!==(X=s.deflt(F.pregap,u))&&((C=je.makeSpan(["arraycolsep"],[])).style.width=V(X),R.push(C));var _=[];for(a=0;a<i;++a){var j=h[a],$=j[n];if($){var Z=j.pos-q;$.depth=j.depth,$.height=j.height,_.push({type:"elem",elem:$,shift:Z})}}_=je.makeVList({positionType:"individualShift",children:_},t),_=je.makeSpan(["col-align-"+(F.align||"c")],[_]),R.push(_),(n<l-1||e.hskipBeforeAndAfter)&&0!==(X=s.deflt(F.postgap,u))&&((C=je.makeSpan(["arraycolsep"],[])).style.width=V(X),R.push(C))}}if(h=je.makeSpan(["mtable"],R),m.length>0){for(var K=je.makeLineSpan("hline",t,c),J=je.makeLineSpan("hdashline",t,c),Q=[{type:"elem",elem:h,shift:0}];m.length>0;){var ee=m.pop(),te=ee.pos-q;ee.isDashed?Q.push({type:"elem",elem:J,shift:te}):Q.push({type:"elem",elem:K,shift:te})}h=je.makeVList({positionType:"individualShift",children:Q},t)}if(0===H.length)return je.makeSpan(["mord"],[h],t);var re=je.makeVList({positionType:"individualShift",children:H},t);return re=je.makeSpan(["tag"],[re],t),je.makeFragment([h,re])},Ur={c:"center ",l:"left ",r:"right "},Yr=function(e,t){for(var r=[],a=new Mt.MathNode("mtd",[],["mtr-glue"]),n=new Mt.MathNode("mtd",[],["mml-eqn-num"]),i=0;i<e.body.length;i++){for(var o=e.body[i],s=[],l=0;l<o.length;l++)s.push(new Mt.MathNode("mtd",[Nt(o[l],t)]));e.tags&&e.tags[i]&&(s.unshift(a),s.push(a),e.leqno?s.unshift(n):s.push(n)),r.push(new Mt.MathNode("mtr",s))}var h=new Mt.MathNode("mtable",r),m=.5===e.arraystretch?.1:.16+e.arraystretch-1+(e.addJot?.09:0);h.setAttribute("rowspacing",V(m));var c="",p="";if(e.cols&&e.cols.length>0){var u=e.cols,d="",g=!1,f=0,v=u.length;"separator"===u[0].type&&(c+="top ",f=1),"separator"===u[u.length-1].type&&(c+="bottom ",v-=1);for(var b=f;b<v;b++)"align"===u[b].type?(p+=Ur[u[b].align],g&&(d+="none "),g=!0):"separator"===u[b].type&&g&&(d+="|"===u[b].separator?"solid ":"dashed ",g=!1);h.setAttribute("columnalign",p.trim()),/[sd]/.test(d)&&h.setAttribute("columnlines",d.trim())}if("align"===e.colSeparationType){for(var y=e.cols||[],x="",w=1;w<y.length;w++)x+=w%2?"0em ":"1em ";h.setAttribute("columnspacing",x.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?h.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?h.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?h.setAttribute("columnspacing","0.5em"):h.setAttribute("columnspacing","1em");var k="",S=e.hLinesBeforeRow;c+=S[0].length>0?"left ":"",c+=S[S.length-1].length>0?"right ":"";for(var M=1;M<S.length-1;M++)k+=0===S[M].length?"none ":S[M][0]?"dashed ":"solid ";return/[sd]/.test(k)&&h.setAttribute("rowlines",k.trim()),""!==c&&(h=new Mt.MathNode("menclose",[h])).setAttribute("notation",c.trim()),e.arraystretch&&e.arraystretch<1&&(h=new Mt.MathNode("mstyle",[h])).setAttribute("scriptlevel","1"),h},Wr=function(e,t){-1===e.envName.indexOf("ed")&&Dr(e);var a,n=[],i=e.envName.indexOf("at")>-1?"alignat":"align",o="split"===e.envName,s=Pr(e.parser,{cols:n,addJot:!0,autoTag:o?void 0:Vr(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:o?2:void 0,leqno:e.parser.settings.leqno},"display"),l=0,h={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){for(var m="",c=0;c<t[0].body.length;c++){m+=Vt(t[0].body[c],"textord").text}a=Number(m),l=2*a}var p=!l;s.body.forEach((function(e){for(var t=1;t<e.length;t+=2){var n=Vt(e[t],"styling");Vt(n.body[0],"ordgroup").body.unshift(h)}if(p)l<e.length&&(l=e.length);else{var i=e.length/2;if(a<i)throw new r("Too many math in a row: expected "+a+", but got "+i,e[0])}}));for(var u=0;u<l;++u){var d="r",g=0;u%2==1?d="l":u>0&&p&&(g=1),n[u]={type:"align",align:d,pregap:g,postgap:0}}return s.colSeparationType=p?"align":"alignat",s};Hr({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){var a=(Ft(t[0])?[t[0]]:Vt(t[0],"ordgroup").body).map((function(e){var t=Pt(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new r("Unknown column alignment: "+t,e)})),n={cols:a,hskipBeforeAndAfter:!0,maxNumCols:a.length};return Pr(e.parser,n,Fr(e.envName))},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],a="c",n={hskipBeforeAndAfter:!1,cols:[{type:"align",align:a}]};if("*"===e.envName.charAt(e.envName.length-1)){var i=e.parser;if(i.consumeSpaces(),"["===i.fetch().text){if(i.consume(),i.consumeSpaces(),a=i.fetch().text,-1==="lcr".indexOf(a))throw new r("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),n.cols=[{type:"align",align:a}]}}var o=Pr(e.parser,n,Fr(e.envName)),s=Math.max(0,...o.body.map((e=>e.length)));return o.cols=new Array(s).fill({type:"align",align:a}),t?{type:"leftright",mode:e.mode,body:[o],left:t[0],right:t[1],rightColor:void 0}:o},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){var t=Pr(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){var a=(Ft(t[0])?[t[0]]:Vt(t[0],"ordgroup").body).map((function(e){var t=Pt(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new r("Unknown column alignment: "+t,e)}));if(a.length>1)throw new r("{subarray} can contain only one column");var n={cols:a,hskipBeforeAndAfter:!1,arraystretch:.5};if((n=Pr(e.parser,n,"script")).body.length>0&&n.body[0].length>1)throw new r("{subarray} can contain only one column");return n},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){var t=Pr(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},Fr(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Wr,htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){s.contains(["gather","gather*"],e.envName)&&Dr(e);var t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:Vr(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return Pr(e.parser,t,"display")},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Wr,htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){Dr(e);var t={autoTag:Vr(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return Pr(e.parser,t,"display")},htmlBuilder:Gr,mathmlBuilder:Yr}),Hr({type:"array",names:["CD"],props:{numArgs:0},handler:e=>(Dr(e),function(e){var t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();var a=e.fetch().text;if("&"!==a&&"\\\\"!==a){if("\\end"===a){0===t[t.length-1].length&&t.pop();break}throw new r("Expected \\\\ or \\cr or \\end",e.nextToken)}e.consume()}for(var n,i,o=[],s=[o],l=0;l<t.length;l++){for(var h=t[l],m={type:"styling",body:[],mode:"math",style:"display"},c=0;c<h.length;c++)if(Kt(h[c])){o.push(m);var p=Pt(h[c+=1]).text,u=new Array(2);if(u[0]={type:"ordgroup",mode:"math",body:[]},u[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(p)>-1);else{if(!("<>AV".indexOf(p)>-1))throw new r('Expected one of "<>AV=|." after @',h[c]);for(var d=0;d<2;d++){for(var g=!0,f=c+1;f<h.length;f++){if(i=p,("mathord"===(n=h[f]).type||"atom"===n.type)&&n.text===i){g=!1,c=f;break}if(Kt(h[f]))throw new r("Missing a "+p+" character to complete a CD arrow.",h[f]);u[d].body.push(h[f])}if(g)throw new r("Missing a "+p+" character to complete a CD arrow.",h[c])}}var v={type:"styling",body:[Jt(p,u,e)],mode:"math",style:"display"};o.push(v),m={type:"styling",body:[],mode:"math",style:"display"}}else m.body.push(h[c]);l%2==0?o.push(m):o.shift(),o=[],s.push(o)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:"math",body:s,arraystretch:1,addJot:!0,rowGaps:[null],cols:new Array(s[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25}),colSeparationType:"CD",hLinesBeforeRow:new Array(s.length+1).fill([])}}(e.parser)),htmlBuilder:Gr,mathmlBuilder:Yr}),Er("\\nonumber","\\gdef\\@eqnsw{0}"),Er("\\notag","\\nonumber"),at({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new r(e.funcName+" valid only within array environment")}});var Xr=Rr;at({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){var{parser:a,funcName:n}=e,i=t[0];if("ordgroup"!==i.type)throw new r("Invalid environment name",i);for(var o="",s=0;s<i.body.length;++s)o+=Vt(i.body[s],"textord").text;if("\\begin"===n){if(!Xr.hasOwnProperty(o))throw new r("No such environment: "+o,i);var l=Xr[o],{args:h,optArgs:m}=a.parseArguments("\\begin{"+o+"}",l),c={mode:a.mode,envName:o,parser:a},p=l.handler(c,h,m);a.expect("\\end",!1);var u=a.nextToken,d=Vt(a.parseFunction(),"environment");if(d.name!==o)throw new r("Mismatch: \\begin{"+o+"} matched by \\end{"+d.name+"}",u);return p}return{type:"environment",mode:a.mode,name:o,nameGroup:i}}});var _r=(e,t)=>{var r=e.font,a=t.withFont(r);return bt(e.body,a)},jr=(e,t)=>{var r=e.font,a=t.withFont(r);return Nt(e.body,a)},$r={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};at({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=it(t[0]),i=a;return i in $r&&(i=$r[i]),{type:"font",mode:r.mode,font:i.slice(1),body:n}},htmlBuilder:_r,mathmlBuilder:jr}),at({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{var{parser:r}=e,a=t[0],n=s.isCharacterBox(a);return{type:"mclass",mode:r.mode,mclass:$t(a),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:a}],isCharacterBox:n}}}),at({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a,breakOnTokenText:n}=e,{mode:i}=r,o=r.parseExpression(!0,n);return{type:"font",mode:i,font:"math"+a.slice(1),body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:_r,mathmlBuilder:jr});var Zr=(e,t)=>{var r=t;return"display"===e?r=r.id>=y.SCRIPT.id?r.text():y.DISPLAY:"text"===e&&r.size===y.DISPLAY.size?r=y.TEXT:"script"===e?r=y.SCRIPT:"scriptscript"===e&&(r=y.SCRIPTSCRIPT),r},Kr=(e,t)=>{var r,a=Zr(e.size,t.style),n=a.fracNum(),i=a.fracDen();r=t.havingStyle(n);var o=bt(e.numer,r,t);if(e.continued){var s=8.5/t.fontMetrics().ptPerEm,l=3.5/t.fontMetrics().ptPerEm;o.height=o.height<s?s:o.height,o.depth=o.depth<l?l:o.depth}r=t.havingStyle(i);var h,m,c,p,u,d,g,f,v,b,x=bt(e.denom,r,t);if(e.hasBarLine?(e.barSize?(m=D(e.barSize,t),h=je.makeLineSpan("frac-line",t,m)):h=je.makeLineSpan("frac-line",t),m=h.height,c=h.height):(h=null,m=0,c=t.fontMetrics().defaultRuleThickness),a.size===y.DISPLAY.size||"display"===e.size?(p=t.fontMetrics().num1,u=m>0?3*c:7*c,d=t.fontMetrics().denom1):(m>0?(p=t.fontMetrics().num2,u=c):(p=t.fontMetrics().num3,u=3*c),d=t.fontMetrics().denom2),h){var w=t.fontMetrics().axisHeight;p-o.depth-(w+.5*m)<u&&(p+=u-(p-o.depth-(w+.5*m))),w-.5*m-(x.height-d)<u&&(d+=u-(w-.5*m-(x.height-d)));var k=-(w-.5*m);g=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:x,shift:d},{type:"elem",elem:h,shift:k},{type:"elem",elem:o,shift:-p}]},t)}else{var S=p-o.depth-(x.height-d);S<u&&(p+=.5*(u-S),d+=.5*(u-S)),g=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:x,shift:d},{type:"elem",elem:o,shift:-p}]},t)}return r=t.havingStyle(a),g.height*=r.sizeMultiplier/t.sizeMultiplier,g.depth*=r.sizeMultiplier/t.sizeMultiplier,f=a.size===y.DISPLAY.size?t.fontMetrics().delim1:a.size===y.SCRIPTSCRIPT.size?t.havingStyle(y.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,v=null==e.leftDelim?vt(t,["mopen"]):Ar.customSizedDelim(e.leftDelim,f,!0,t.havingStyle(a),e.mode,["mopen"]),b=e.continued?je.makeSpan([]):null==e.rightDelim?vt(t,["mclose"]):Ar.customSizedDelim(e.rightDelim,f,!0,t.havingStyle(a),e.mode,["mclose"]),je.makeSpan(["mord"].concat(r.sizingClasses(t)),[v,je.makeSpan(["mfrac"],[g]),b],t)},Jr=(e,t)=>{var r=new Mt.MathNode("mfrac",[Nt(e.numer,t),Nt(e.denom,t)]);if(e.hasBarLine){if(e.barSize){var a=D(e.barSize,t);r.setAttribute("linethickness",V(a))}}else r.setAttribute("linethickness","0px");var n=Zr(e.size,t.style);if(n.size!==t.style.size){r=new Mt.MathNode("mstyle",[r]);var i=n.size===y.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",i),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){var o=[];if(null!=e.leftDelim){var s=new Mt.MathNode("mo",[new Mt.TextNode(e.leftDelim.replace("\\",""))]);s.setAttribute("fence","true"),o.push(s)}if(o.push(r),null!=e.rightDelim){var l=new Mt.MathNode("mo",[new Mt.TextNode(e.rightDelim.replace("\\",""))]);l.setAttribute("fence","true"),o.push(l)}return At(o)}return r};at({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{var r,{parser:a,funcName:n}=e,i=t[0],o=t[1],s=null,l=null,h="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,s="(",l=")";break;case"\\\\bracefrac":r=!1,s="\\{",l="\\}";break;case"\\\\brackfrac":r=!1,s="[",l="]";break;default:throw new Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:a.mode,continued:!1,numer:i,denom:o,hasBarLine:r,leftDelim:s,rightDelim:l,size:h,barSize:null}},htmlBuilder:Kr,mathmlBuilder:Jr}),at({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:i,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),at({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){var t,{parser:r,funcName:a,token:n}=e;switch(a){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:n}}});var Qr=["display","text","script","scriptscript"],ea=function(e){var t=null;return e.length>0&&(t="."===(t=e)?null:t),t};at({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){var r,{parser:a}=e,n=t[4],i=t[5],o=it(t[0]),s="atom"===o.type&&"open"===o.family?ea(o.text):null,l=it(t[1]),h="atom"===l.type&&"close"===l.family?ea(l.text):null,m=Vt(t[2],"size"),c=null;r=!!m.isBlank||(c=m.value).number>0;var p="auto",u=t[3];if("ordgroup"===u.type){if(u.body.length>0){var d=Vt(u.body[0],"textord");p=Qr[Number(d.text)]}}else u=Vt(u,"textord"),p=Qr[Number(u.text)];return{type:"genfrac",mode:a.mode,numer:n,denom:i,continued:!1,hasBarLine:r,barSize:c,leftDelim:s,rightDelim:h,size:p}},htmlBuilder:Kr,mathmlBuilder:Jr}),at({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){var{parser:r,funcName:a,token:n}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:Vt(t[0],"size").value,token:n}}}),at({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e}(Vt(t[1],"infix").size),o=t[2],s=i.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:o,continued:!1,hasBarLine:s,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Kr,mathmlBuilder:Jr});var ta=(e,t)=>{var r,a,n=t.style;"supsub"===e.type?(r=e.sup?bt(e.sup,t.havingStyle(n.sup()),t):bt(e.sub,t.havingStyle(n.sub()),t),a=Vt(e.base,"horizBrace")):a=Vt(e,"horizBrace");var i,o=bt(a.base,t.havingBaseStyle(y.DISPLAY)),s=Dt(a,t);if(a.isOver?(i=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:s}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(i=je.makeVList({positionType:"bottom",positionData:o.depth+.1+s.height,children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:o}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){var l=je.makeSpan(["mord",a.isOver?"mover":"munder"],[i],t);i=a.isOver?je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.2},{type:"elem",elem:r}]},t):je.makeVList({positionType:"bottom",positionData:l.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:l}]},t)}return je.makeSpan(["mord",a.isOver?"mover":"munder"],[i],t)};at({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"horizBrace",mode:r.mode,label:a,isOver:/^\\over/.test(a),base:t[0]}},htmlBuilder:ta,mathmlBuilder:(e,t)=>{var r=Lt(e.label);return new Mt.MathNode(e.isOver?"mover":"munder",[Nt(e.base,t),r])}}),at({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[1],n=Vt(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:r.mode,href:n,body:ot(a)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{var r=pt(e.body,t,!1);return je.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{var r=Ct(e.body,t);return r instanceof kt||(r=new kt("mrow",[r])),r.setAttribute("href",e.href),r}}),at({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=Vt(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:a}))return r.formatUnsupportedCmd("\\url");for(var n=[],i=0;i<a.length;i++){var o=a[i];"~"===o&&(o="\\textasciitilde"),n.push({type:"textord",mode:"text",text:o})}var s={type:"text",mode:r.mode,font:"\\texttt",body:n};return{type:"href",mode:r.mode,href:a,body:ot(s)}}}),at({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){var{parser:r}=e;return{type:"hbox",mode:r.mode,body:ot(t[0])}},htmlBuilder(e,t){var r=pt(e.body,t,!1);return je.makeFragment(r)},mathmlBuilder:(e,t)=>new Mt.MathNode("mrow",Bt(e.body,t))}),at({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{var a,{parser:n,funcName:i,token:o}=e,s=Vt(t[0],"raw").string,l=t[1];n.settings.strict&&n.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h={};switch(i){case"\\htmlClass":h.class=s,a={command:"\\htmlClass",class:s};break;case"\\htmlId":h.id=s,a={command:"\\htmlId",id:s};break;case"\\htmlStyle":h.style=s,a={command:"\\htmlStyle",style:s};break;case"\\htmlData":for(var m=s.split(","),c=0;c<m.length;c++){var p=m[c].split("=");if(2!==p.length)throw new r("Error parsing key-value for \\htmlData");h["data-"+p[0].trim()]=p[1].trim()}a={command:"\\htmlData",attributes:h};break;default:throw new Error("Unrecognized html command")}return n.settings.isTrusted(a)?{type:"html",mode:n.mode,attributes:h,body:ot(l)}:n.formatUnsupportedCmd(i)},htmlBuilder:(e,t)=>{var r=pt(e.body,t,!1),a=["enclosing"];e.attributes.class&&a.push(...e.attributes.class.trim().split(/\s+/));var n=je.makeSpan(a,r,t);for(var i in e.attributes)"class"!==i&&e.attributes.hasOwnProperty(i)&&n.setAttribute(i,e.attributes[i]);return n},mathmlBuilder:(e,t)=>Ct(e.body,t)}),at({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:ot(t[0]),mathml:ot(t[1])}},htmlBuilder:(e,t)=>{var r=pt(e.html,t,!1);return je.makeFragment(r)},mathmlBuilder:(e,t)=>Ct(e.mathml,t)});var ra=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new r("Invalid size: '"+e+"' in \\includegraphics");var a={number:+(t[1]+t[2]),unit:t[3]};if(!L(a))throw new r("Invalid unit: '"+a.unit+"' in \\includegraphics.");return a};at({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,a)=>{var{parser:n}=e,i={number:0,unit:"em"},o={number:.9,unit:"em"},s={number:0,unit:"em"},l="";if(a[0])for(var h=Vt(a[0],"raw").string.split(","),m=0;m<h.length;m++){var c=h[m].split("=");if(2===c.length){var p=c[1].trim();switch(c[0].trim()){case"alt":l=p;break;case"width":i=ra(p);break;case"height":o=ra(p);break;case"totalheight":s=ra(p);break;default:throw new r("Invalid key: '"+c[0]+"' in \\includegraphics.")}}}var u=Vt(t[0],"url").url;return""===l&&(l=(l=(l=u).replace(/^.*[\\/]/,"")).substring(0,l.lastIndexOf("."))),n.settings.isTrusted({command:"\\includegraphics",url:u})?{type:"includegraphics",mode:n.mode,alt:l,width:i,height:o,totalheight:s,src:u}:n.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{var r=D(e.height,t),a=0;e.totalheight.number>0&&(a=D(e.totalheight,t)-r);var n=0;e.width.number>0&&(n=D(e.width,t));var i={height:V(r+a)};n>0&&(i.width=V(n)),a>0&&(i.verticalAlign=V(-a));var o=new X(e.src,e.alt,i);return o.height=r,o.depth=a,o},mathmlBuilder:(e,t)=>{var r=new Mt.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);var a=D(e.height,t),n=0;if(e.totalheight.number>0&&(n=D(e.totalheight,t)-a,r.setAttribute("valign",V(-n))),r.setAttribute("height",V(a+n)),e.width.number>0){var i=D(e.width,t);r.setAttribute("width",V(i))}return r.setAttribute("src",e.src),r}}),at({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=Vt(t[0],"size");if(r.settings.strict){var i="m"===a[1],o="mu"===n.value.unit;i?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, not "+n.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:n.value}},htmlBuilder:(e,t)=>je.makeGlue(e.dimension,t),mathmlBuilder(e,t){var r=D(e.dimension,t);return new Mt.SpaceNode(r)}}),at({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"lap",mode:r.mode,alignment:a.slice(5),body:n}},htmlBuilder:(e,t)=>{var r;"clap"===e.alignment?(r=je.makeSpan([],[bt(e.body,t)]),r=je.makeSpan(["inner"],[r],t)):r=je.makeSpan(["inner"],[bt(e.body,t)]);var a=je.makeSpan(["fix"],[]),n=je.makeSpan([e.alignment],[r,a],t),i=je.makeSpan(["strut"]);return i.style.height=V(n.height+n.depth),n.depth&&(i.style.verticalAlign=V(-n.depth)),n.children.unshift(i),n=je.makeSpan(["thinbox"],[n],t),je.makeSpan(["mord","vbox"],[n],t)},mathmlBuilder:(e,t)=>{var r=new Mt.MathNode("mpadded",[Nt(e.body,t)]);if("rlap"!==e.alignment){var a="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",a+"width")}return r.setAttribute("width","0px"),r}}),at({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){var{funcName:r,parser:a}=e,n=a.mode;a.switchMode("math");var i="\\("===r?"\\)":"$",o=a.parseExpression(!1,i);return a.expect(i),a.switchMode(n),{type:"styling",mode:a.mode,style:"text",body:o}}}),at({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new r("Mismatched "+e.funcName)}});var aa=(e,t)=>{switch(t.style.size){case y.DISPLAY.size:return e.display;case y.TEXT.size:return e.text;case y.SCRIPT.size:return e.script;case y.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};at({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:ot(t[0]),text:ot(t[1]),script:ot(t[2]),scriptscript:ot(t[3])}},htmlBuilder:(e,t)=>{var r=aa(e,t),a=pt(r,t,!1);return je.makeFragment(a)},mathmlBuilder:(e,t)=>{var r=aa(e,t);return Ct(r,t)}});var na=(e,t,r,a,n,i,o)=>{e=je.makeSpan([],[e]);var l,h,m,c=r&&s.isCharacterBox(r);if(t){var p=bt(t,a.havingStyle(n.sup()),a);h={elem:p,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-p.depth)}}if(r){var u=bt(r,a.havingStyle(n.sub()),a);l={elem:u,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-u.height)}}if(h&&l){var d=a.fontMetrics().bigOpSpacing5+l.elem.height+l.elem.depth+l.kern+e.depth+o;m=je.makeVList({positionType:"bottom",positionData:d,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:l.elem,marginLeft:V(-i)},{type:"kern",size:l.kern},{type:"elem",elem:e},{type:"kern",size:h.kern},{type:"elem",elem:h.elem,marginLeft:V(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(l){var g=e.height-o;m=je.makeVList({positionType:"top",positionData:g,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:l.elem,marginLeft:V(-i)},{type:"kern",size:l.kern},{type:"elem",elem:e}]},a)}else{if(!h)return e;var f=e.depth+o;m=je.makeVList({positionType:"bottom",positionData:f,children:[{type:"elem",elem:e},{type:"kern",size:h.kern},{type:"elem",elem:h.elem,marginLeft:V(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}var v=[m];if(l&&0!==i&&!c){var b=je.makeSpan(["mspace"],[],a);b.style.marginRight=V(i),v.unshift(b)}return je.makeSpan(["mop","op-limits"],v,a)},ia=["\\smallint"],oa=(e,t)=>{var r,a,n,i=!1;"supsub"===e.type?(r=e.sup,a=e.sub,n=Vt(e.base,"op"),i=!0):n=Vt(e,"op");var o,l=t.style,h=!1;if(l.size===y.DISPLAY.size&&n.symbol&&!s.contains(ia,n.name)&&(h=!0),n.symbol){var m=h?"Size2-Regular":"Size1-Regular",c="";if("\\oiint"!==n.name&&"\\oiiint"!==n.name||(c=n.name.slice(1),n.name="oiint"===c?"\\iint":"\\iiint"),o=je.makeSymbol(n.name,m,"math",t,["mop","op-symbol",h?"large-op":"small-op"]),c.length>0){var p=o.italic,u=je.staticSvg(c+"Size"+(h?"2":"1"),t);o=je.makeVList({positionType:"individualShift",children:[{type:"elem",elem:o,shift:0},{type:"elem",elem:u,shift:h?.08:0}]},t),n.name="\\"+c,o.classes.unshift("mop"),o.italic=p}}else if(n.body){var d=pt(n.body,t,!0);1===d.length&&d[0]instanceof j?(o=d[0]).classes[0]="mop":o=je.makeSpan(["mop"],d,t)}else{for(var g=[],f=1;f<n.name.length;f++)g.push(je.mathsym(n.name[f],n.mode,t));o=je.makeSpan(["mop"],g,t)}var v=0,b=0;return(o instanceof j||"\\oiint"===n.name||"\\oiiint"===n.name)&&!n.suppressBaseShift&&(v=(o.height-o.depth)/2-t.fontMetrics().axisHeight,b=o.italic),i?na(o,r,a,t,l,b,v):(v&&(o.style.position="relative",o.style.top=V(v)),o)},sa=(e,t)=>{var r;if(e.symbol)r=new kt("mo",[zt(e.name,e.mode)]),s.contains(ia,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new kt("mo",Bt(e.body,t));else{r=new kt("mi",[new St(e.name.slice(1))]);var a=new kt("mo",[zt("⁡","text")]);r=e.parentIsSupSub?new kt("mrow",[r,a]):wt([r,a])}return r},la={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};at({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=a;return 1===n.length&&(n=la[n]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:oa,mathmlBuilder:sa}),at({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:ot(a)}},htmlBuilder:oa,mathmlBuilder:sa});var ha={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};at({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:oa,mathmlBuilder:sa}),at({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:oa,mathmlBuilder:sa}),at({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e,a=r;return 1===a.length&&(a=ha[a]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:oa,mathmlBuilder:sa});var ma=(e,t)=>{var r,a,n,i,o=!1;if("supsub"===e.type?(r=e.sup,a=e.sub,n=Vt(e.base,"operatorname"),o=!0):n=Vt(e,"operatorname"),n.body.length>0){for(var s=n.body.map((e=>{var t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e})),l=pt(s,t.withFont("mathrm"),!0),h=0;h<l.length;h++){var m=l[h];m instanceof j&&(m.text=m.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}i=je.makeSpan(["mop"],l,t)}else i=je.makeSpan(["mop"],[],t);return o?na(i,r,a,t,t.style,0,0):i};function ca(e,t,r){for(var a=pt(e,t,!1),n=t.sizeMultiplier/r.sizeMultiplier,i=0;i<a.length;i++){var o=a[i].classes.indexOf("sizing");o<0?Array.prototype.push.apply(a[i].classes,t.sizingClasses(r)):a[i].classes[o+1]==="reset-size"+t.size&&(a[i].classes[o+1]="reset-size"+r.size),a[i].height*=n,a[i].depth*=n}return je.makeFragment(a)}at({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"operatorname",mode:r.mode,body:ot(n),alwaysHandleSupSub:"\\operatornamewithlimits"===a,limits:!1,parentIsSupSub:!1}},htmlBuilder:ma,mathmlBuilder:(e,t)=>{for(var r=Bt(e.body,t.withFont("mathrm")),a=!0,n=0;n<r.length;n++){var i=r[n];if(i instanceof Mt.SpaceNode);else if(i instanceof Mt.MathNode)switch(i.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var o=i.children[0];1===i.children.length&&o instanceof Mt.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break;default:a=!1}else a=!1}if(a){var s=r.map((e=>e.toText())).join("");r=[new Mt.TextNode(s)]}var l=new Mt.MathNode("mi",r);l.setAttribute("mathvariant","normal");var h=new Mt.MathNode("mo",[zt("⁡","text")]);return e.parentIsSupSub?new Mt.MathNode("mrow",[l,h]):Mt.newDocumentFragment([l,h])}}),Er("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),nt({type:"ordgroup",htmlBuilder:(e,t)=>e.semisimple?je.makeFragment(pt(e.body,t,!1)):je.makeSpan(["mord"],pt(e.body,t,!0),t),mathmlBuilder:(e,t)=>Ct(e.body,t,!0)}),at({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){var{parser:r}=e,a=t[0];return{type:"overline",mode:r.mode,body:a}},htmlBuilder(e,t){var r=bt(e.body,t.havingCrampedStyle()),a=je.makeLineSpan("overline-line",t),n=t.fontMetrics().defaultRuleThickness,i=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:a},{type:"kern",size:n}]},t);return je.makeSpan(["mord","overline"],[i],t)},mathmlBuilder(e,t){var r=new Mt.MathNode("mo",[new Mt.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new Mt.MathNode("mover",[Nt(e.body,t),r]);return a.setAttribute("accent","true"),a}}),at({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"phantom",mode:r.mode,body:ot(a)}},htmlBuilder:(e,t)=>{var r=pt(e.body,t.withPhantom(),!1);return je.makeFragment(r)},mathmlBuilder:(e,t)=>{var r=Bt(e.body,t);return new Mt.MathNode("mphantom",r)}}),at({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"hphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=je.makeSpan([],[bt(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var a=0;a<r.children.length;a++)r.children[a].height=0,r.children[a].depth=0;return r=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),je.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{var r=Bt(ot(e.body),t),a=new Mt.MathNode("mphantom",r),n=new Mt.MathNode("mpadded",[a]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}}),at({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"vphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=je.makeSpan(["inner"],[bt(e.body,t.withPhantom())]),a=je.makeSpan(["fix"],[]);return je.makeSpan(["mord","rlap"],[r,a],t)},mathmlBuilder:(e,t)=>{var r=Bt(ot(e.body),t),a=new Mt.MathNode("mphantom",r),n=new Mt.MathNode("mpadded",[a]);return n.setAttribute("width","0px"),n}}),at({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e,a=Vt(t[0],"size").value,n=t[1];return{type:"raisebox",mode:r.mode,dy:a,body:n}},htmlBuilder(e,t){var r=bt(e.body,t),a=D(e.dy,t);return je.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){var r=new Mt.MathNode("mpadded",[Nt(e.body,t)]),a=e.dy.number+e.dy.unit;return r.setAttribute("voffset",a),r}}),at({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t}=e;return{type:"internal",mode:t.mode}}}),at({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(e,t,r){var{parser:a}=e,n=r[0],i=Vt(t[0],"size"),o=Vt(t[1],"size");return{type:"rule",mode:a.mode,shift:n&&Vt(n,"size").value,width:i.value,height:o.value}},htmlBuilder(e,t){var r=je.makeSpan(["mord","rule"],[],t),a=D(e.width,t),n=D(e.height,t),i=e.shift?D(e.shift,t):0;return r.style.borderRightWidth=V(a),r.style.borderTopWidth=V(n),r.style.bottom=V(i),r.width=a,r.height=n+i,r.depth=-i,r.maxFontSize=1.125*n*t.sizeMultiplier,r},mathmlBuilder(e,t){var r=D(e.width,t),a=D(e.height,t),n=e.shift?D(e.shift,t):0,i=t.color&&t.getColor()||"black",o=new Mt.MathNode("mspace");o.setAttribute("mathbackground",i),o.setAttribute("width",V(r)),o.setAttribute("height",V(a));var s=new Mt.MathNode("mpadded",[o]);return n>=0?s.setAttribute("height",V(n)):(s.setAttribute("height",V(n)),s.setAttribute("depth",V(-n))),s.setAttribute("voffset",V(n)),s}});var pa=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];at({type:"sizing",names:pa,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!1,r);return{type:"sizing",mode:n.mode,size:pa.indexOf(a)+1,body:i}},htmlBuilder:(e,t)=>{var r=t.havingSize(e.size);return ca(e.body,r,t)},mathmlBuilder:(e,t)=>{var r=t.havingSize(e.size),a=Bt(e.body,r),n=new Mt.MathNode("mstyle",a);return n.setAttribute("mathsize",V(r.sizeMultiplier)),n}}),at({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{var{parser:a}=e,n=!1,i=!1,o=r[0]&&Vt(r[0],"ordgroup");if(o)for(var s="",l=0;l<o.body.length;++l){if("t"===(s=o.body[l].text))n=!0;else{if("b"!==s){n=!1,i=!1;break}i=!0}}else n=!0,i=!0;var h=t[0];return{type:"smash",mode:a.mode,body:h,smashHeight:n,smashDepth:i}},htmlBuilder:(e,t)=>{var r=je.makeSpan([],[bt(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].depth=0;var i=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return je.makeSpan(["mord"],[i],t)},mathmlBuilder:(e,t)=>{var r=new Mt.MathNode("mpadded",[Nt(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),at({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a}=e,n=r[0],i=t[0];return{type:"sqrt",mode:a.mode,body:i,index:n}},htmlBuilder(e,t){var r=bt(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=je.wrapFragment(r,t);var a=t.fontMetrics().defaultRuleThickness,n=a;t.style.id<y.TEXT.id&&(n=t.fontMetrics().xHeight);var i=a+n/4,o=r.height+r.depth+i+a,{span:s,ruleWidth:l,advanceWidth:h}=Ar.sqrtImage(o,t),m=s.height-l;m>r.height+r.depth+i&&(i=(i+m-r.height-r.depth)/2);var c=s.height-r.height-i-l;r.style.paddingLeft=V(h);var p=je.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+c)},{type:"elem",elem:s},{type:"kern",size:l}]},t);if(e.index){var u=t.havingStyle(y.SCRIPTSCRIPT),d=bt(e.index,u,t),g=.6*(p.height-p.depth),f=je.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:d}]},t),v=je.makeSpan(["root"],[f]);return je.makeSpan(["mord","sqrt"],[v,p],t)}return je.makeSpan(["mord","sqrt"],[p],t)},mathmlBuilder(e,t){var{body:r,index:a}=e;return a?new Mt.MathNode("mroot",[Nt(r,t),Nt(a,t)]):new Mt.MathNode("msqrt",[Nt(r,t)])}});var ua={display:y.DISPLAY,text:y.TEXT,script:y.SCRIPT,scriptscript:y.SCRIPTSCRIPT};at({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!0,r),o=a.slice(1,a.length-5);return{type:"styling",mode:n.mode,style:o,body:i}},htmlBuilder(e,t){var r=ua[e.style],a=t.havingStyle(r).withFont("");return ca(e.body,a,t)},mathmlBuilder(e,t){var r=ua[e.style],a=t.havingStyle(r),n=Bt(e.body,a),i=new Mt.MathNode("mstyle",n),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return i.setAttribute("scriptlevel",o[0]),i.setAttribute("displaystyle",o[1]),i}});nt({type:"supsub",htmlBuilder(e,t){var r=function(e,t){var r=e.base;return r?"op"===r.type?r.limits&&(t.style.size===y.DISPLAY.size||r.alwaysHandleSupSub)?oa:null:"operatorname"===r.type?r.alwaysHandleSupSub&&(t.style.size===y.DISPLAY.size||r.limits)?ma:null:"accent"===r.type?s.isCharacterBox(r.base)?Gt:null:"horizBrace"===r.type&&!e.sub===r.isOver?ta:null:null}(e,t);if(r)return r(e,t);var a,n,i,{base:o,sup:l,sub:h}=e,m=bt(o,t),c=t.fontMetrics(),p=0,u=0,d=o&&s.isCharacterBox(o);if(l){var g=t.havingStyle(t.style.sup());a=bt(l,g,t),d||(p=m.height-g.fontMetrics().supDrop*g.sizeMultiplier/t.sizeMultiplier)}if(h){var f=t.havingStyle(t.style.sub());n=bt(h,f,t),d||(u=m.depth+f.fontMetrics().subDrop*f.sizeMultiplier/t.sizeMultiplier)}i=t.style===y.DISPLAY?c.sup1:t.style.cramped?c.sup3:c.sup2;var v,b=t.sizeMultiplier,x=V(.5/c.ptPerEm/b),w=null;if(n){var k=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(m instanceof j||k)&&(w=V(-m.italic))}if(a&&n){p=Math.max(p,i,a.depth+.25*c.xHeight),u=Math.max(u,c.sub2);var S=4*c.defaultRuleThickness;if(p-a.depth-(n.height-u)<S){u=S-(p-a.depth)+n.height;var M=.8*c.xHeight-(p-a.depth);M>0&&(p+=M,u-=M)}var z=[{type:"elem",elem:n,shift:u,marginRight:x,marginLeft:w},{type:"elem",elem:a,shift:-p,marginRight:x}];v=je.makeVList({positionType:"individualShift",children:z},t)}else if(n){u=Math.max(u,c.sub1,n.height-.8*c.xHeight);var A=[{type:"elem",elem:n,marginLeft:w,marginRight:x}];v=je.makeVList({positionType:"shift",positionData:u,children:A},t)}else{if(!a)throw new Error("supsub must have either sup or sub.");p=Math.max(p,i,a.depth+.25*c.xHeight),v=je.makeVList({positionType:"shift",positionData:-p,children:[{type:"elem",elem:a,marginRight:x}]},t)}var T=ft(m,"right")||"mord";return je.makeSpan([T],[m,je.makeSpan(["msupsub"],[v])],t)},mathmlBuilder(e,t){var r,a=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(a=!0,r=e.base.isOver),!e.base||"op"!==e.base.type&&"operatorname"!==e.base.type||(e.base.parentIsSupSub=!0);var n,i=[Nt(e.base,t)];if(e.sub&&i.push(Nt(e.sub,t)),e.sup&&i.push(Nt(e.sup,t)),a)n=r?"mover":"munder";else if(e.sub)if(e.sup){var o=e.base;n=o&&"op"===o.type&&o.limits&&t.style===y.DISPLAY||o&&"operatorname"===o.type&&o.alwaysHandleSupSub&&(t.style===y.DISPLAY||o.limits)?"munderover":"msubsup"}else{var s=e.base;n=s&&"op"===s.type&&s.limits&&(t.style===y.DISPLAY||s.alwaysHandleSupSub)||s&&"operatorname"===s.type&&s.alwaysHandleSupSub&&(s.limits||t.style===y.DISPLAY)?"munder":"msub"}else{var l=e.base;n=l&&"op"===l.type&&l.limits&&(t.style===y.DISPLAY||l.alwaysHandleSupSub)||l&&"operatorname"===l.type&&l.alwaysHandleSupSub&&(l.limits||t.style===y.DISPLAY)?"mover":"msup"}return new Mt.MathNode(n,i)}}),nt({type:"atom",htmlBuilder:(e,t)=>je.mathsym(e.text,e.mode,t,["m"+e.family]),mathmlBuilder(e,t){var r=new Mt.MathNode("mo",[zt(e.text,e.mode)]);if("bin"===e.family){var a=Tt(e,t);"bold-italic"===a&&r.setAttribute("mathvariant",a)}else"punct"===e.family?r.setAttribute("separator","true"):"open"!==e.family&&"close"!==e.family||r.setAttribute("stretchy","false");return r}});var da={mi:"italic",mn:"normal",mtext:"normal"};nt({type:"mathord",htmlBuilder:(e,t)=>je.makeOrd(e,t,"mathord"),mathmlBuilder(e,t){var r=new Mt.MathNode("mi",[zt(e.text,e.mode,t)]),a=Tt(e,t)||"italic";return a!==da[r.type]&&r.setAttribute("mathvariant",a),r}}),nt({type:"textord",htmlBuilder:(e,t)=>je.makeOrd(e,t,"textord"),mathmlBuilder(e,t){var r,a=zt(e.text,e.mode,t),n=Tt(e,t)||"normal";return r="text"===e.mode?new Mt.MathNode("mtext",[a]):/[0-9]/.test(e.text)?new Mt.MathNode("mn",[a]):"\\prime"===e.text?new Mt.MathNode("mo",[a]):new Mt.MathNode("mi",[a]),n!==da[r.type]&&r.setAttribute("mathvariant",n),r}});var ga={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},fa={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};nt({type:"spacing",htmlBuilder(e,t){if(fa.hasOwnProperty(e.text)){var a=fa[e.text].className||"";if("text"===e.mode){var n=je.makeOrd(e,t,"textord");return n.classes.push(a),n}return je.makeSpan(["mspace",a],[je.mathsym(e.text,e.mode,t)],t)}if(ga.hasOwnProperty(e.text))return je.makeSpan(["mspace",ga[e.text]],[],t);throw new r('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){if(!fa.hasOwnProperty(e.text)){if(ga.hasOwnProperty(e.text))return new Mt.MathNode("mspace");throw new r('Unknown type of space "'+e.text+'"')}return new Mt.MathNode("mtext",[new Mt.TextNode(" ")])}});var va=()=>{var e=new Mt.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};nt({type:"tag",mathmlBuilder(e,t){var r=new Mt.MathNode("mtable",[new Mt.MathNode("mtr",[va(),new Mt.MathNode("mtd",[Ct(e.body,t)]),va(),new Mt.MathNode("mtd",[Ct(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});var ba={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},ya={"\\textbf":"textbf","\\textmd":"textmd"},xa={"\\textit":"textit","\\textup":"textup"},wa=(e,t)=>{var r=e.font;return r?ba[r]?t.withTextFontFamily(ba[r]):ya[r]?t.withTextFontWeight(ya[r]):t.withTextFontShape(xa[r]):t};at({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"text",mode:r.mode,body:ot(n),font:a}},htmlBuilder(e,t){var r=wa(e,t),a=pt(e.body,r,!0);return je.makeSpan(["mord","text"],a,r)},mathmlBuilder(e,t){var r=wa(e,t);return Ct(e.body,r)}}),at({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=bt(e.body,t),a=je.makeLineSpan("underline-line",t),n=t.fontMetrics().defaultRuleThickness,i=je.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n},{type:"elem",elem:a},{type:"kern",size:3*n},{type:"elem",elem:r}]},t);return je.makeSpan(["mord","underline"],[i],t)},mathmlBuilder(e,t){var r=new Mt.MathNode("mo",[new Mt.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new Mt.MathNode("munder",[Nt(e.body,t),r]);return a.setAttribute("accentunder","true"),a}}),at({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=bt(e.body,t),a=t.fontMetrics().axisHeight,n=.5*(r.height-a-(r.depth+a));return je.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:(e,t)=>new Mt.MathNode("mpadded",[Nt(e.body,t)],["vcenter"])}),at({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,a){throw new r("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){for(var r=ka(e),a=[],n=t.havingStyle(t.style.text()),i=0;i<r.length;i++){var o=r[i];"~"===o&&(o="\\textasciitilde"),a.push(je.makeSymbol(o,"Typewriter-Regular",e.mode,n,["mord","texttt"]))}return je.makeSpan(["mord","text"].concat(n.sizingClasses(t)),je.tryCombineChars(a),n)},mathmlBuilder(e,t){var r=new Mt.TextNode(ka(e)),a=new Mt.MathNode("mtext",[r]);return a.setAttribute("mathvariant","monospace"),a}});var ka=e=>e.body.replace(/ /g,e.star?"␣":" "),Sa=et,Ma="[ \r\n\t]",za="(\\\\[a-zA-Z@]+)"+Ma+"*",Aa="[̀-ͯ]",Ta=new RegExp(Aa+"+$"),Ba="("+Ma+"+)|\\\\(\n|[ \r\t]+\n?)[ \r\t]*|([!-\\[\\]-‧‪-퟿豈-￿]"+Aa+"*|[\ud800-\udbff][\udc00-\udfff]"+Aa+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|"+za+"|\\\\[^\ud800-\udfff])";class Ca{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(Ba,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var a=this.input,n=this.tokenRegex.lastIndex;if(n===a.length)return new t("EOF",new e(this,n,n));var i=this.tokenRegex.exec(a);if(null===i||i.index!==n)throw new r("Unexpected character: '"+a[n]+"'",new t(a[n],new e(this,n,n+1)));var o=i[6]||i[3]||(i[2]?"\\ ":" ");if(14===this.catcodes[o]){var s=a.indexOf("\n",this.tokenRegex.lastIndex);return-1===s?(this.tokenRegex.lastIndex=a.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=s+1,this.lex()}return new t(o,new e(this,n,this.tokenRegex.lastIndex))}}class Na{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new r("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(var a=0;a<this.undefStack.length;a++)delete this.undefStack[a][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var n=this.undefStack[this.undefStack.length-1];n&&!n.hasOwnProperty(e)&&(n[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}}var qa=Or;Er("\\noexpand",(function(e){var t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}})),Er("\\expandafter",(function(e){var t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}})),Er("\\@firstoftwo",(function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}})),Er("\\@secondoftwo",(function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}})),Er("\\@ifnextchar",(function(e){var t=e.consumeArgs(3);e.consumeSpaces();var r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}})),Er("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),Er("\\TextOrMath",(function(e){var t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}}));var Ia={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};Er("\\char",(function(e){var t,a=e.popToken(),n="";if("'"===a.text)t=8,a=e.popToken();else if('"'===a.text)t=16,a=e.popToken();else if("`"===a.text)if("\\"===(a=e.popToken()).text[0])n=a.text.charCodeAt(1);else{if("EOF"===a.text)throw new r("\\char` missing argument");n=a.text.charCodeAt(0)}else t=10;if(t){if(null==(n=Ia[a.text])||n>=t)throw new r("Invalid base-"+t+" digit "+a.text);for(var i;null!=(i=Ia[e.future().text])&&i<t;)n*=t,n+=i,e.popToken()}return"\\@char{"+n+"}"}));var Ra=(e,t,a)=>{var n=e.consumeArg().tokens;if(1!==n.length)throw new r("\\newcommand's first argument must be a macro name");var i=n[0].text,o=e.isDefined(i);if(o&&!t)throw new r("\\newcommand{"+i+"} attempting to redefine "+i+"; use \\renewcommand");if(!o&&!a)throw new r("\\renewcommand{"+i+"} when command "+i+" does not yet exist; use \\newcommand");var s=0;if(1===(n=e.consumeArg().tokens).length&&"["===n[0].text){for(var l="",h=e.expandNextToken();"]"!==h.text&&"EOF"!==h.text;)l+=h.text,h=e.expandNextToken();if(!l.match(/^\s*[0-9]+\s*$/))throw new r("Invalid number of arguments: "+l);s=parseInt(l),n=e.consumeArg().tokens}return e.macros.set(i,{tokens:n,numArgs:s}),""};Er("\\newcommand",(e=>Ra(e,!1,!0))),Er("\\renewcommand",(e=>Ra(e,!0,!1))),Er("\\providecommand",(e=>Ra(e,!0,!0))),Er("\\message",(e=>{e.consumeArgs(1)[0];return""})),Er("\\errmessage",(e=>{e.consumeArgs(1)[0];return""})),Er("\\show",(e=>{e.popToken().text;return""})),Er("\\bgroup","{"),Er("\\egroup","}"),Er("~","\\nobreakspace"),Er("\\lq","`"),Er("\\rq","'"),Er("\\aa","\\r a"),Er("\\AA","\\r A"),Er("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}"),Er("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),Er("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}"),Er("ℬ","\\mathscr{B}"),Er("ℰ","\\mathscr{E}"),Er("ℱ","\\mathscr{F}"),Er("ℋ","\\mathscr{H}"),Er("ℐ","\\mathscr{I}"),Er("ℒ","\\mathscr{L}"),Er("ℳ","\\mathscr{M}"),Er("ℛ","\\mathscr{R}"),Er("ℭ","\\mathfrak{C}"),Er("ℌ","\\mathfrak{H}"),Er("ℨ","\\mathfrak{Z}"),Er("\\Bbbk","\\Bbb{k}"),Er("·","\\cdotp"),Er("\\llap","\\mathllap{\\textrm{#1}}"),Er("\\rlap","\\mathrlap{\\textrm{#1}}"),Er("\\clap","\\mathclap{\\textrm{#1}}"),Er("\\mathstrut","\\vphantom{(}"),Er("\\underbar","\\underline{\\text{#1}}"),Er("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),Er("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}"),Er("\\ne","\\neq"),Er("≠","\\neq"),Er("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}"),Er("∉","\\notin"),Er("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}"),Er("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}"),Er("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}"),Er("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}"),Er("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}"),Er("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}"),Er("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}"),Er("⟂","\\perp"),Er("‼","\\mathclose{!\\mkern-0.8mu!}"),Er("∌","\\notni"),Er("⌜","\\ulcorner"),Er("⌝","\\urcorner"),Er("⌞","\\llcorner"),Er("⌟","\\lrcorner"),Er("©","\\copyright"),Er("®","\\textregistered"),Er("️","\\textregistered"),Er("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),Er("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),Er("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),Er("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),Er("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),Er("⋮","\\vdots"),Er("\\varGamma","\\mathit{\\Gamma}"),Er("\\varDelta","\\mathit{\\Delta}"),Er("\\varTheta","\\mathit{\\Theta}"),Er("\\varLambda","\\mathit{\\Lambda}"),Er("\\varXi","\\mathit{\\Xi}"),Er("\\varPi","\\mathit{\\Pi}"),Er("\\varSigma","\\mathit{\\Sigma}"),Er("\\varUpsilon","\\mathit{\\Upsilon}"),Er("\\varPhi","\\mathit{\\Phi}"),Er("\\varPsi","\\mathit{\\Psi}"),Er("\\varOmega","\\mathit{\\Omega}"),Er("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),Er("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),Er("\\boxed","\\fbox{$\\displaystyle{#1}$}"),Er("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),Er("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),Er("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var Ha={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};Er("\\dots",(function(e){var t="\\dotso",r=e.expandAfterFuture().text;return r in Ha?t=Ha[r]:("\\not"===r.slice(0,4)||r in te.math&&s.contains(["bin","rel"],te.math[r].group))&&(t="\\dotsb"),t}));var Oa={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};Er("\\dotso",(function(e){return e.future().text in Oa?"\\ldots\\,":"\\ldots"})),Er("\\dotsc",(function(e){var t=e.future().text;return t in Oa&&","!==t?"\\ldots\\,":"\\ldots"})),Er("\\cdots",(function(e){return e.future().text in Oa?"\\@cdots\\,":"\\@cdots"})),Er("\\dotsb","\\cdots"),Er("\\dotsm","\\cdots"),Er("\\dotsi","\\!\\cdots"),Er("\\dotsx","\\ldots\\,"),Er("\\DOTSI","\\relax"),Er("\\DOTSB","\\relax"),Er("\\DOTSX","\\relax"),Er("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),Er("\\,","\\tmspace+{3mu}{.1667em}"),Er("\\thinspace","\\,"),Er("\\>","\\mskip{4mu}"),Er("\\:","\\tmspace+{4mu}{.2222em}"),Er("\\medspace","\\:"),Er("\\;","\\tmspace+{5mu}{.2777em}"),Er("\\thickspace","\\;"),Er("\\!","\\tmspace-{3mu}{.1667em}"),Er("\\negthinspace","\\!"),Er("\\negmedspace","\\tmspace-{4mu}{.2222em}"),Er("\\negthickspace","\\tmspace-{5mu}{.277em}"),Er("\\enspace","\\kern.5em "),Er("\\enskip","\\hskip.5em\\relax"),Er("\\quad","\\hskip1em\\relax"),Er("\\qquad","\\hskip2em\\relax"),Er("\\tag","\\@ifstar\\tag@literal\\tag@paren"),Er("\\tag@paren","\\tag@literal{({#1})}"),Er("\\tag@literal",(e=>{if(e.macros.get("\\df@tag"))throw new r("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"})),Er("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),Er("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),Er("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),Er("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),Er("\\newline","\\\\\\relax"),Er("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var Ea=V(A["Main-Regular"]["T".charCodeAt(0)][1]-.7*A["Main-Regular"]["A".charCodeAt(0)][1]);Er("\\LaTeX","\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+Ea+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}"),Er("\\KaTeX","\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+Ea+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}"),Er("\\hspace","\\@ifstar\\@hspacer\\@hspace"),Er("\\@hspace","\\hskip #1\\relax"),Er("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),Er("\\ordinarycolon",":"),Er("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),Er("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),Er("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),Er("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),Er("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),Er("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),Er("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),Er("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),Er("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),Er("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),Er("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),Er("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),Er("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),Er("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),Er("∷","\\dblcolon"),Er("∹","\\eqcolon"),Er("≔","\\coloneqq"),Er("≕","\\eqqcolon"),Er("⩴","\\Coloneqq"),Er("\\ratio","\\vcentcolon"),Er("\\coloncolon","\\dblcolon"),Er("\\colonequals","\\coloneqq"),Er("\\coloncolonequals","\\Coloneqq"),Er("\\equalscolon","\\eqqcolon"),Er("\\equalscoloncolon","\\Eqqcolon"),Er("\\colonminus","\\coloneq"),Er("\\coloncolonminus","\\Coloneq"),Er("\\minuscolon","\\eqcolon"),Er("\\minuscoloncolon","\\Eqcolon"),Er("\\coloncolonapprox","\\Colonapprox"),Er("\\coloncolonsim","\\Colonsim"),Er("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),Er("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),Er("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),Er("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),Er("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}"),Er("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),Er("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),Er("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),Er("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),Er("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),Er("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),Er("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),Er("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),Er("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}"),Er("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}"),Er("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}"),Er("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}"),Er("\\nleqq","\\html@mathml{\\@nleqq}{≰}"),Er("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}"),Er("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}"),Er("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}"),Er("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}"),Er("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}"),Er("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}"),Er("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}"),Er("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}"),Er("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}"),Er("\\imath","\\html@mathml{\\@imath}{ı}"),Er("\\jmath","\\html@mathml{\\@jmath}{ȷ}"),Er("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}"),Er("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}"),Er("⟦","\\llbracket"),Er("⟧","\\rrbracket"),Er("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}"),Er("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}"),Er("⦃","\\lBrace"),Er("⦄","\\rBrace"),Er("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}"),Er("⦵","\\minuso"),Er("\\darr","\\downarrow"),Er("\\dArr","\\Downarrow"),Er("\\Darr","\\Downarrow"),Er("\\lang","\\langle"),Er("\\rang","\\rangle"),Er("\\uarr","\\uparrow"),Er("\\uArr","\\Uparrow"),Er("\\Uarr","\\Uparrow"),Er("\\N","\\mathbb{N}"),Er("\\R","\\mathbb{R}"),Er("\\Z","\\mathbb{Z}"),Er("\\alef","\\aleph"),Er("\\alefsym","\\aleph"),Er("\\Alpha","\\mathrm{A}"),Er("\\Beta","\\mathrm{B}"),Er("\\bull","\\bullet"),Er("\\Chi","\\mathrm{X}"),Er("\\clubs","\\clubsuit"),Er("\\cnums","\\mathbb{C}"),Er("\\Complex","\\mathbb{C}"),Er("\\Dagger","\\ddagger"),Er("\\diamonds","\\diamondsuit"),Er("\\empty","\\emptyset"),Er("\\Epsilon","\\mathrm{E}"),Er("\\Eta","\\mathrm{H}"),Er("\\exist","\\exists"),Er("\\harr","\\leftrightarrow"),Er("\\hArr","\\Leftrightarrow"),Er("\\Harr","\\Leftrightarrow"),Er("\\hearts","\\heartsuit"),Er("\\image","\\Im"),Er("\\infin","\\infty"),Er("\\Iota","\\mathrm{I}"),Er("\\isin","\\in"),Er("\\Kappa","\\mathrm{K}"),Er("\\larr","\\leftarrow"),Er("\\lArr","\\Leftarrow"),Er("\\Larr","\\Leftarrow"),Er("\\lrarr","\\leftrightarrow"),Er("\\lrArr","\\Leftrightarrow"),Er("\\Lrarr","\\Leftrightarrow"),Er("\\Mu","\\mathrm{M}"),Er("\\natnums","\\mathbb{N}"),Er("\\Nu","\\mathrm{N}"),Er("\\Omicron","\\mathrm{O}"),Er("\\plusmn","\\pm"),Er("\\rarr","\\rightarrow"),Er("\\rArr","\\Rightarrow"),Er("\\Rarr","\\Rightarrow"),Er("\\real","\\Re"),Er("\\reals","\\mathbb{R}"),Er("\\Reals","\\mathbb{R}"),Er("\\Rho","\\mathrm{P}"),Er("\\sdot","\\cdot"),Er("\\sect","\\S"),Er("\\spades","\\spadesuit"),Er("\\sub","\\subset"),Er("\\sube","\\subseteq"),Er("\\supe","\\supseteq"),Er("\\Tau","\\mathrm{T}"),Er("\\thetasym","\\vartheta"),Er("\\weierp","\\wp"),Er("\\Zeta","\\mathrm{Z}"),Er("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),Er("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),Er("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),Er("\\bra","\\mathinner{\\langle{#1}|}"),Er("\\ket","\\mathinner{|{#1}\\rangle}"),Er("\\braket","\\mathinner{\\langle{#1}\\rangle}"),Er("\\Bra","\\left\\langle#1\\right|"),Er("\\Ket","\\left|#1\\right\\rangle");var La=e=>t=>{var r=t.consumeArg().tokens,a=t.consumeArg().tokens,n=t.consumeArg().tokens,i=t.consumeArg().tokens,o=t.macros.get("|"),s=t.macros.get("\\|");t.macros.beginGroup();var l=t=>r=>{e&&(r.macros.set("|",o),n.length&&r.macros.set("\\|",s));var i=t;!t&&n.length&&("|"===r.future().text&&(r.popToken(),i=!0));return{tokens:i?n:a,numArgs:0}};t.macros.set("|",l(!1)),n.length&&t.macros.set("\\|",l(!0));var h=t.consumeArg().tokens,m=t.expandTokens([...i,...h,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};Er("\\bra@ket",La(!1)),Er("\\bra@set",La(!0)),Er("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),Er("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),Er("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),Er("\\angln","{\\angl n}"),Er("\\blue","\\textcolor{##6495ed}{#1}"),Er("\\orange","\\textcolor{##ffa500}{#1}"),Er("\\pink","\\textcolor{##ff00af}{#1}"),Er("\\red","\\textcolor{##df0030}{#1}"),Er("\\green","\\textcolor{##28ae7b}{#1}"),Er("\\gray","\\textcolor{gray}{#1}"),Er("\\purple","\\textcolor{##9d38bd}{#1}"),Er("\\blueA","\\textcolor{##ccfaff}{#1}"),Er("\\blueB","\\textcolor{##80f6ff}{#1}"),Er("\\blueC","\\textcolor{##63d9ea}{#1}"),Er("\\blueD","\\textcolor{##11accd}{#1}"),Er("\\blueE","\\textcolor{##0c7f99}{#1}"),Er("\\tealA","\\textcolor{##94fff5}{#1}"),Er("\\tealB","\\textcolor{##26edd5}{#1}"),Er("\\tealC","\\textcolor{##01d1c1}{#1}"),Er("\\tealD","\\textcolor{##01a995}{#1}"),Er("\\tealE","\\textcolor{##208170}{#1}"),Er("\\greenA","\\textcolor{##b6ffb0}{#1}"),Er("\\greenB","\\textcolor{##8af281}{#1}"),Er("\\greenC","\\textcolor{##74cf70}{#1}"),Er("\\greenD","\\textcolor{##1fab54}{#1}"),Er("\\greenE","\\textcolor{##0d923f}{#1}"),Er("\\goldA","\\textcolor{##ffd0a9}{#1}"),Er("\\goldB","\\textcolor{##ffbb71}{#1}"),Er("\\goldC","\\textcolor{##ff9c39}{#1}"),Er("\\goldD","\\textcolor{##e07d10}{#1}"),Er("\\goldE","\\textcolor{##a75a05}{#1}"),Er("\\redA","\\textcolor{##fca9a9}{#1}"),Er("\\redB","\\textcolor{##ff8482}{#1}"),Er("\\redC","\\textcolor{##f9685d}{#1}"),Er("\\redD","\\textcolor{##e84d39}{#1}"),Er("\\redE","\\textcolor{##bc2612}{#1}"),Er("\\maroonA","\\textcolor{##ffbde0}{#1}"),Er("\\maroonB","\\textcolor{##ff92c6}{#1}"),Er("\\maroonC","\\textcolor{##ed5fa6}{#1}"),Er("\\maroonD","\\textcolor{##ca337c}{#1}"),Er("\\maroonE","\\textcolor{##9e034e}{#1}"),Er("\\purpleA","\\textcolor{##ddd7ff}{#1}"),Er("\\purpleB","\\textcolor{##c6b9fc}{#1}"),Er("\\purpleC","\\textcolor{##aa87ff}{#1}"),Er("\\purpleD","\\textcolor{##7854ab}{#1}"),Er("\\purpleE","\\textcolor{##543b78}{#1}"),Er("\\mintA","\\textcolor{##f5f9e8}{#1}"),Er("\\mintB","\\textcolor{##edf2df}{#1}"),Er("\\mintC","\\textcolor{##e0e5cc}{#1}"),Er("\\grayA","\\textcolor{##f6f7f7}{#1}"),Er("\\grayB","\\textcolor{##f0f1f2}{#1}"),Er("\\grayC","\\textcolor{##e3e5e6}{#1}"),Er("\\grayD","\\textcolor{##d6d8da}{#1}"),Er("\\grayE","\\textcolor{##babec2}{#1}"),Er("\\grayF","\\textcolor{##888d93}{#1}"),Er("\\grayG","\\textcolor{##626569}{#1}"),Er("\\grayH","\\textcolor{##3b3e40}{#1}"),Er("\\grayI","\\textcolor{##21242c}{#1}"),Er("\\kaBlue","\\textcolor{##314453}{#1}"),Er("\\kaGreen","\\textcolor{##71B307}{#1}");var Da={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class Va{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new Na(qa,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new Ca(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var r,a,n;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;r=this.popToken(),({tokens:n,end:a}=this.consumeArg(["]"]))}else({tokens:n,start:r,end:a}=this.consumeArg());return this.pushToken(new t("EOF",a.loc)),this.pushTokens(n),r.range(a,"")}consumeSpaces(){for(;;){if(" "!==this.future().text)break;this.stack.pop()}}consumeArg(e){var t=[],a=e&&e.length>0;a||this.consumeSpaces();var n,i=this.future(),o=0,s=0;do{if(n=this.popToken(),t.push(n),"{"===n.text)++o;else if("}"===n.text){if(-1===--o)throw new r("Extra }",n)}else if("EOF"===n.text)throw new r("Unexpected end of input in a macro argument, expected '"+(e&&a?e[s]:"}")+"'",n);if(e&&a)if((0===o||1===o&&"{"===e[s])&&n.text===e[s]){if(++s===e.length){t.splice(-s,s);break}}else s=0}while(0!==o||a);return"{"===i.text&&"}"===t[t.length-1].text&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:i,end:n}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new r("The length of delimiters doesn't match the number of args!");for(var a=t[0],n=0;n<a.length;n++){var i=this.popToken();if(a[n]!==i.text)throw new r("Use of the macro doesn't match its definition",i)}}for(var o=[],s=0;s<e;s++)o.push(this.consumeArg(t&&t[s+1]).tokens);return o}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new r("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),a=t.text,n=t.noexpand?null:this._getExpansion(a);if(null==n||e&&n.unexpandable){if(e&&null==n&&"\\"===a[0]&&!this.isDefined(a))throw new r("Undefined control sequence: "+a);return this.pushToken(t),!1}this.countExpansion(1);var i=n.tokens,o=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs)for(var s=(i=i.slice()).length-1;s>=0;--s){var l=i[s];if("#"===l.text){if(0===s)throw new r("Incomplete placeholder at end of macro body",l);if("#"===(l=i[--s]).text)i.splice(s+1,1);else{if(!/^[1-9]$/.test(l.text))throw new r("Not a valid argument number",l);i.splice(s,2,...o[+l.text-1])}}}return this.pushTokens(i),i.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(!1===this.expandOnce()){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new t(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(!1===this.expandOnce(!0)){var a=this.stack.pop();a.treatAsRelax&&(a.noexpand=!1,a.treatAsRelax=!1),t.push(a)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t?t.map((e=>e.text)).join(""):t}_getExpansion(e){var t=this.macros.get(e);if(null==t)return t;if(1===e.length){var r=this.lexer.catcodes[e];if(null!=r&&13!==r)return}var a="function"==typeof t?t(this):t;if("string"==typeof a){var n=0;if(-1!==a.indexOf("#"))for(var i=a.replace(/##/g,"");-1!==i.indexOf("#"+(n+1));)++n;for(var o=new Ca(a,this.settings),s=[],l=o.lex();"EOF"!==l.text;)s.push(l),l=o.lex();return s.reverse(),{tokens:s,numArgs:n}}return a}isDefined(e){return this.macros.has(e)||Sa.hasOwnProperty(e)||te.math.hasOwnProperty(e)||te.text.hasOwnProperty(e)||Da.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:Sa.hasOwnProperty(e)&&!Sa[e].primitive}}var Pa=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Fa=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g","ʰ":"h","ⁱ":"i","ʲ":"j","ᵏ":"k","ˡ":"l","ᵐ":"m","ⁿ":"n","ᵒ":"o","ᵖ":"p","ʳ":"r","ˢ":"s","ᵗ":"t","ᵘ":"u","ᵛ":"v","ʷ":"w","ˣ":"x","ʸ":"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),Ga={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},Ua={"á":"á","à":"à","ä":"ä","ǟ":"ǟ","ã":"ã","ā":"ā","ă":"ă","ắ":"ắ","ằ":"ằ","ẵ":"ẵ","ǎ":"ǎ","â":"â","ấ":"ấ","ầ":"ầ","ẫ":"ẫ","ȧ":"ȧ","ǡ":"ǡ","å":"å","ǻ":"ǻ","ḃ":"ḃ","ć":"ć","ḉ":"ḉ","č":"č","ĉ":"ĉ","ċ":"ċ","ç":"ç","ď":"ď","ḋ":"ḋ","ḑ":"ḑ","é":"é","è":"è","ë":"ë","ẽ":"ẽ","ē":"ē","ḗ":"ḗ","ḕ":"ḕ","ĕ":"ĕ","ḝ":"ḝ","ě":"ě","ê":"ê","ế":"ế","ề":"ề","ễ":"ễ","ė":"ė","ȩ":"ȩ","ḟ":"ḟ","ǵ":"ǵ","ḡ":"ḡ","ğ":"ğ","ǧ":"ǧ","ĝ":"ĝ","ġ":"ġ","ģ":"ģ","ḧ":"ḧ","ȟ":"ȟ","ĥ":"ĥ","ḣ":"ḣ","ḩ":"ḩ","í":"í","ì":"ì","ï":"ï","ḯ":"ḯ","ĩ":"ĩ","ī":"ī","ĭ":"ĭ","ǐ":"ǐ","î":"î","ǰ":"ǰ","ĵ":"ĵ","ḱ":"ḱ","ǩ":"ǩ","ķ":"ķ","ĺ":"ĺ","ľ":"ľ","ļ":"ļ","ḿ":"ḿ","ṁ":"ṁ","ń":"ń","ǹ":"ǹ","ñ":"ñ","ň":"ň","ṅ":"ṅ","ņ":"ņ","ó":"ó","ò":"ò","ö":"ö","ȫ":"ȫ","õ":"õ","ṍ":"ṍ","ṏ":"ṏ","ȭ":"ȭ","ō":"ō","ṓ":"ṓ","ṑ":"ṑ","ŏ":"ŏ","ǒ":"ǒ","ô":"ô","ố":"ố","ồ":"ồ","ỗ":"ỗ","ȯ":"ȯ","ȱ":"ȱ","ő":"ő","ṕ":"ṕ","ṗ":"ṗ","ŕ":"ŕ","ř":"ř","ṙ":"ṙ","ŗ":"ŗ","ś":"ś","ṥ":"ṥ","š":"š","ṧ":"ṧ","ŝ":"ŝ","ṡ":"ṡ","ş":"ş","ẗ":"ẗ","ť":"ť","ṫ":"ṫ","ţ":"ţ","ú":"ú","ù":"ù","ü":"ü","ǘ":"ǘ","ǜ":"ǜ","ǖ":"ǖ","ǚ":"ǚ","ũ":"ũ","ṹ":"ṹ","ū":"ū","ṻ":"ṻ","ŭ":"ŭ","ǔ":"ǔ","û":"û","ů":"ů","ű":"ű","ṽ":"ṽ","ẃ":"ẃ","ẁ":"ẁ","ẅ":"ẅ","ŵ":"ŵ","ẇ":"ẇ","ẘ":"ẘ","ẍ":"ẍ","ẋ":"ẋ","ý":"ý","ỳ":"ỳ","ÿ":"ÿ","ỹ":"ỹ","ȳ":"ȳ","ŷ":"ŷ","ẏ":"ẏ","ẙ":"ẙ","ź":"ź","ž":"ž","ẑ":"ẑ","ż":"ż","Á":"Á","À":"À","Ä":"Ä","Ǟ":"Ǟ","Ã":"Ã","Ā":"Ā","Ă":"Ă","Ắ":"Ắ","Ằ":"Ằ","Ẵ":"Ẵ","Ǎ":"Ǎ","Â":"Â","Ấ":"Ấ","Ầ":"Ầ","Ẫ":"Ẫ","Ȧ":"Ȧ","Ǡ":"Ǡ","Å":"Å","Ǻ":"Ǻ","Ḃ":"Ḃ","Ć":"Ć","Ḉ":"Ḉ","Č":"Č","Ĉ":"Ĉ","Ċ":"Ċ","Ç":"Ç","Ď":"Ď","Ḋ":"Ḋ","Ḑ":"Ḑ","É":"É","È":"È","Ë":"Ë","Ẽ":"Ẽ","Ē":"Ē","Ḗ":"Ḗ","Ḕ":"Ḕ","Ĕ":"Ĕ","Ḝ":"Ḝ","Ě":"Ě","Ê":"Ê","Ế":"Ế","Ề":"Ề","Ễ":"Ễ","Ė":"Ė","Ȩ":"Ȩ","Ḟ":"Ḟ","Ǵ":"Ǵ","Ḡ":"Ḡ","Ğ":"Ğ","Ǧ":"Ǧ","Ĝ":"Ĝ","Ġ":"Ġ","Ģ":"Ģ","Ḧ":"Ḧ","Ȟ":"Ȟ","Ĥ":"Ĥ","Ḣ":"Ḣ","Ḩ":"Ḩ","Í":"Í","Ì":"Ì","Ï":"Ï","Ḯ":"Ḯ","Ĩ":"Ĩ","Ī":"Ī","Ĭ":"Ĭ","Ǐ":"Ǐ","Î":"Î","İ":"İ","Ĵ":"Ĵ","Ḱ":"Ḱ","Ǩ":"Ǩ","Ķ":"Ķ","Ĺ":"Ĺ","Ľ":"Ľ","Ļ":"Ļ","Ḿ":"Ḿ","Ṁ":"Ṁ","Ń":"Ń","Ǹ":"Ǹ","Ñ":"Ñ","Ň":"Ň","Ṅ":"Ṅ","Ņ":"Ņ","Ó":"Ó","Ò":"Ò","Ö":"Ö","Ȫ":"Ȫ","Õ":"Õ","Ṍ":"Ṍ","Ṏ":"Ṏ","Ȭ":"Ȭ","Ō":"Ō","Ṓ":"Ṓ","Ṑ":"Ṑ","Ŏ":"Ŏ","Ǒ":"Ǒ","Ô":"Ô","Ố":"Ố","Ồ":"Ồ","Ỗ":"Ỗ","Ȯ":"Ȯ","Ȱ":"Ȱ","Ő":"Ő","Ṕ":"Ṕ","Ṗ":"Ṗ","Ŕ":"Ŕ","Ř":"Ř","Ṙ":"Ṙ","Ŗ":"Ŗ","Ś":"Ś","Ṥ":"Ṥ","Š":"Š","Ṧ":"Ṧ","Ŝ":"Ŝ","Ṡ":"Ṡ","Ş":"Ş","Ť":"Ť","Ṫ":"Ṫ","Ţ":"Ţ","Ú":"Ú","Ù":"Ù","Ü":"Ü","Ǘ":"Ǘ","Ǜ":"Ǜ","Ǖ":"Ǖ","Ǚ":"Ǚ","Ũ":"Ũ","Ṹ":"Ṹ","Ū":"Ū","Ṻ":"Ṻ","Ŭ":"Ŭ","Ǔ":"Ǔ","Û":"Û","Ů":"Ů","Ű":"Ű","Ṽ":"Ṽ","Ẃ":"Ẃ","Ẁ":"Ẁ","Ẅ":"Ẅ","Ŵ":"Ŵ","Ẇ":"Ẇ","Ẍ":"Ẍ","Ẋ":"Ẋ","Ý":"Ý","Ỳ":"Ỳ","Ÿ":"Ÿ","Ỹ":"Ỹ","Ȳ":"Ȳ","Ŷ":"Ŷ","Ẏ":"Ẏ","Ź":"Ź","Ž":"Ž","Ẑ":"Ẑ","Ż":"Ż","ά":"ά","ὰ":"ὰ","ᾱ":"ᾱ","ᾰ":"ᾰ","έ":"έ","ὲ":"ὲ","ή":"ή","ὴ":"ὴ","ί":"ί","ὶ":"ὶ","ϊ":"ϊ","ΐ":"ΐ","ῒ":"ῒ","ῑ":"ῑ","ῐ":"ῐ","ό":"ό","ὸ":"ὸ","ύ":"ύ","ὺ":"ὺ","ϋ":"ϋ","ΰ":"ΰ","ῢ":"ῢ","ῡ":"ῡ","ῠ":"ῠ","ώ":"ώ","ὼ":"ὼ","Ύ":"Ύ","Ὺ":"Ὺ","Ϋ":"Ϋ","Ῡ":"Ῡ","Ῠ":"Ῠ","Ώ":"Ώ","Ὼ":"Ὼ"};class Ya{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new Va(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new r("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var r=this.nextToken;this.consume(),this.gullet.pushToken(new t("}")),this.gullet.pushTokens(e);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=r,a}parseExpression(e,t){for(var r=[];;){"math"===this.mode&&this.consumeSpaces();var a=this.fetch();if(-1!==Ya.endOfExpression.indexOf(a.text))break;if(t&&a.text===t)break;if(e&&Sa[a.text]&&Sa[a.text].infix)break;var n=this.parseAtom(t);if(!n)break;"internal"!==n.type&&r.push(n)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t,a=-1,n=0;n<e.length;n++)if("infix"===e[n].type){if(-1!==a)throw new r("only one infix operator per group",e[n].token);a=n,t=e[n].replaceWith}if(-1!==a&&t){var i,o,s=e.slice(0,a),l=e.slice(a+1);return i=1===s.length&&"ordgroup"===s[0].type?s[0]:{type:"ordgroup",mode:this.mode,body:s},o=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},["\\\\abovefrac"===t?this.callFunction(t,[i,e[a],o],[]):this.callFunction(t,[i,o],[])]}return e}handleSupSubscript(e){var t=this.fetch(),a=t.text;this.consume(),this.consumeSpaces();var n=this.parseGroup(e);if(!n)throw new r("Expected group after '"+a+"'",t);return n}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var a={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[a]}}parseAtom(e){var a,n,i=this.parseGroup("atom",e);if("text"===this.mode)return i;for(;;){this.consumeSpaces();var o=this.fetch();if("\\limits"===o.text||"\\nolimits"===o.text){if(i&&"op"===i.type){var s="\\limits"===o.text;i.limits=s,i.alwaysHandleSupSub=!0}else{if(!i||"operatorname"!==i.type)throw new r("Limit controls must follow a math operator",o);i.alwaysHandleSupSub&&(i.limits="\\limits"===o.text)}this.consume()}else if("^"===o.text){if(a)throw new r("Double superscript",o);a=this.handleSupSubscript("superscript")}else if("_"===o.text){if(n)throw new r("Double subscript",o);n=this.handleSupSubscript("subscript")}else if("'"===o.text){if(a)throw new r("Double superscript",o);var l={type:"textord",mode:this.mode,text:"\\prime"},h=[l];for(this.consume();"'"===this.fetch().text;)h.push(l),this.consume();"^"===this.fetch().text&&h.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:h}}else{if(!Fa[o.text])break;var m=Pa.test(o.text),c=[];for(c.push(new t(Fa[o.text])),this.consume();;){var p=this.fetch().text;if(!Fa[p])break;if(Pa.test(p)!==m)break;c.unshift(new t(Fa[p])),this.consume()}var u=this.subparse(c);m?n={type:"ordgroup",mode:"math",body:u}:a={type:"ordgroup",mode:"math",body:u}}}return a||n?{type:"supsub",mode:this.mode,base:i,sup:a,sub:n}:i}parseFunction(e,t){var a=this.fetch(),n=a.text,i=Sa[n];if(!i)return null;if(this.consume(),t&&"atom"!==t&&!i.allowedInArgument)throw new r("Got function '"+n+"' with no arguments"+(t?" as "+t:""),a);if("text"===this.mode&&!i.allowedInText)throw new r("Can't use function '"+n+"' in text mode",a);if("math"===this.mode&&!1===i.allowedInMath)throw new r("Can't use function '"+n+"' in math mode",a);var{args:o,optArgs:s}=this.parseArguments(n,i);return this.callFunction(n,o,s,a,e)}callFunction(e,t,a,n,i){var o={funcName:e,parser:this,token:n,breakOnTokenText:i},s=Sa[e];if(s&&s.handler)return s.handler(o,t,a);throw new r("No function handler for "+e)}parseArguments(e,t){var a=t.numArgs+t.numOptionalArgs;if(0===a)return{args:[],optArgs:[]};for(var n=[],i=[],o=0;o<a;o++){var s=t.argTypes&&t.argTypes[o],l=o<t.numOptionalArgs;(t.primitive&&null==s||"sqrt"===t.type&&1===o&&null==i[0])&&(s="primitive");var h=this.parseGroupOfType("argument to '"+e+"'",s,l);if(l)i.push(h);else{if(null==h)throw new r("Null argument, please report this as a bug");n.push(h)}}return{args:n,optArgs:i}}parseGroupOfType(e,t,a){switch(t){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,t);case"hbox":var n=this.parseArgumentGroup(a,"text");return null!=n?{type:"styling",mode:n.mode,body:[n],style:"text"}:null;case"raw":var i=this.parseStringGroup("raw",a);return null!=i?{type:"raw",mode:"text",string:i.text}:null;case"primitive":if(a)throw new r("A primitive argument cannot be optional");var o=this.parseGroup(e);if(null==o)throw new r("Expected group as "+e,this.fetch());return o;case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new r("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){var r=this.gullet.scanArgument(t);if(null==r)return null;for(var a,n="";"EOF"!==(a=this.fetch()).text;)n+=a.text,this.consume();return this.consume(),r.text=n,r}parseRegexGroup(e,t){for(var a,n=this.fetch(),i=n,o="";"EOF"!==(a=this.fetch()).text&&e.test(o+a.text);)o+=(i=a).text,this.consume();if(""===o)throw new r("Invalid "+t+": '"+n.text+"'",n);return n.range(i,o)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(null==t)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!a)throw new r("Invalid color: '"+t.text+"'",t);var n=a[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){var t,a=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",a=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new r("Invalid size: '"+t.text+"'",t);var i={number:+(n[1]+n[2]),unit:n[3]};if(!L(i))throw new r("Invalid unit: '"+i.unit+"'",t);return{type:"size",mode:this.mode,value:i,isBlank:a}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(null==r)return null;var a=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var n=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var i={type:"ordgroup",mode:this.mode,loc:r.loc,body:n};return t&&this.switchMode(a),i}parseGroup(t,a){var n,i=this.fetch(),o=i.text;if("{"===o||"\\begingroup"===o){this.consume();var s="{"===o?"}":"\\endgroup";this.gullet.beginGroup();var l=this.parseExpression(!1,s),h=this.fetch();this.expect(s),this.gullet.endGroup(),n={type:"ordgroup",mode:this.mode,loc:e.range(i,h),body:l,semisimple:"\\begingroup"===o||void 0}}else if(null==(n=this.parseFunction(a,t)||this.parseSymbol())&&"\\"===o[0]&&!Da.hasOwnProperty(o)){if(this.settings.throwOnError)throw new r("Undefined control sequence: "+o,i);n=this.formatUnsupportedCmd(o),this.consume()}return n}formLigatures(t){for(var r=t.length-1,a=0;a<r;++a){var n=t[a],i=n.text;"-"===i&&"-"===t[a+1].text&&(a+1<r&&"-"===t[a+2].text?(t.splice(a,3,{type:"textord",mode:"text",loc:e.range(n,t[a+2]),text:"---"}),r-=2):(t.splice(a,2,{type:"textord",mode:"text",loc:e.range(n,t[a+1]),text:"--"}),r-=1)),"'"!==i&&"`"!==i||t[a+1].text!==i||(t.splice(a,2,{type:"textord",mode:"text",loc:e.range(n,t[a+1]),text:i+i}),r-=1)}}parseSymbol(){var t=this.fetch(),a=t.text;if(/^\\verb[^a-zA-Z]/.test(a)){this.consume();var n=a.slice(5),i="*"===n.charAt(0);if(i&&(n=n.slice(1)),n.length<2||n.charAt(0)!==n.slice(-1))throw new r("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:n=n.slice(1,-1),star:i}}Ua.hasOwnProperty(a[0])&&!te[this.mode][a[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+a[0]+'" used in math mode',t),a=Ua[a[0]]+a.slice(1));var o,s=Ta.exec(a);if(s&&("i"===(a=a.substring(0,s.index))?a="ı":"j"===a&&(a="ȷ")),te[this.mode][a]){this.settings.strict&&"math"===this.mode&&Re.indexOf(a)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+a[0]+'" used in math mode',t);var l,h=te[this.mode][a].group,m=e.range(t);if(Q.hasOwnProperty(h)){var c=h;l={type:"atom",mode:this.mode,family:c,loc:m,text:a}}else l={type:h,mode:this.mode,loc:m,text:a};o=l}else{if(!(a.charCodeAt(0)>=128))return null;this.settings.strict&&(k(a.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+a[0]+'" used in math mode',t):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+a[0]+'" ('+a.charCodeAt(0)+")",t)),o={type:"textord",mode:"text",loc:e.range(t),text:a}}if(this.consume(),s)for(var p=0;p<s[0].length;p++){var u=s[0][p];if(!Ga[u])throw new r("Unknown accent ' "+u+"'",t);var d=Ga[u][this.mode]||Ga[u].text;if(!d)throw new r("Accent "+u+" unsupported in "+this.mode+" mode",t);o={type:"accent",mode:this.mode,loc:e.range(t),label:d,isStretchy:!1,isShifty:!0,base:o}}return o}}Ya.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var Wa=function(e,a){if(!("string"==typeof e||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var n=new Ya(e,a);delete n.gullet.macros.current["\\df@tag"];var i=n.parse();if(delete n.gullet.macros.current["\\current@color"],delete n.gullet.macros.current["\\color"],n.gullet.macros.get("\\df@tag")){if(!a.displayMode)throw new r("\\tag works only in display equations");i=[{type:"tag",mode:"text",body:i,tag:n.subparse([new t("\\df@tag")])}]}return i},Xa=function(e,t,r){t.textContent="";var a=ja(e,r).toNode();t.appendChild(a)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&(Xa=function(){throw new r("KaTeX doesn't work in quirks mode.")});var _a=function(e,t,a){if(a.throwOnError||!(e instanceof r))throw e;var n=je.makeSpan(["katex-error"],[new j(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+a.errorColor),n},ja=function(e,t){var r=new m(t);try{return function(e,t,r){var a,n=It(r);if("mathml"===r.output)return qt(e,t,n,r.displayMode,!0);if("html"===r.output){var i=xt(e,n);a=je.makeSpan(["katex"],[i])}else{var o=qt(e,t,n,r.displayMode,!1),s=xt(e,n);a=je.makeSpan(["katex"],[o,s])}return Rt(a,r)}(Wa(e,r),e,r)}catch(a){return _a(a,e,r)}},$a={version:"0.16.10",render:Xa,renderToString:function(e,t){return ja(e,t).toMarkup()},ParseError:r,SETTINGS_SCHEMA:l,__parse:function(e,t){var r=new m(t);return Wa(e,r)},__renderToDomTree:ja,__renderToHTMLTree:function(e,t){var r=new m(t);try{return function(e,t,r){var a=xt(e,It(r)),n=je.makeSpan(["katex"],[a]);return Rt(n,r)}(Wa(e,r),0,r)}catch(a){return _a(a,e,r)}},__setFontMetrics:function(e,t){A[e]=t},__defineSymbol:re,__defineFunction:at,__defineMacro:Er,__domTree:{Span:Y,Anchor:W,SymbolNode:j,SvgNode:$,PathNode:Z,LineNode:K}};try{window.katex=$a}catch(Za){}
