import{c as t,g as e}from"./_commonjsHelpers-MdiGH4nz.js";var n={exports:{}};
/*! shepherd.js 11.2.0 */const o=e(n.exports=function(){var t=function(t){return e(t)&&!n(t)};function e(t){return!!t&&"object"==typeof t}function n(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||i(t)}var o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(t){return t.$$typeof===o}function s(t){return Array.isArray(t)?[]:{}}function r(t,e){return!1!==e.clone&&e.isMergeableObject(t)?d(s(t),t,e):t}function l(t,e,n){return t.concat(e).map((function(t){return r(t,n)}))}function c(t,e){if(!e.customMerge)return d;var n=e.customMerge(t);return"function"==typeof n?n:d}function a(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return Object.propertyIsEnumerable.call(t,e)})):[]}function u(t){return Object.keys(t).concat(a(t))}function f(t,e){try{return e in t}catch(n){return!1}}function h(t,e){return f(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))}function p(t,e,n){var o={};return n.isMergeableObject(t)&&u(t).forEach((function(e){o[e]=r(t[e],n)})),u(e).forEach((function(i){h(t,i)||(f(t,i)&&n.isMergeableObject(e[i])?o[i]=c(i,n)(t[i],e[i],n):o[i]=r(e[i],n))})),o}function d(e,n,o){(o=o||{}).arrayMerge=o.arrayMerge||l,o.isMergeableObject=o.isMergeableObject||t,o.cloneUnlessOtherwiseSpecified=r;var i=Array.isArray(n);return i===Array.isArray(e)?i?o.arrayMerge(e,n,o):p(e,n,o):r(n,o)}d.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,n){return d(t,n,e)}),{})};var m=d;function g(t){return t instanceof Element}function y(t){return t instanceof HTMLElement}function b(t){return"function"==typeof t}function x(t){return"string"==typeof t}function $(t){return void 0===t}class w{on(t,e,n,o=!1){return $(this.bindings)&&(this.bindings={}),$(this.bindings[t])&&(this.bindings[t]=[]),this.bindings[t].push({handler:e,ctx:n,once:o}),this}once(t,e,n){return this.on(t,e,n,!0)}off(t,e){return $(this.bindings)||$(this.bindings[t])||($(e)?delete this.bindings[t]:this.bindings[t].forEach(((n,o)=>{n.handler===e&&this.bindings[t].splice(o,1)}))),this}trigger(t,...e){return!$(this.bindings)&&this.bindings[t]&&this.bindings[t].forEach(((n,o)=>{const{ctx:i,handler:s,once:r}=n,l=i||this;s.apply(l,e),r&&this.bindings[t].splice(o,1)})),this}}function v(t){const e=Object.getOwnPropertyNames(t.constructor.prototype);for(let n=0;n<e.length;n++){const o=e[n],i=t[o];"constructor"!==o&&"function"==typeof i&&(t[o]=i.bind(t))}return t}function E(t,e){return n=>{if(e.isOpen()){const o=e.el&&n.currentTarget===e.el;(!$(t)&&n.currentTarget.matches(t)||o)&&e.tour.next()}}}function O(t){const{event:e,selector:n}=t.options.advanceOn||{};if(e){const i=E(n,t);let s;try{s=document.querySelector(n)}catch(o){}if(!$(n)&&!s)return;s?(s.addEventListener(e,i),t.on("destroy",(()=>s.removeEventListener(e,i)))):(document.body.addEventListener(e,i,!0),t.on("destroy",(()=>document.body.removeEventListener(e,i,!0))))}}function T(t){return x(t)&&""!==t?"-"!==t.charAt(t.length-1)?`${t}-`:t:""}function S(t){const e=t.options.attachTo||{},n=Object.assign({},e);if(b(n.element)&&(n.element=n.element.call(t)),x(n.element)){try{n.element=document.querySelector(n.element)}catch(o){}n.element}return n}function _(t){return null==t||!t.element||!t.on}function A(){let t=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{const n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?n:3&n|8).toString(16)}))}function I(){return I=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},I.apply(this,arguments)}function L(t,e){if(null==t)return{};var n,o,i={},s=Object.keys(t);for(o=0;o<s.length;o++)n=s[o],e.indexOf(n)>=0||(i[n]=t[n]);return i}const P=Math.min,C=Math.max,k=Math.round,R=Math.floor,M=t=>({x:t,y:t}),j={left:"right",right:"left",bottom:"top",top:"bottom"},F={start:"end",end:"start"};function D(t,e,n){return C(t,P(e,n))}function H(t,e){return"function"==typeof t?t(e):t}function B(t){return t.split("-")[0]}function N(t){return t.split("-")[1]}function V(t){return"x"===t?"y":"x"}function W(t){return"y"===t?"height":"width"}function q(t){return["top","bottom"].includes(B(t))?"y":"x"}function z(t){return V(q(t))}function U(t,e,n){void 0===n&&(n=!1);const o=N(t),i=z(t),s=W(i);let r="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=G(r)),[r,G(r)]}function Y(t){const e=G(t);return[X(t),e,X(e)]}function X(t){return t.replace(/start|end/g,(t=>F[t]))}function Z(t,e,n){const o=["left","right"],i=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?s:r;default:return[]}}function K(t,e,n,o){const i=N(t);let s=Z(B(t),"start"===n,o);return i&&(s=s.map((t=>t+"-"+i)),e&&(s=s.concat(s.map(X)))),s}function G(t){return t.replace(/left|right|bottom|top/g,(t=>j[t]))}function J(t){return I({top:0,right:0,bottom:0,left:0},t)}function Q(t){return"number"!=typeof t?J(t):{top:t,right:t,bottom:t,left:t}}function tt(t){return I({},t,{top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height})}const et=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],nt=["mainAxis","crossAxis","limiter"];function ot(t,e,n){let{reference:o,floating:i}=t;const s=q(e),r=z(e),l=W(r),c=B(e),a="y"===s,u=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,h=o[l]/2-i[l]/2;let p;switch(c){case"top":p={x:u,y:o.y-i.height};break;case"bottom":p={x:u,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:f};break;case"left":p={x:o.x-i.width,y:f};break;default:p={x:o.x,y:o.y}}switch(N(e)){case"start":p[r]-=h*(n&&a?-1:1);break;case"end":p[r]+=h*(n&&a?-1:1)}return p}const it=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:s=[],platform:r}=n,l=s.filter(Boolean),c=await(null==r.isRTL?void 0:r.isRTL(e));let a=await r.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:f}=ot(a,o,c),h=o,p={},d=0;for(let m=0;m<l.length;m++){const{name:n,fn:s}=l[m],{x:g,y:y,data:b,reset:x}=await s({x:u,y:f,initialPlacement:o,placement:h,strategy:i,middlewareData:p,rects:a,platform:r,elements:{reference:t,floating:e}});u=null!=g?g:u,f=null!=y?y:f,p=I({},p,{[n]:I({},p[n],b)}),x&&d<=50&&(d++,"object"==typeof x&&(x.placement&&(h=x.placement),x.rects&&(a=!0===x.rects?await r.getElementRects({reference:t,floating:e,strategy:i}):x.rects),({x:u,y:f}=ot(a,h,c))),m=-1)}return{x:u,y:f,placement:h,strategy:i,middlewareData:p}};async function st(t,e){var n;void 0===e&&(e={});const{x:o,y:i,platform:s,rects:r,elements:l,strategy:c}=t,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:h=!1,padding:p=0}=H(e,t),d=Q(p),m=l[h?"floating"===f?"reference":"floating":f],g=tt(await s.getClippingRect({element:null==(n=await(null==s.isElement?void 0:s.isElement(m)))||n?m:m.contextElement||await(null==s.getDocumentElement?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:u,strategy:c})),y="floating"===f?I({},r.floating,{x:o,y:i}):r.reference,b=await(null==s.getOffsetParent?void 0:s.getOffsetParent(l.floating)),x=await(null==s.isElement?void 0:s.isElement(b))&&await(null==s.getScale?void 0:s.getScale(b))||{x:1,y:1},$=tt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({rect:y,offsetParent:b,strategy:c}):y);return{top:(g.top-$.top+d.top)/x.y,bottom:($.bottom-g.bottom+d.bottom)/x.y,left:(g.left-$.left+d.left)/x.x,right:($.right-g.right+d.right)/x.x}}const rt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:s,platform:r,elements:l}=e,{element:c,padding:a=0}=H(t,e)||{};if(null==c)return{};const u=Q(a),f={x:n,y:o},h=z(i),p=W(h),d=await r.getDimensions(c),m="y"===h,g=m?"top":"left",y=m?"bottom":"right",b=m?"clientHeight":"clientWidth",x=s.reference[p]+s.reference[h]-f[h]-s.floating[p],$=f[h]-s.reference[h],w=await(null==r.getOffsetParent?void 0:r.getOffsetParent(c));let v=w?w[b]:0;v&&await(null==r.isElement?void 0:r.isElement(w))||(v=l.floating[b]||s.floating[p]);const E=x/2-$/2,O=v/2-d[p]/2-1,T=P(u[g],O),S=P(u[y],O),_=T,A=v-d[p]-S,I=v/2-d[p]/2+E,L=D(_,I,A),C=null!=N(i)&&I!=L&&s.reference[p]/2-(I<_?T:S)-d[p]/2<0?I<_?_-I:A-I:0;return{[h]:f[h]-C,data:{[h]:L,centerOffset:I-L+C}}}}),lt=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n;const{placement:o,middlewareData:i,rects:s,initialPlacement:r,platform:l,elements:c}=e,a=H(t,e),{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:d="none",flipAlignment:m=!0}=a,g=L(a,et),y=B(o),b=B(r)===r,x=await(null==l.isRTL?void 0:l.isRTL(c.floating)),$=h||(b||!m?[G(r)]:Y(r));h||"none"===d||$.push(...K(r,m,d,x));const w=[r,...$],v=await st(e,g),E=[];let O=(null==(n=i.flip)?void 0:n.overflows)||[];if(u&&E.push(v[y]),f){const t=U(o,s,x);E.push(v[t[0]],v[t[1]])}if(O=[...O,{placement:o,overflows:E}],!E.every((t=>t<=0))){var T,S;const t=((null==(T=i.flip)?void 0:T.index)||0)+1,e=w[t];if(e)return{data:{index:t,overflows:O},reset:{placement:e}};let n=null==(S=O.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:S.placement;if(!n)switch(p){case"bestFit":{var _;const t=null==(_=O.map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:_[0];t&&(n=t);break}case"initialPlacement":n=r}if(o!==n)return{reset:{placement:n}}}return{}}}},ct=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,s=H(t,e),{mainAxis:r=!0,crossAxis:l=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}}}=s,a=L(s,nt),u={x:n,y:o},f=await st(e,a),h=q(B(i)),p=V(h);let d=u[p],m=u[h];if(r){const t="y"===p?"bottom":"right";d=D(d+f["y"===p?"top":"left"],d,d-f[t])}if(l){const t="y"===h?"bottom":"right";m=D(m+f["y"===h?"top":"left"],m,m-f[t])}const g=c.fn(I({},e,{[p]:d,[h]:m}));return I({},g,{data:{x:g.x-n,y:g.y-o}})}}},at=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:i,rects:s,middlewareData:r}=e,{offset:l=0,mainAxis:c=!0,crossAxis:a=!0}=H(t,e),u={x:n,y:o},f=q(i),h=V(f);let p=u[h],d=u[f];const m=H(l,e),g="number"==typeof m?{mainAxis:m,crossAxis:0}:I({mainAxis:0,crossAxis:0},m);if(c){const t="y"===h?"height":"width",e=s.reference[h]-s.floating[t]+g.mainAxis,n=s.reference[h]+s.reference[t]-g.mainAxis;p<e?p=e:p>n&&(p=n)}if(a){var y,b;const t="y"===h?"width":"height",e=["top","left"].includes(B(i)),n=s.reference[f]-s.floating[t]+(e&&(null==(y=r.offset)?void 0:y[f])||0)+(e?0:g.crossAxis),o=s.reference[f]+s.reference[t]+(e?0:(null==(b=r.offset)?void 0:b[f])||0)-(e?g.crossAxis:0);d<n?d=n:d>o&&(d=o)}return{[h]:p,[f]:d}}}};function ut(t){return pt(t)?(t.nodeName||"").toLowerCase():"#document"}function ft(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function ht(t){var e;return null==(e=(pt(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function pt(t){return t instanceof Node||t instanceof ft(t).Node}function dt(t){return t instanceof Element||t instanceof ft(t).Element}function mt(t){return t instanceof HTMLElement||t instanceof ft(t).HTMLElement}function gt(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof ft(t).ShadowRoot)}function yt(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=Et(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function bt(t){return["table","td","th"].includes(ut(t))}function xt(t){const e=wt(),n=Et(t);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function $t(t){let e=Tt(t);for(;mt(e)&&!vt(e);){if(xt(e))return e;e=Tt(e)}return null}function wt(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function vt(t){return["html","body","#document"].includes(ut(t))}function Et(t){return ft(t).getComputedStyle(t)}function Ot(t){return dt(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Tt(t){if("html"===ut(t))return t;const e=t.assignedSlot||t.parentNode||gt(t)&&t.host||ht(t);return gt(e)?e.host:e}function St(t){const e=Tt(t);return vt(e)?t.ownerDocument?t.ownerDocument.body:t.body:mt(e)&&yt(e)?e:St(e)}function _t(t,e){var n;void 0===e&&(e=[]);const o=St(t),i=o===(null==(n=t.ownerDocument)?void 0:n.body),s=ft(o);return i?e.concat(s,s.visualViewport||[],yt(o)?o:[]):e.concat(o,_t(o))}function At(t){const e=Et(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=mt(t),s=i?t.offsetWidth:n,r=i?t.offsetHeight:o,l=k(n)!==s||k(o)!==r;return l&&(n=s,o=r),{width:n,height:o,$:l}}function It(t){return dt(t)?t:t.contextElement}function Lt(t){const e=It(t);if(!mt(e))return M(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:s}=At(e);let r=(s?k(n.width):n.width)/o,l=(s?k(n.height):n.height)/i;return r&&Number.isFinite(r)||(r=1),l&&Number.isFinite(l)||(l=1),{x:r,y:l}}const Pt=M(0);function Ct(t){const e=ft(t);return wt()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:Pt}function kt(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==ft(t))&&e}function Rt(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const i=t.getBoundingClientRect(),s=It(t);let r=M(1);e&&(o?dt(o)&&(r=Lt(o)):r=Lt(t));const l=kt(s,n,o)?Ct(s):M(0);let c=(i.left+l.x)/r.x,a=(i.top+l.y)/r.y,u=i.width/r.x,f=i.height/r.y;if(s){const t=ft(s),e=o&&dt(o)?ft(o):o;let n=t.frameElement;for(;n&&o&&e!==t;){const t=Lt(n),e=n.getBoundingClientRect(),o=Et(n),i=e.left+(n.clientLeft+parseFloat(o.paddingLeft))*t.x,s=e.top+(n.clientTop+parseFloat(o.paddingTop))*t.y;c*=t.x,a*=t.y,u*=t.x,f*=t.y,c+=i,a+=s,n=ft(n).frameElement}}return tt({width:u,height:f,x:c,y:a})}function Mt(t){let{rect:e,offsetParent:n,strategy:o}=t;const i=mt(n),s=ht(n);if(n===s)return e;let r={scrollLeft:0,scrollTop:0},l=M(1);const c=M(0);if((i||!i&&"fixed"!==o)&&(("body"!==ut(n)||yt(s))&&(r=Ot(n)),mt(n))){const t=Rt(n);l=Lt(n),c.x=t.x+n.clientLeft,c.y=t.y+n.clientTop}return{width:e.width*l.x,height:e.height*l.y,x:e.x*l.x-r.scrollLeft*l.x+c.x,y:e.y*l.y-r.scrollTop*l.y+c.y}}function jt(t){return Array.from(t.getClientRects())}function Ft(t){return Rt(ht(t)).left+Ot(t).scrollLeft}function Dt(t){const e=ht(t),n=Ot(t),o=t.ownerDocument.body,i=C(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),s=C(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let r=-n.scrollLeft+Ft(t);const l=-n.scrollTop;return"rtl"===Et(o).direction&&(r+=C(e.clientWidth,o.clientWidth)-i),{width:i,height:s,x:r,y:l}}function Ht(t,e){const n=ft(t),o=ht(t),i=n.visualViewport;let s=o.clientWidth,r=o.clientHeight,l=0,c=0;if(i){s=i.width,r=i.height;const t=wt();(!t||t&&"fixed"===e)&&(l=i.offsetLeft,c=i.offsetTop)}return{width:s,height:r,x:l,y:c}}function Bt(t,e){const n=Rt(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,s=mt(t)?Lt(t):M(1);return{width:t.clientWidth*s.x,height:t.clientHeight*s.y,x:i*s.x,y:o*s.y}}function Nt(t,e,n){let o;if("viewport"===e)o=Ht(t,n);else if("document"===e)o=Dt(ht(t));else if(dt(e))o=Bt(e,n);else{const n=Ct(t);o=I({},e,{x:e.x-n.x,y:e.y-n.y})}return tt(o)}function Vt(t,e){const n=Tt(t);return!(n===e||!dt(n)||vt(n))&&("fixed"===Et(n).position||Vt(n,e))}function Wt(t,e){const n=e.get(t);if(n)return n;let o=_t(t).filter((t=>dt(t)&&"body"!==ut(t))),i=null;const s="fixed"===Et(t).position;let r=s?Tt(t):t;for(;dt(r)&&!vt(r);){const e=Et(r),n=xt(r);n||"fixed"!==e.position||(i=null),(s?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||yt(r)&&!n&&Vt(t,r))?o=o.filter((t=>t!==r)):i=e,r=Tt(r)}return e.set(t,o),o}function qt(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const s=[..."clippingAncestors"===n?Wt(e,this._c):[].concat(n),o],r=s[0],l=s.reduce(((t,n)=>{const o=Nt(e,n,i);return t.top=C(o.top,t.top),t.right=P(o.right,t.right),t.bottom=P(o.bottom,t.bottom),t.left=C(o.left,t.left),t}),Nt(e,r,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function zt(t){return At(t)}function Ut(t,e,n){const o=mt(e),i=ht(e),s="fixed"===n,r=Rt(t,!0,s,e);let l={scrollLeft:0,scrollTop:0};const c=M(0);if(o||!o&&!s)if(("body"!==ut(e)||yt(i))&&(l=Ot(e)),o){const t=Rt(e,!0,s,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else i&&(c.x=Ft(i));return{x:r.left+l.scrollLeft-c.x,y:r.top+l.scrollTop-c.y,width:r.width,height:r.height}}function Yt(t,e){return mt(t)&&"fixed"!==Et(t).position?e?e(t):t.offsetParent:null}function Xt(t,e){const n=ft(t);if(!mt(t))return n;let o=Yt(t,e);for(;o&&bt(o)&&"static"===Et(o).position;)o=Yt(o,e);return o&&("html"===ut(o)||"body"===ut(o)&&"static"===Et(o).position&&!xt(o))?n:o||$t(t)||n}function Zt(t){return"rtl"===Et(t).direction}const Kt={convertOffsetParentRelativeRectToViewportRelativeRect:Mt,getDocumentElement:ht,getClippingRect:qt,getOffsetParent:Xt,getElementRects:async function(t){let{reference:e,floating:n,strategy:o}=t;const i=this.getOffsetParent||Xt,s=this.getDimensions;return{reference:Ut(e,await i(n),o),floating:I({x:0,y:0},await s(n))}},getClientRects:jt,getDimensions:zt,getScale:Lt,isElement:dt,isRTL:Zt};function Gt(t,e){let n,o=null;const i=ht(t);function s(){clearTimeout(n),o&&o.disconnect(),o=null}function r(l,c){void 0===l&&(l=!1),void 0===c&&(c=1),s();const{left:a,top:u,width:f,height:h}=t.getBoundingClientRect();if(l||e(),!f||!h)return;const p={rootMargin:-R(u)+"px "+-R(i.clientWidth-(a+f))+"px "+-R(i.clientHeight-(u+h))+"px "+-R(a)+"px",threshold:C(0,P(1,c))||1};let d=!0;function m(t){const e=t[0].intersectionRatio;if(e!==c){if(!d)return r();e?r(!1,e):n=setTimeout((()=>{r(!1,1e-7)}),100)}d=!1}try{o=new IntersectionObserver(m,I({},p,{root:i.ownerDocument}))}catch(g){o=new IntersectionObserver(m,p)}o.observe(t)}return r(!0),s}function Jt(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:r="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:c=!1}=o,a=It(t),u=i||s?[...a?_t(a):[],..._t(e)]:[];u.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)}));const f=a&&l?Gt(a,n):null;let h,p=-1,d=null;r&&(d=new ResizeObserver((t=>{let[o]=t;o&&o.target===a&&d&&(d.unobserve(e),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{d&&d.observe(e)}))),n()})),a&&!c&&d.observe(a),d.observe(e));let m=c?Rt(t):null;function g(){const e=Rt(t);!m||e.x===m.x&&e.y===m.y&&e.width===m.width&&e.height===m.height||n(),m=e,h=requestAnimationFrame(g)}return c&&g(),n(),()=>{u.forEach((t=>{i&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)})),f&&f(),d&&d.disconnect(),d=null,c&&cancelAnimationFrame(h)}}const Qt=(t,e,n)=>{const o=new Map,i=I({platform:Kt},n),s=I({},i.platform,{_c:o});return it(t,e,I({},i,{platform:s}))};function te(t){t.cleanup&&t.cleanup();const e=t._getResolvedAttachToOptions();let n=e.element;const o=re(e,t),i=_(e);return i&&(n=document.body,t.shepherdElementComponent.getElement().classList.add("shepherd-centered")),t.cleanup=Jt(n,t.el,(()=>{t.el?oe(n,t,o,i):t.cleanup()})),t.target=e.element,o}function ee(t,e){return{floatingUIOptions:m(t.floatingUIOptions||{},e.floatingUIOptions||{})}}function ne(t){t.cleanup&&t.cleanup(),t.cleanup=null}function oe(t,e,n,o){return Qt(t,e.el,n).then(ie(e,o)).then((t=>new Promise((e=>{setTimeout((()=>e(t)),300)})))).then((t=>{t&&t.el&&t.el.focus({preventScroll:!0})}))}function ie(t,e){return({x:n,y:o,placement:i,middlewareData:s})=>t.el?(e?Object.assign(t.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(t.el.style,{position:"absolute",left:`${n}px`,top:`${o}px`}),t.el.dataset.popperPlacement=i,se(t.el,s),t):t}function se(t,e){const n=t.querySelector(".shepherd-arrow");if(n&&e.arrow){const{x:t,y:o}=e.arrow;Object.assign(n.style,{left:null!=t?`${t}px`:"",top:null!=o?`${o}px`:""})}}function re(t,e){const n={strategy:"absolute",middleware:[]},o=le(e);return _(t)||(n.middleware.push(lt(),ct({limiter:at(),crossAxis:!0})),o&&n.middleware.push(rt({element:o})),n.placement=t.on),m(e.options.floatingUIOptions||{},n)}function le(t){return!(!t.options.arrow||!t.el)&&t.el.querySelector(".shepherd-arrow")}function ce(){}function ae(t,e){for(const n in e)t[n]=e[n];return t}function ue(t){return t()}function fe(){return Object.create(null)}function he(t){t.forEach(ue)}function pe(t){return"function"==typeof t}function de(t,e){return t!=t?e==e:t!==e||t&&"object"==typeof t||"function"==typeof t}function me(t){return 0===Object.keys(t).length}function ge(t,e){t.appendChild(e)}function ye(t,e,n){t.insertBefore(e,n||null)}function be(t){t.parentNode&&t.parentNode.removeChild(t)}function xe(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function $e(t){return document.createElement(t)}function we(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function ve(t){return document.createTextNode(t)}function Ee(){return ve(" ")}function Oe(){return ve("")}function Te(t,e,n,o){return t.addEventListener(e,n,o),()=>t.removeEventListener(e,n,o)}function Se(t,e,n){null==n?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}const _e=["width","height"];function Ae(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const o in e)null==e[o]?t.removeAttribute(o):"style"===o?t.style.cssText=e[o]:"__value"===o?t.value=t[o]=e[o]:n[o]&&n[o].set&&-1===_e.indexOf(o)?t[o]=e[o]:Se(t,o,e[o])}function Ie(t){return Array.from(t.childNodes)}function Le(t,e,n){t.classList[n?"add":"remove"](e)}let Pe;function Ce(t){Pe=t}function ke(){if(!Pe)throw new Error("Function called outside component initialization");return Pe}function Re(t){ke().$$.on_mount.push(t)}function Me(t){ke().$$.after_update.push(t)}const je=[],Fe=[];let De=[];const He=[],Be=Promise.resolve();let Ne=!1;function Ve(){Ne||(Ne=!0,Be.then(Ue))}function We(t){De.push(t)}const qe=new Set;let ze=0;function Ue(){if(0!==ze)return;const t=Pe;do{try{for(;ze<je.length;){const t=je[ze];ze++,Ce(t),Ye(t.$$)}}catch(e){throw je.length=0,ze=0,e}for(Ce(null),je.length=0,ze=0;Fe.length;)Fe.pop()();for(let t=0;t<De.length;t+=1){const e=De[t];qe.has(e)||(qe.add(e),e())}De.length=0}while(je.length);for(;He.length;)He.pop()();Ne=!1,qe.clear(),Ce(t)}function Ye(t){if(null!==t.fragment){t.update(),he(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(We)}}function Xe(t){const e=[],n=[];De.forEach((o=>-1===t.indexOf(o)?e.push(o):n.push(o))),n.forEach((t=>t())),De=e}const Ze=new Set;let Ke;function Ge(){Ke={r:0,c:[],p:Ke}}function Je(){Ke.r||he(Ke.c),Ke=Ke.p}function Qe(t,e){t&&t.i&&(Ze.delete(t),t.i(e))}function tn(t,e,n,o){if(t&&t.o){if(Ze.has(t))return;Ze.add(t),Ke.c.push((()=>{Ze.delete(t),o&&(n&&t.d(1),o())})),t.o(e)}else o&&o()}function en(t,e){const n={},o={},i={$$scope:1};let s=t.length;for(;s--;){const r=t[s],l=e[s];if(l){for(const t in r)t in l||(o[t]=1);for(const t in l)i[t]||(n[t]=l[t],i[t]=1);t[s]=l}else for(const t in r)i[t]=1}for(const r in o)r in n||(n[r]=void 0);return n}function nn(t){t&&t.c()}function on(t,e,n,o){const{fragment:i,after_update:s}=t.$$;i&&i.m(e,n),o||We((()=>{const e=t.$$.on_mount.map(ue).filter(pe);t.$$.on_destroy?t.$$.on_destroy.push(...e):he(e),t.$$.on_mount=[]})),s.forEach(We)}function sn(t,e){const n=t.$$;null!==n.fragment&&(Xe(n.after_update),he(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function rn(t,e){-1===t.$$.dirty[0]&&(je.push(t),Ve(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function ln(t,e,n,o,i,s,r,l=[-1]){const c=Pe;Ce(t);const a=t.$$={fragment:null,ctx:[],props:s,update:ce,not_equal:i,bound:fe(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(c?c.$$.context:[])),callbacks:fe(),dirty:l,skip_bound:!1,root:e.target||c.$$.root};let u=!1;if(a.ctx=n?n(t,e.props||{},((e,n,...o)=>{const s=o.length?o[0]:n;return a.ctx&&i(a.ctx[e],a.ctx[e]=s)&&(!a.skip_bound&&a.bound[e]&&a.bound[e](s),u&&rn(t,e)),n})):[],a.update(),u=!0,he(a.before_update),a.fragment=!!o&&o(a.ctx),e.target){if(e.hydrate){const t=Ie(e.target);a.fragment&&a.fragment.l(t),t.forEach(be)}else a.fragment&&a.fragment.c();e.intro&&Qe(t.$$.fragment),on(t,e.target,e.anchor,e.customElement),Ue()}Ce(c)}class cn{$destroy(){sn(this,1),this.$destroy=ce}$on(t,e){if(!pe(e))return ce;const n=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return n.push(e),()=>{const t=n.indexOf(e);-1!==t&&n.splice(t,1)}}$set(t){this.$$set&&!me(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}function an(t){let e,n,o,i,s;return{c(){e=$e("button"),Se(e,"aria-label",n=t[3]?t[3]:null),Se(e,"class",o=`${t[1]||""} shepherd-button ${t[4]?"shepherd-button-secondary":""}`),e.disabled=t[2],Se(e,"tabindex","0")},m(n,o){ye(n,e,o),e.innerHTML=t[5],i||(s=Te(e,"click",(function(){pe(t[0])&&t[0].apply(this,arguments)})),i=!0)},p(i,[s]){t=i,32&s&&(e.innerHTML=t[5]),8&s&&n!==(n=t[3]?t[3]:null)&&Se(e,"aria-label",n),18&s&&o!==(o=`${t[1]||""} shepherd-button ${t[4]?"shepherd-button-secondary":""}`)&&Se(e,"class",o),4&s&&(e.disabled=t[2])},i:ce,o:ce,d(t){t&&be(e),i=!1,s()}}}function un(t,e,n){let o,i,s,r,l,c,{config:a,step:u}=e;function f(t){return b(t)?t.call(u):t}return t.$$set=t=>{"config"in t&&n(6,a=t.config),"step"in t&&n(7,u=t.step)},t.$$.update=()=>{192&t.$$.dirty&&(n(0,o=a.action?a.action.bind(u.tour):null),n(1,i=a.classes),n(2,s=!!a.disabled&&f(a.disabled)),n(3,r=a.label?f(a.label):null),n(4,l=a.secondary),n(5,c=a.text?f(a.text):null))},[o,i,s,r,l,c,a,u]}class fn extends cn{constructor(t){super(),ln(this,t,un,an,de,{config:6,step:7})}}function hn(t,e,n){const o=t.slice();return o[2]=e[n],o}function pn(t){let e,n,o=t[1],i=[];for(let r=0;r<o.length;r+=1)i[r]=dn(hn(t,o,r));const s=t=>tn(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=Oe()},m(t,o){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,o);ye(t,e,o),n=!0},p(t,n){if(3&n){let r;for(o=t[1],r=0;r<o.length;r+=1){const s=hn(t,o,r);i[r]?(i[r].p(s,n),Qe(i[r],1)):(i[r]=dn(s),i[r].c(),Qe(i[r],1),i[r].m(e.parentNode,e))}for(Ge(),r=o.length;r<i.length;r+=1)s(r);Je()}},i(t){if(!n){for(let t=0;t<o.length;t+=1)Qe(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let e=0;e<i.length;e+=1)tn(i[e]);n=!1},d(t){xe(i,t),t&&be(e)}}}function dn(t){let e,n;return e=new fn({props:{config:t[2],step:t[0]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};2&n&&(o.config=t[2]),1&n&&(o.step=t[0]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function mn(t){let e,n,o=t[1]&&pn(t);return{c(){e=$e("footer"),o&&o.c(),Se(e,"class","shepherd-footer")},m(t,i){ye(t,e,i),o&&o.m(e,null),n=!0},p(t,[n]){t[1]?o?(o.p(t,n),2&n&&Qe(o,1)):(o=pn(t),o.c(),Qe(o,1),o.m(e,null)):o&&(Ge(),tn(o,1,1,(()=>{o=null})),Je())},i(t){n||(Qe(o),n=!0)},o(t){tn(o),n=!1},d(t){t&&be(e),o&&o.d()}}}function gn(t,e,n){let o,{step:i}=e;return t.$$set=t=>{"step"in t&&n(0,i=t.step)},t.$$.update=()=>{1&t.$$.dirty&&n(1,o=i.options.buttons)},[i,o]}class yn extends cn{constructor(t){super(),ln(this,t,gn,mn,de,{step:0})}}function bn(t){let e,n,o,i,s;return{c(){e=$e("button"),n=$e("span"),n.textContent="×",Se(n,"aria-hidden","true"),Se(e,"aria-label",o=t[0].label?t[0].label:"Close Tour"),Se(e,"class","shepherd-cancel-icon"),Se(e,"type","button")},m(o,r){ye(o,e,r),ge(e,n),i||(s=Te(e,"click",t[1]),i=!0)},p(t,[n]){1&n&&o!==(o=t[0].label?t[0].label:"Close Tour")&&Se(e,"aria-label",o)},i:ce,o:ce,d(t){t&&be(e),i=!1,s()}}}function xn(t,e,n){let{cancelIcon:o,step:i}=e;const s=t=>{t.preventDefault(),i.cancel()};return t.$$set=t=>{"cancelIcon"in t&&n(0,o=t.cancelIcon),"step"in t&&n(2,i=t.step)},[o,s,i]}class $n extends cn{constructor(t){super(),ln(this,t,xn,bn,de,{cancelIcon:0,step:2})}}function wn(t){let e;return{c(){e=$e("h3"),Se(e,"id",t[1]),Se(e,"class","shepherd-title")},m(n,o){ye(n,e,o),t[3](e)},p(t,[n]){2&n&&Se(e,"id",t[1])},i:ce,o:ce,d(n){n&&be(e),t[3](null)}}}function vn(t,e,n){let{labelId:o,element:i,title:s}=e;function r(t){Fe[t?"unshift":"push"]((()=>{i=t,n(0,i)}))}return Me((()=>{b(s)&&n(2,s=s()),n(0,i.innerHTML=s,i)})),t.$$set=t=>{"labelId"in t&&n(1,o=t.labelId),"element"in t&&n(0,i=t.element),"title"in t&&n(2,s=t.title)},[i,o,s,r]}class En extends cn{constructor(t){super(),ln(this,t,vn,wn,de,{labelId:1,element:0,title:2})}}function On(t){let e,n;return e=new En({props:{labelId:t[0],title:t[2]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};1&n&&(o.labelId=t[0]),4&n&&(o.title=t[2]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function Tn(t){let e,n;return e=new $n({props:{cancelIcon:t[3],step:t[1]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};8&n&&(o.cancelIcon=t[3]),2&n&&(o.step=t[1]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function Sn(t){let e,n,o,i=t[2]&&On(t),s=t[3]&&t[3].enabled&&Tn(t);return{c(){e=$e("header"),i&&i.c(),n=Ee(),s&&s.c(),Se(e,"class","shepherd-header")},m(t,r){ye(t,e,r),i&&i.m(e,null),ge(e,n),s&&s.m(e,null),o=!0},p(t,[o]){t[2]?i?(i.p(t,o),4&o&&Qe(i,1)):(i=On(t),i.c(),Qe(i,1),i.m(e,n)):i&&(Ge(),tn(i,1,1,(()=>{i=null})),Je()),t[3]&&t[3].enabled?s?(s.p(t,o),8&o&&Qe(s,1)):(s=Tn(t),s.c(),Qe(s,1),s.m(e,null)):s&&(Ge(),tn(s,1,1,(()=>{s=null})),Je())},i(t){o||(Qe(i),Qe(s),o=!0)},o(t){tn(i),tn(s),o=!1},d(t){t&&be(e),i&&i.d(),s&&s.d()}}}function _n(t,e,n){let o,i,{labelId:s,step:r}=e;return t.$$set=t=>{"labelId"in t&&n(0,s=t.labelId),"step"in t&&n(1,r=t.step)},t.$$.update=()=>{2&t.$$.dirty&&(n(2,o=r.options.title),n(3,i=r.options.cancelIcon))},[s,r,o,i]}class An extends cn{constructor(t){super(),ln(this,t,_n,Sn,de,{labelId:0,step:1})}}function In(t){let e;return{c(){e=$e("div"),Se(e,"class","shepherd-text"),Se(e,"id",t[1])},m(n,o){ye(n,e,o),t[3](e)},p(t,[n]){2&n&&Se(e,"id",t[1])},i:ce,o:ce,d(n){n&&be(e),t[3](null)}}}function Ln(t,e,n){let{descriptionId:o,element:i,step:s}=e;function r(t){Fe[t?"unshift":"push"]((()=>{i=t,n(0,i)}))}return Me((()=>{let{text:t}=s.options;b(t)&&(t=t.call(s)),y(t)?i.appendChild(t):n(0,i.innerHTML=t,i)})),t.$$set=t=>{"descriptionId"in t&&n(1,o=t.descriptionId),"element"in t&&n(0,i=t.element),"step"in t&&n(2,s=t.step)},[i,o,s,r]}class Pn extends cn{constructor(t){super(),ln(this,t,Ln,In,de,{descriptionId:1,element:0,step:2})}}function Cn(t){let e,n;return e=new An({props:{labelId:t[1],step:t[2]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};2&n&&(o.labelId=t[1]),4&n&&(o.step=t[2]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function kn(t){let e,n;return e=new Pn({props:{descriptionId:t[0],step:t[2]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};1&n&&(o.descriptionId=t[0]),4&n&&(o.step=t[2]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function Rn(t){let e,n;return e=new yn({props:{step:t[2]}}),{c(){nn(e.$$.fragment)},m(t,o){on(e,t,o),n=!0},p(t,n){const o={};4&n&&(o.step=t[2]),e.$set(o)},i(t){n||(Qe(e.$$.fragment,t),n=!0)},o(t){tn(e.$$.fragment,t),n=!1},d(t){sn(e,t)}}}function Mn(t){let e,n,o,i,s=!$(t[2].options.title)||t[2].options.cancelIcon&&t[2].options.cancelIcon.enabled,r=!$(t[2].options.text),l=Array.isArray(t[2].options.buttons)&&t[2].options.buttons.length,c=s&&Cn(t),a=r&&kn(t),u=l&&Rn(t);return{c(){e=$e("div"),c&&c.c(),n=Ee(),a&&a.c(),o=Ee(),u&&u.c(),Se(e,"class","shepherd-content")},m(t,s){ye(t,e,s),c&&c.m(e,null),ge(e,n),a&&a.m(e,null),ge(e,o),u&&u.m(e,null),i=!0},p(t,[i]){4&i&&(s=!$(t[2].options.title)||t[2].options.cancelIcon&&t[2].options.cancelIcon.enabled),s?c?(c.p(t,i),4&i&&Qe(c,1)):(c=Cn(t),c.c(),Qe(c,1),c.m(e,n)):c&&(Ge(),tn(c,1,1,(()=>{c=null})),Je()),4&i&&(r=!$(t[2].options.text)),r?a?(a.p(t,i),4&i&&Qe(a,1)):(a=kn(t),a.c(),Qe(a,1),a.m(e,o)):a&&(Ge(),tn(a,1,1,(()=>{a=null})),Je()),4&i&&(l=Array.isArray(t[2].options.buttons)&&t[2].options.buttons.length),l?u?(u.p(t,i),4&i&&Qe(u,1)):(u=Rn(t),u.c(),Qe(u,1),u.m(e,null)):u&&(Ge(),tn(u,1,1,(()=>{u=null})),Je())},i(t){i||(Qe(c),Qe(a),Qe(u),i=!0)},o(t){tn(c),tn(a),tn(u),i=!1},d(t){t&&be(e),c&&c.d(),a&&a.d(),u&&u.d()}}}function jn(t,e,n){let{descriptionId:o,labelId:i,step:s}=e;return t.$$set=t=>{"descriptionId"in t&&n(0,o=t.descriptionId),"labelId"in t&&n(1,i=t.labelId),"step"in t&&n(2,s=t.step)},[o,i,s]}class Fn extends cn{constructor(t){super(),ln(this,t,jn,Mn,de,{descriptionId:0,labelId:1,step:2})}}function Dn(t){let e;return{c(){e=$e("div"),Se(e,"class","shepherd-arrow"),Se(e,"data-popper-arrow","")},m(t,n){ye(t,e,n)},d(t){t&&be(e)}}}function Hn(t){let e,n,o,i,s,r,l,c,a=t[4].options.arrow&&t[4].options.attachTo&&t[4].options.attachTo.element&&t[4].options.attachTo.on&&Dn();o=new Fn({props:{descriptionId:t[2],labelId:t[3],step:t[4]}});let u=[{"aria-describedby":i=$(t[4].options.text)?null:t[2]},{"aria-labelledby":s=t[4].options.title?t[3]:null},t[1],{role:"dialog"},{tabindex:"0"}],f={};for(let h=0;h<u.length;h+=1)f=ae(f,u[h]);return{c(){e=$e("div"),a&&a.c(),n=Ee(),nn(o.$$.fragment),Ae(e,f),Le(e,"shepherd-has-cancel-icon",t[5]),Le(e,"shepherd-has-title",t[6]),Le(e,"shepherd-element",!0)},m(i,s){ye(i,e,s),a&&a.m(e,null),ge(e,n),on(o,e,null),t[13](e),r=!0,l||(c=Te(e,"keydown",t[7]),l=!0)},p(t,[l]){t[4].options.arrow&&t[4].options.attachTo&&t[4].options.attachTo.element&&t[4].options.attachTo.on?a||(a=Dn(),a.c(),a.m(e,n)):a&&(a.d(1),a=null);const c={};4&l&&(c.descriptionId=t[2]),8&l&&(c.labelId=t[3]),16&l&&(c.step=t[4]),o.$set(c),Ae(e,f=en(u,[(!r||20&l&&i!==(i=$(t[4].options.text)?null:t[2]))&&{"aria-describedby":i},(!r||24&l&&s!==(s=t[4].options.title?t[3]:null))&&{"aria-labelledby":s},2&l&&t[1],{role:"dialog"},{tabindex:"0"}])),Le(e,"shepherd-has-cancel-icon",t[5]),Le(e,"shepherd-has-title",t[6]),Le(e,"shepherd-element",!0)},i(t){r||(Qe(o.$$.fragment,t),r=!0)},o(t){tn(o.$$.fragment,t),r=!1},d(n){n&&be(e),a&&a.d(),sn(o),t[13](null),l=!1,c()}}}const Bn=9,Nn=27,Vn=37,Wn=39;function qn(t){return t.split(" ").filter((t=>!!t.length))}function zn(t,e,n){let o,i,s,{classPrefix:r,element:l,descriptionId:c,firstFocusableElement:a,focusableElements:u,labelId:f,lastFocusableElement:h,step:p,dataStepId:d}=e;const m=()=>l;function g(){y(s),s=p.options.classes,b(s)}function y(t){if(x(t)){const e=qn(t);e.length&&l.classList.remove(...e)}}function b(t){if(x(t)){const e=qn(t);e.length&&l.classList.add(...e)}}Re((()=>{n(1,d={[`data-${r}shepherd-step-id`]:p.id}),n(9,u=l.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]')),n(8,a=u[0]),n(10,h=u[u.length-1])})),Me((()=>{s!==p.options.classes&&g()}));const $=t=>{const{tour:e}=p;switch(t.keyCode){case Bn:if(0===u.length){t.preventDefault();break}t.shiftKey?(document.activeElement===a||document.activeElement.classList.contains("shepherd-element"))&&(t.preventDefault(),h.focus()):document.activeElement===h&&(t.preventDefault(),a.focus());break;case Nn:e.options.exitOnEsc&&(t.stopPropagation(),p.cancel());break;case Vn:e.options.keyboardNavigation&&(t.stopPropagation(),e.back());break;case Wn:e.options.keyboardNavigation&&(t.stopPropagation(),e.next())}};function w(t){Fe[t?"unshift":"push"]((()=>{l=t,n(0,l)}))}return t.$$set=t=>{"classPrefix"in t&&n(11,r=t.classPrefix),"element"in t&&n(0,l=t.element),"descriptionId"in t&&n(2,c=t.descriptionId),"firstFocusableElement"in t&&n(8,a=t.firstFocusableElement),"focusableElements"in t&&n(9,u=t.focusableElements),"labelId"in t&&n(3,f=t.labelId),"lastFocusableElement"in t&&n(10,h=t.lastFocusableElement),"step"in t&&n(4,p=t.step),"dataStepId"in t&&n(1,d=t.dataStepId)},t.$$.update=()=>{16&t.$$.dirty&&(n(5,o=p.options&&p.options.cancelIcon&&p.options.cancelIcon.enabled),n(6,i=p.options&&p.options.title))},[l,d,c,f,p,o,i,$,a,u,h,r,m,w]}class Un extends cn{constructor(t){super(),ln(this,t,zn,Hn,de,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class Yn extends w{constructor(t,e={}){return super(t,e),this.tour=t,this.classPrefix=this.tour.options?T(this.tour.options.classPrefix):"",this.styles=t.styles,this._resolvedAttachTo=null,v(this),this._setOptions(e),this}cancel(){this.tour.cancel(),this.trigger("cancel")}complete(){this.tour.complete(),this.trigger("complete")}destroy(){ne(this),y(this.el)&&(this.el.remove(),this.el=null),this._updateStepTargetOnHide(),this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide(),this.trigger("before-hide"),this.el&&(this.el.hidden=!0),this._updateStepTargetOnHide(),this.trigger("hide")}_resolveAttachToOptions(){return this._resolvedAttachTo=S(this),this._resolvedAttachTo}_getResolvedAttachToOptions(){return null===this._resolvedAttachTo?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return Boolean(this.el&&!this.el.hidden)}show(){return b(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then((()=>this._show())):Promise.resolve(this._show())}updateStepOptions(t){Object.assign(this.options,t),this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){const t=`${this.id}-description`,e=`${this.id}-label`;return this.shepherdElementComponent=new Un({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:t,labelId:e,step:this,styles:this.styles}}),this.shepherdElementComponent.getElement()}_scrollTo(t){const{element:e}=this._getResolvedAttachToOptions();b(this.options.scrollToHandler)?this.options.scrollToHandler(e):g(e)&&"function"==typeof e.scrollIntoView&&e.scrollIntoView(t)}_getClassOptions(t){const e=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions,n=t.classes?t.classes:"",o=e&&e.classes?e.classes:"",i=[...n.split(" "),...o.split(" ")],s=new Set(i);return Array.from(s).join(" ").trim()}_setOptions(t={}){let e=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;e=m({},e||{}),this.options=Object.assign({arrow:!0},e,t,ee(e,t));const{when:n}=this.options;this.options.classes=this._getClassOptions(t),this.destroy(),this.id=this.options.id||`step-${A()}`,n&&Object.keys(n).forEach((t=>{this.on(t,n[t],this)}))}_setupElements(){$(this.el)||this.destroy(),this.el=this._createTooltipContent(),this.options.advanceOn&&O(this),te(this)}_show(){this.trigger("before-show"),this._resolveAttachToOptions(),this._setupElements(),this.tour.modal||this.tour._setupModal(),this.tour.modal.setupForStep(this),this._styleTargetElementForStep(this),this.el.hidden=!1,this.options.scrollTo&&setTimeout((()=>{this._scrollTo(this.options.scrollTo)})),this.el.hidden=!1;const t=this.shepherdElementComponent.getElement(),e=this.target||document.body;e.classList.add(`${this.classPrefix}shepherd-enabled`),e.classList.add(`${this.classPrefix}shepherd-target`),t.classList.add("shepherd-enabled"),this.trigger("show")}_styleTargetElementForStep(t){const e=t.target;e&&(t.options.highlightClass&&e.classList.add(t.options.highlightClass),e.classList.remove("shepherd-target-click-disabled"),!1===t.options.canClickTarget&&e.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){const t=this.target||document.body;this.options.highlightClass&&t.classList.remove(this.options.highlightClass),t.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function Xn(t){if(t){const{steps:e}=t;e.forEach((t=>{t.options&&!1===t.options.canClickTarget&&t.options.attachTo&&t.target instanceof HTMLElement&&t.target.classList.remove("shepherd-target-click-disabled")}))}}function Zn({width:t,height:e,x:n=0,y:o=0,r:i=0}){const{innerWidth:s,innerHeight:r}=window,{topLeft:l=0,topRight:c=0,bottomRight:a=0,bottomLeft:u=0}="number"==typeof i?{topLeft:i,topRight:i,bottomRight:i,bottomLeft:i}:i;return`M${s},${r}H0V0H${s}V${r}ZM${n+l},${o}a${l},${l},0,0,0-${l},${l}V${e+o-u}a${u},${u},0,0,0,${u},${u}H${t+n-a}a${a},${a},0,0,0,${a}-${a}V${o+c}a${c},${c},0,0,0-${c}-${c}Z`}function Kn(t){let e,n,o,i,s;return{c(){e=we("svg"),n=we("path"),Se(n,"d",t[2]),Se(e,"class",o=(t[1]?"shepherd-modal-is-visible":"")+" shepherd-modal-overlay-container")},m(o,r){ye(o,e,r),ge(e,n),t[11](e),i||(s=Te(e,"touchmove",t[3]),i=!0)},p(t,[i]){4&i&&Se(n,"d",t[2]),2&i&&o!==(o=(t[1]?"shepherd-modal-is-visible":"")+" shepherd-modal-overlay-container")&&Se(e,"class",o)},i:ce,o:ce,d(n){n&&be(e),t[11](null),i=!1,s()}}}function Gn(t){if(!t)return null;const e=t instanceof HTMLElement&&window.getComputedStyle(t).overflowY;return"hidden"!==e&&"visible"!==e&&t.scrollHeight>=t.clientHeight?t:Gn(t.parentElement)}function Jn(t,e){const n=t.getBoundingClientRect();let o=n.y||n.top,i=n.bottom||o+n.height;if(e){const t=e.getBoundingClientRect(),n=t.y||t.top,s=t.bottom||n+t.height;o=Math.max(o,n),i=Math.min(i,s)}return{y:o,height:Math.max(i-o,0)}}function Qn(t,e,n){let{element:o,openingProperties:i}=e;A();let s,r,l=!1;a();const c=()=>o;function a(){n(4,i={width:0,height:0,x:0,y:0,r:0})}function u(){n(1,l=!1),y()}function f(t=0,e=0,o,s){if(s){const{y:r,height:l}=Jn(s,o),{x:c,width:a,left:u}=s.getBoundingClientRect();n(4,i={width:a+2*t,height:l+2*t,x:(c||u)-t,y:r-t,r:e})}else a()}function h(t){y(),t.tour.options.useModalOverlay?(b(t),p()):u()}function p(){n(1,l=!0)}const d=t=>{t.preventDefault()},m=t=>{t.stopPropagation()};function g(){window.addEventListener("touchmove",d,{passive:!1})}function y(){s&&(cancelAnimationFrame(s),s=void 0),window.removeEventListener("touchmove",d,{passive:!1})}function b(t){const{modalOverlayOpeningPadding:e,modalOverlayOpeningRadius:n}=t.options,o=Gn(t.target),i=()=>{s=void 0,f(e,n,o,t.target),s=requestAnimationFrame(i)};i(),g()}function x(t){Fe[t?"unshift":"push"]((()=>{o=t,n(0,o)}))}return t.$$set=t=>{"element"in t&&n(0,o=t.element),"openingProperties"in t&&n(4,i=t.openingProperties)},t.$$.update=()=>{16&t.$$.dirty&&n(2,r=Zn(i))},[o,l,r,m,i,c,a,u,f,h,p,x]}class to extends cn{constructor(t){super(),ln(this,t,Qn,Kn,de,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}const eo=new w;class no extends w{constructor(t={}){super(t),v(this);const e={exitOnEsc:!0,keyboardNavigation:!0};return this.options=Object.assign({},e,t),this.classPrefix=T(this.options.classPrefix),this.steps=[],this.addSteps(this.options.steps),["active","cancel","complete","inactive","show","start"].map((t=>{(t=>{this.on(t,(e=>{(e=e||{}).tour=this,eo.trigger(t,e)}))})(t)})),this._setTourID(),this}addStep(t,e){let n=t;return n instanceof Yn?n.tour=this:n=new Yn(this,n),$(e)?this.steps.push(n):this.steps.splice(e,0,n),n}addSteps(t){return Array.isArray(t)&&t.forEach((t=>{this.addStep(t)})),this}back(){const t=this.steps.indexOf(this.currentStep);this.show(t-1,!1)}async cancel(){if(this.options.confirmCancel){const t="function"==typeof this.options.confirmCancel,e=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";(t?await this.options.confirmCancel():window.confirm(e))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(t){return this.steps.find((e=>e.id===t))}getCurrentStep(){return this.currentStep}hide(){const t=this.getCurrentStep();if(t)return t.hide()}isActive(){return eo.activeTour===this}next(){const t=this.steps.indexOf(this.currentStep);t===this.steps.length-1?this.complete():this.show(t+1,!0)}removeStep(t){const e=this.getCurrentStep();this.steps.some(((e,n)=>{if(e.id===t)return e.isOpen()&&e.hide(),e.destroy(),this.steps.splice(n,1),!0})),e&&e.id===t&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(t=0,e=!0){const n=x(t)?this.getById(t):this.steps[t];n&&(this._updateStateBeforeShow(),b(n.options.showOn)&&!n.options.showOn()?this._skipStep(n,e):(this.trigger("show",{step:n,previous:this.currentStep}),this.currentStep=n,n.show()))}start(){this.trigger("start"),this.focusedElBeforeOpen=document.activeElement,this.currentStep=null,this._setupModal(),this._setupActiveTour(),this.next()}_done(t){const e=this.steps.indexOf(this.currentStep);if(Array.isArray(this.steps)&&this.steps.forEach((t=>t.destroy())),Xn(this),this.trigger(t,{index:e}),eo.activeTour=null,this.trigger("inactive",{tour:this}),this.modal&&this.modal.hide(),("cancel"===t||"complete"===t)&&this.modal){const t=document.querySelector(".shepherd-modal-overlay-container");t&&t.remove()}y(this.focusedElBeforeOpen)&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this}),eo.activeTour=this}_setupModal(){this.modal=new to({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(t,e){const n=this.steps.indexOf(t);if(n===this.steps.length-1)this.complete();else{const t=e?n+1:n-1;this.show(t,e)}}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide(),this.isActive()||this._setupActiveTour()}_setTourID(){const t=this.options.tourName||"tour";this.id=`${t}--${A()}`}}const oo="undefined"==typeof window;class io{constructor(){}}return oo?Object.assign(eo,{Tour:io,Step:io}):Object.assign(eo,{Tour:no,Step:Yn}),eo}());try{window.Shepherd=o}catch(i){}
