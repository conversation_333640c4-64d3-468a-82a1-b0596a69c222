!function(){window.Helpers.initCustomOptionCheck();const e=document.querySelector(".flatpickr"),t=document.querySelector(".contact-number-mask"),o=$("#plCountry"),i=document.querySelector("#plFurnishingDetails");t&&new Cleave(t,{phone:!0,phoneRegionCode:"US"}),o&&(o.wrap('<div class="position-relative"></div>'),o.select2({placeholder:"Select country",dropdownParent:o.parent()})),e&&e.flatpickr();i&&new Tagify(i,{whitelist:["Fridge","TV","AC","WiFi","RO","Washing Machine","Sofa","Bed","Dining Table","Microwave","Cupboard"],maxTags:10,dropdown:{maxItems:20,classname:"tags-inline",enabled:0,closeOnSelect:!1}});const n=document.querySelector("#wizard-property-listing");if(void 0!==typeof n&&null!==n){const e=n.querySelector("#wizard-property-listing-form"),t=e.querySelector("#personal-details"),o=e.querySelector("#property-details"),i=e.querySelector("#property-features"),a=e.querySelector("#property-area"),r=e.querySelector("#price-details"),l=[].slice.call(e.querySelectorAll(".btn-next")),s=[].slice.call(e.querySelectorAll(".btn-prev")),u=new Stepper(n,{linear:!0}),c=FormValidation.formValidation(t,{fields:{plFirstName:{validators:{notEmpty:{message:"Please enter your first name"}}},plLastName:{validators:{notEmpty:{message:"Please enter your last name"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton},init:e=>{e.on("plugins.message.placed",(function(e){e.element.parentElement.classList.contains("input-group")&&e.element.parentElement.insertAdjacentElement("afterend",e.messageElement)}))}}).on("core.form.valid",(function(){u.next()})),p=FormValidation.formValidation(o,{fields:{plPropertyType:{validators:{notEmpty:{message:"Please select property type"}}},plZipCode:{validators:{notEmpty:{message:"Please enter zip code"},stringLength:{min:4,max:10,message:"The zip code must be more than 4 and less than 10 characters long"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(e,t){return"plAddress"===e?".col-lg-12":".col-sm-6"}}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),d=$("#plPropertyType");d.length&&(d.wrap('<div class="position-relative"></div>'),d.select2({placeholder:"Select property type",dropdownParent:d.parent()}).on("change",(function(){p.revalidateField("plPropertyType")})));const m=FormValidation.formValidation(i,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),g=FormValidation.formValidation(a,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-12"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),w=FormValidation.formValidation(r,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-12"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){alert("Submitted..!!")}));l.forEach((e=>{e.addEventListener("click",(e=>{switch(u._currentIndex){case 0:c.validate();break;case 1:p.validate();break;case 2:m.validate();break;case 3:g.validate();break;case 4:w.validate()}}))})),s.forEach((e=>{e.addEventListener("click",(e=>{switch(u._currentIndex){case 4:case 3:case 2:case 1:u.previous()}}))}))}}();
