window.isRtl=window.Helpers.isRtl(),window.isDarkStyle=window.Helpers.isDarkStyle();let e,t=!1;document.getElementById("layout-menu")&&(t=document.getElementById("layout-menu").classList.contains("menu-horizontal")),function(){var a,s;setTimeout((function(){window.Helpers.initCustomOptionCheck()}),1e3),"undefined"!=typeof Waves&&(Waves.init(),Waves.attach(".btn[class*='btn-']:not(.position-relative):not([class*='btn-outline-']):not([class*='btn-label-'])",["waves-light"]),Waves.attach("[class*='btn-outline-']:not(.position-relative)"),Waves.attach("[class*='btn-label-']:not(.position-relative)"),Waves.attach(".pagination .page-item .page-link"),Waves.attach(".dropdown-menu .dropdown-item"),Waves.attach(".light-style .list-group .list-group-item-action"),Waves.attach(".dark-style .list-group .list-group-item-action",["waves-light"]),Waves.attach(".nav-tabs:not(.nav-tabs-widget) .nav-item .nav-link"),Waves.attach(".nav-pills .nav-item .nav-link",["waves-light"])),document.querySelectorAll("#layout-menu").forEach((function(a){e=new Menu(a,{orientation:t?"horizontal":"vertical",closeChildren:!!t,showDropdownOnHover:localStorage.getItem("templateCustomizer-"+templateName+"--ShowDropdownOnHover")?"true"===localStorage.getItem("templateCustomizer-"+templateName+"--ShowDropdownOnHover"):void 0===window.templateCustomizer||window.templateCustomizer.settings.defaultShowDropdownOnHover}),window.Helpers.scrollToActive(!1),window.Helpers.mainMenu=e})),document.querySelectorAll(".layout-menu-toggle").forEach((e=>{e.addEventListener("click",(e=>{if(e.preventDefault(),window.Helpers.toggleCollapsed(),config.enableMenuLocalStorage&&!window.Helpers.isSmallScreen())try{localStorage.setItem("templateCustomizer-"+templateName+"--LayoutCollapsed",String(window.Helpers.isCollapsed()));let e=document.querySelector(".template-customizer-layouts-options");if(e){let t=window.Helpers.isCollapsed()?"collapsed":"expanded";e.querySelector(`input[value="${t}"]`).click()}}catch(t){}}))})),window.Helpers.swipeIn(".drag-target",(function(e){window.Helpers.setCollapsed(!1)})),window.Helpers.swipeOut("#layout-menu",(function(e){window.Helpers.isSmallScreen()&&window.Helpers.setCollapsed(!0)}));let o=document.getElementsByClassName("menu-inner"),n=document.getElementsByClassName("menu-inner-shadow")[0];o.length>0&&n&&o[0].addEventListener("ps-scroll-y",(function(){this.querySelector(".ps__thumb-y").offsetTop?n.style.display="block":n.style.display="none"}));let l=document.querySelector(".dropdown-style-switcher");const i=document.documentElement.getAttribute("data-style");let r=localStorage.getItem("templateCustomizer-"+templateName+"--Style")||((null==(s=null==(a=window.templateCustomizer)?void 0:a.settings)?void 0:s.defaultStyle)??"light");
//!if there is no Customizer then use default style as light
if(window.templateCustomizer&&l){[].slice.call(l.children[1].querySelectorAll(".dropdown-item")).forEach((function(e){e.classList.remove("active"),e.addEventListener("click",(function(){let e=this.getAttribute("data-theme");"light"===e?window.templateCustomizer.setStyle("light"):"dark"===e?window.templateCustomizer.setStyle("dark"):window.templateCustomizer.setStyle("system")})),e.getAttribute("data-theme")===i&&e.classList.add("active")}));const e=l.querySelector("i");"light"===r?(e.classList.add("ti-sun"),new bootstrap.Tooltip(e,{title:"Light Mode",fallbackPlacements:["bottom"]})):"dark"===r?(e.classList.add("ti-moon-stars"),new bootstrap.Tooltip(e,{title:"Dark Mode",fallbackPlacements:["bottom"]})):(e.classList.add("ti-device-desktop-analytics"),new bootstrap.Tooltip(e,{title:"System Mode",fallbackPlacements:["bottom"]}))}var c;"system"===(c=r)&&(c=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),[].slice.call(document.querySelectorAll("[data-app-"+c+"-img]")).map((function(e){const t=e.getAttribute("data-app-"+c+"-img");e.src=assetsPath+"img/"+t}));let d=document.getElementsByClassName("dropdown-language");if(d.length){let e=function(e){"rtl"===e?"true"!==localStorage.getItem("templateCustomizer-"+templateName+"--Rtl")&&window.templateCustomizer&&window.templateCustomizer.setRtl(!0):"true"===localStorage.getItem("templateCustomizer-"+templateName+"--Rtl")&&window.templateCustomizer&&window.templateCustomizer.setRtl(!1)};let t=d[0].querySelectorAll(".dropdown-item");const a=d[0].querySelector(".dropdown-item.active");e(a.dataset.textDirection);for(let s=0;s<t.length;s++)t[s].addEventListener("click",(function(){let t=this.getAttribute("data-text-direction");window.templateCustomizer.setLang(this.getAttribute("data-language")),e(t)}))}setTimeout((function(){let e=document.querySelector(".template-customizer-reset-btn");e&&(e.onclick=function(){window.location.href=baseUrl+"lang/en"})}),1500);const u=document.querySelector(".dropdown-notifications-all"),m=document.querySelectorAll(".dropdown-notifications-read");u&&u.addEventListener("click",(e=>{m.forEach((e=>{e.closest(".dropdown-notifications-item").classList.add("marked-as-read")}))})),m&&m.forEach((e=>{e.addEventListener("click",(t=>{e.closest(".dropdown-notifications-item").classList.toggle("marked-as-read")}))}));document.querySelectorAll(".dropdown-notifications-archive").forEach((e=>{e.addEventListener("click",(t=>{e.closest(".dropdown-notifications-item").remove()}))}));[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(e){return new bootstrap.Tooltip(e)}));const p=function(e){"show.bs.collapse"==e.type||"show.bs.collapse"==e.type?e.target.closest(".accordion-item").classList.add("active"):e.target.closest(".accordion-item").classList.remove("active")};[].slice.call(document.querySelectorAll(".accordion")).map((function(e){e.addEventListener("show.bs.collapse",p),e.addEventListener("hide.bs.collapse",p)})),window.Helpers.setAutoUpdate(!0),window.Helpers.initPasswordToggle(),window.Helpers.initSpeechToText(),window.Helpers.initNavbarDropdownScrollbar();let w=document.querySelector("[data-template^='horizontal-menu']");if(w&&(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?window.Helpers.setNavbarFixed("fixed"):window.Helpers.setNavbarFixed("")),window.addEventListener("resize",(function(t){window.innerWidth>=window.Helpers.LAYOUT_BREAKPOINT&&document.querySelector(".search-input-wrapper")&&(document.querySelector(".search-input-wrapper").classList.add("d-none"),document.querySelector(".search-input").value=""),w&&(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?window.Helpers.setNavbarFixed("fixed"):window.Helpers.setNavbarFixed(""),setTimeout((function(){window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?document.getElementById("layout-menu")&&document.getElementById("layout-menu").classList.contains("menu-horizontal")&&e.switchMenu("vertical"):document.getElementById("layout-menu")&&document.getElementById("layout-menu").classList.contains("menu-vertical")&&e.switchMenu("horizontal")}),100))}),!0),!t&&!window.Helpers.isSmallScreen()&&("undefined"!=typeof TemplateCustomizer&&(window.templateCustomizer.settings.defaultMenuCollapsed?window.Helpers.setCollapsed(!0,!1):window.Helpers.setCollapsed(!1,!1)),"undefined"!=typeof config&&config.enableMenuLocalStorage))try{null!==localStorage.getItem("templateCustomizer-"+templateName+"--LayoutCollapsed")&&window.Helpers.setCollapsed("true"===localStorage.getItem("templateCustomizer-"+templateName+"--LayoutCollapsed"),!1)}catch(h){}}(),"undefined"!=typeof $&&$((function(){window.Helpers.initSidebarToggle();var e=$(".search-toggler"),t=$(".search-input-wrapper"),a=$(".search-input"),s=$(".content-backdrop");if(e.length&&e.on("click",(function(){t.length&&(t.toggleClass("d-none"),a.focus())})),$(document).on("keydown",(function(e){let s=e.ctrlKey,o=191===e.which;s&&o&&t.length&&(t.toggleClass("d-none"),a.focus())})),setTimeout((function(){var e=$(".twitter-typeahead");a.on("focus",(function(){t.hasClass("container-xxl")?(t.find(e).addClass("container-xxl"),e.removeClass("container-fluid")):t.hasClass("container-fluid")&&(t.find(e).addClass("container-fluid"),e.removeClass("container-xxl"))}))}),10),a.length){var o=function(e){return function(t,a){let s;s=[],e.filter((function(e){if(e.name.toLowerCase().startsWith(t.toLowerCase()))s.push(e);else{if(e.name.toLowerCase().startsWith(t.toLowerCase())||!e.name.toLowerCase().includes(t.toLowerCase()))return[];s.push(e),s.sort((function(e,t){return t.name<e.name?1:-1}))}})),a(s)}},n="search-vertical.json";if($("#layout-menu").hasClass("menu-horizontal"))n="search-horizontal.json";var l,i=$.ajax({url:assetsPath+"json/"+n,dataType:"json",async:!1}).responseJSON;a.each((function(){var e=$(this);a.typeahead({hint:!1,classNames:{menu:"tt-menu navbar-search-suggestion",cursor:"active",suggestion:"suggestion d-flex justify-content-between px-4 py-2 w-100"}},{name:"pages",display:"name",limit:5,source:o(i.pages),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Pages</h6>',suggestion:function({url:e,icon:t,name:a}){return'<a href="'+baseUrl+e+'"><div><i class="ti '+t+' me-2"></i><span class="align-middle">'+a+"</span></div></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Pages</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}},{name:"files",display:"name",limit:4,source:o(i.files),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Files</h6>',suggestion:function({src:e,name:t,subtitle:a,meta:s}){return'<a href="javascript:;"><div class="d-flex w-50"><img class="me-3" src="'+assetsPath+e+'" alt="'+t+'" height="32"><div class="w-75"><h6 class="mb-0">'+t+'</h6><small class="text-muted">'+a+'</small></div></div><small class="text-muted">'+s+"</small></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Files</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}},{name:"members",display:"name",limit:4,source:o(i.members),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Members</h6>',suggestion:function({name:e,src:t,subtitle:a}){return'<a href="'+baseUrl+'app/user/view/account"><div class="d-flex align-items-center"><img class="rounded-circle me-3" src="'+assetsPath+t+'" alt="'+e+'" height="32"><div class="user-info"><h6 class="mb-0">'+e+'</h6><small class="text-muted">'+a+"</small></div></div></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Members</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}}).bind("typeahead:render",(function(){s.addClass("show").removeClass("fade")})).bind("typeahead:select",(function(e,t){"javascript:;"!==t.url&&(window.location=baseUrl+t.url)})).bind("typeahead:close",(function(){a.val(""),e.typeahead("val",""),t.addClass("d-none"),s.addClass("fade").removeClass("show")})),a.on("keyup",(function(){""==a.val()&&s.addClass("fade").removeClass("show")}))})),$(".navbar-search-suggestion").each((function(){l=new PerfectScrollbar($(this)[0],{wheelPropagation:!1,suppressScrollX:!0})})),a.on("keyup",(function(){l.update()}))}}));
