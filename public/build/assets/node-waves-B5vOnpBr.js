import{c as t,g as e}from"./_commonjsHelpers-MdiGH4nz.js";var n={exports:{}};
/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves
 *
 * Copyright 2014-2018 Alfiana E. Si<PERSON>ea and other contributors
 * Released under the MIT license
 * https://github.com/fians/Waves/blob/master/LICENSE
 */const o=e(n.exports=function(){var t=t||{},e=document.querySelectorAll.bind(document),n=Object.prototype.toString,o="ontouchstart"in window;function a(t){return null!==t&&t===t.window}function i(t){return a(t)?t:9===t.nodeType&&t.defaultView}function r(t){var e=typeof t;return"function"===e||"object"===e&&!!t}function s(t){return r(t)&&t.nodeType>0}function u(t){var o=n.call(t);return"[object String]"===o?e(t):r(t)&&/^\[object (Array|HTMLCollection|NodeList|Object)\]$/.test(o)&&t.hasOwnProperty("length")?t:s(t)?[t]:[]}function c(t){var e,n,o={top:0,left:0},a=t&&t.ownerDocument;return e=a.documentElement,void 0!==t.getBoundingClientRect&&(o=t.getBoundingClientRect()),n=i(a),{top:o.top+n.pageYOffset-e.clientTop,left:o.left+n.pageXOffset-e.clientLeft}}function l(t){var e="";for(var n in t)t.hasOwnProperty(n)&&(e+=n+":"+t[n]+";");return e}var d={duration:750,delay:200,show:function(t,e,n){if(2===t.button)return!1;e=e||this;var o=document.createElement("div");o.className="waves-ripple waves-rippling",e.appendChild(o);var a=c(e),i=0,r=0;"touches"in t&&t.touches.length?(i=t.touches[0].pageY-a.top,r=t.touches[0].pageX-a.left):(i=t.pageY-a.top,r=t.pageX-a.left),r=r>=0?r:0,i=i>=0?i:0;var s="scale("+e.clientWidth/100*3+")",u="translate(0,0)";n&&(u="translate("+n.x+"px, "+n.y+"px)"),o.setAttribute("data-hold",Date.now()),o.setAttribute("data-x",r),o.setAttribute("data-y",i),o.setAttribute("data-scale",s),o.setAttribute("data-translate",u);var m={top:i+"px",left:r+"px"};o.classList.add("waves-notransition"),o.setAttribute("style",l(m)),o.classList.remove("waves-notransition"),m["-webkit-transform"]=s+" "+u,m["-moz-transform"]=s+" "+u,m["-ms-transform"]=s+" "+u,m["-o-transform"]=s+" "+u,m.transform=s+" "+u,m.opacity="1";var v="mousemove"===t.type?2500:d.duration;m["-webkit-transition-duration"]=v+"ms",m["-moz-transition-duration"]=v+"ms",m["-o-transition-duration"]=v+"ms",m["transition-duration"]=v+"ms",o.setAttribute("style",l(m))},hide:function(t,e){for(var n=(e=e||this).getElementsByClassName("waves-rippling"),a=0,i=n.length;a<i;a++)v(t,e,n[a]);o&&(e.removeEventListener("touchend",d.hide),e.removeEventListener("touchcancel",d.hide)),e.removeEventListener("mouseup",d.hide),e.removeEventListener("mouseleave",d.hide)}},m={input:function(t){var e=t.parentNode;if("i"!==e.tagName.toLowerCase()||!e.classList.contains("waves-effect")){var n=document.createElement("i");n.className=t.className+" waves-input-wrapper",t.className="waves-button-input",e.replaceChild(n,t),n.appendChild(t);var o=window.getComputedStyle(t,null),a=o.color,i=o.backgroundColor;n.setAttribute("style","color:"+a+";background:"+i),t.setAttribute("style","background-color:rgba(0,0,0,0);")}},img:function(t){var e=t.parentNode;if("i"!==e.tagName.toLowerCase()||!e.classList.contains("waves-effect")){var n=document.createElement("i");e.replaceChild(n,t),n.appendChild(t)}}};function v(t,e,n){if(n){n.classList.remove("waves-rippling");var o=n.getAttribute("data-x"),a=n.getAttribute("data-y"),i=n.getAttribute("data-scale"),r=n.getAttribute("data-translate"),s=350-(Date.now()-Number(n.getAttribute("data-hold")));s<0&&(s=0),"mousemove"===t.type&&(s=150);var u="mousemove"===t.type?2500:d.duration;setTimeout((function(){var t={top:a+"px",left:o+"px",opacity:"0","-webkit-transition-duration":u+"ms","-moz-transition-duration":u+"ms","-o-transition-duration":u+"ms","transition-duration":u+"ms","-webkit-transform":i+" "+r,"-moz-transform":i+" "+r,"-ms-transform":i+" "+r,"-o-transform":i+" "+r,transform:i+" "+r};n.setAttribute("style",l(t)),setTimeout((function(){try{e.removeChild(n)}catch(t){return!1}}),u)}),s)}}var f={touches:0,allowEvent:function(t){var e=!0;return/^(mousedown|mousemove)$/.test(t.type)&&f.touches&&(e=!1),e},registerEvent:function(t){var e=t.type;"touchstart"===e?f.touches+=1:/^(touchend|touchcancel)$/.test(e)&&setTimeout((function(){f.touches&&(f.touches-=1)}),500)}};function p(t){if(!1===f.allowEvent(t))return null;for(var e=null,n=t.target||t.srcElement;n.parentElement;){if(!(n instanceof SVGElement)&&n.classList.contains("waves-effect")){e=n;break}n=n.parentElement}return e}function h(t){var e=p(t);if(null!==e){if(e.disabled||e.getAttribute("disabled")||e.classList.contains("disabled"))return;if(f.registerEvent(t),"touchstart"===t.type&&d.delay){var n=!1,a=setTimeout((function(){a=null,d.show(t,e)}),d.delay),i=function(o){a&&(clearTimeout(a),a=null,d.show(t,e)),n||(n=!0,d.hide(o,e)),s()},r=function(t){a&&(clearTimeout(a),a=null),i(t),s()};e.addEventListener("touchmove",r,!1),e.addEventListener("touchend",i,!1),e.addEventListener("touchcancel",i,!1);var s=function(){e.removeEventListener("touchmove",r),e.removeEventListener("touchend",i),e.removeEventListener("touchcancel",i)}}else d.show(t,e),o&&(e.addEventListener("touchend",d.hide,!1),e.addEventListener("touchcancel",d.hide,!1)),e.addEventListener("mouseup",d.hide,!1),e.addEventListener("mouseleave",d.hide,!1)}}return t.init=function(t){var e=document.body;"duration"in(t=t||{})&&(d.duration=t.duration),"delay"in t&&(d.delay=t.delay),o&&(e.addEventListener("touchstart",h,!1),e.addEventListener("touchcancel",f.registerEvent,!1),e.addEventListener("touchend",f.registerEvent,!1)),e.addEventListener("mousedown",h,!1)},t.attach=function(t,e){var o,a;t=u(t),"[object Array]"===n.call(e)&&(e=e.join(" ")),e=e?" "+e:"";for(var i=0,r=t.length;i<r;i++)a=(o=t[i]).tagName.toLowerCase(),-1!==["input","img"].indexOf(a)&&(m[a](o),o=o.parentElement),-1===o.className.indexOf("waves-effect")&&(o.className+=" waves-effect"+e)},t.ripple=function(t,e){var n=(t=u(t)).length;if((e=e||{}).wait=e.wait||0,e.position=e.position||null,n)for(var o,a,i,r={},s=0,l={type:"mousedown",button:1},m=function(t,e){return function(){d.hide(t,e)}};s<n;s++)o=t[s],a=e.position||{x:o.clientWidth/2,y:o.clientHeight/2},i=c(o),r.x=i.left+a.x,r.y=i.top+a.y,l.pageX=r.x,l.pageY=r.y,d.show(l,o),e.wait>=0&&null!==e.wait&&setTimeout(m({type:"mouseup",button:1},o),e.wait)},t.calm=function(t){for(var e={type:"mouseup",button:1},n=0,o=(t=u(t)).length;n<o;n++)d.hide(e,t[n])},t.displayEffect=function(e){t.init(e)},t}.call(t));window.Waves=o;
