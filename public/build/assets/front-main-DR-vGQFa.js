window.isRtl=window.Helpers.isRtl(),window.isDarkStyle=window.Helpers.isDarkStyle(),function(){var t,e;const a=document.getElementById("navbarSupportedContent"),o=document.querySelector(".layout-navbar"),l=document.querySelectorAll(".navbar-nav .nav-link");setTimeout((function(){window.Helpers.initCustomOptionCheck()}),1e3),"undefined"!=typeof Waves&&(Waves.init(),Waves.attach(".btn[class*='btn-']:not([class*='btn-outline-']):not([class*='btn-label-'])",["waves-light"]),Waves.attach("[class*='btn-outline-']"),Waves.attach("[class*='btn-label-']"),Waves.attach(".pagination .page-item .page-link"));[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(t){return new bootstrap.Tooltip(t)}));const s=function(t){"show.bs.collapse"==t.type||"show.bs.collapse"==t.type?t.target.closest(".accordion-item").classList.add("active"):t.target.closest(".accordion-item").classList.remove("active")};function n(){a.classList.remove("show")}[].slice.call(document.querySelectorAll(".accordion")).map((function(t){t.addEventListener("show.bs.collapse",s),t.addEventListener("hide.bs.collapse",s)})),isRtl&&Helpers._addClass("dropdown-menu-end",document.querySelectorAll("#layout-navbar .dropdown-menu")),window.addEventListener("scroll",(t=>{window.scrollY>10?o.classList.add("navbar-active"):o.classList.remove("navbar-active")})),window.addEventListener("load",(t=>{window.scrollY>10?o.classList.add("navbar-active"):o.classList.remove("navbar-active")})),document.addEventListener("click",(function(t){a.contains(t.target)||n()})),l.forEach((t=>{t.addEventListener("click",(e=>{t.classList.contains("dropdown-toggle")?e.preventDefault():n()}))})),isRtl&&Helpers._addClass("dropdown-menu-end",document.querySelectorAll(".dropdown-menu"));const i=document.querySelectorAll(".nav-link.mega-dropdown");i&&i.forEach((t=>{new MegaDropdown(t)}));let c=document.querySelector(".dropdown-style-switcher");const d=document.documentElement.getAttribute("data-style");let r=localStorage.getItem("templateCustomizer-"+templateName+"--Style")||((null==(e=null==(t=window.templateCustomizer)?void 0:t.settings)?void 0:e.defaultStyle)??"light");
//!if there is no Customizer then use default style as light
if(window.templateCustomizer&&c){[].slice.call(c.children[1].querySelectorAll(".dropdown-item")).forEach((function(t){t.classList.remove("active"),t.addEventListener("click",(function(){let t=this.getAttribute("data-theme");"light"===t?window.templateCustomizer.setStyle("light"):"dark"===t?window.templateCustomizer.setStyle("dark"):window.templateCustomizer.setStyle("system")})),setTimeout((()=>{t.getAttribute("data-theme")===d&&t.classList.add("active")}),1e3)}));const t=c.querySelector("i");"light"===r?(t.classList.add("ti-sun"),new bootstrap.Tooltip(t,{title:"Light Mode",fallbackPlacements:["bottom"]})):"dark"===r?(t.classList.add("ti-moon-stars"),new bootstrap.Tooltip(t,{title:"Dark Mode",fallbackPlacements:["bottom"]})):(t.classList.add("ti-device-desktop-analytics"),new bootstrap.Tooltip(t,{title:"System Mode",fallbackPlacements:["bottom"]}))}!function(t){"system"===t&&(t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");[].slice.call(document.querySelectorAll("[data-app-"+t+"-img]")).map((function(e){const a=e.getAttribute("data-app-"+t+"-img");e.src=assetsPath+"img/"+a}))}(r)}();
