const e=["transitionend","webkitTransitionEnd","oTransitionEnd"],t=["transition","MozTransition","webkitTransition","WebkitTransition","OTransition"],s="\n.layout-menu-fixed .layout-navbar-full .layout-menu,\n.layout-menu-fixed-offcanvas .layout-navbar-full .layout-menu {\n  top: {navbarHeight}px !important;\n}\n.layout-page {\n  padding-top: {navbarHeight}px !important;\n}\n.content-wrapper {\n  padding-bottom: {footerHeight}px !important;\n}";function i(e){throw new Error("Parameter required"+(e?`: \`${e}\``:""))}const n={ROOT_EL:"undefined"!=typeof window?document.documentElement:null,LAYOUT_BREAKPOINT:1200,RESIZE_DELAY:200,menuPsScroll:null,mainMenu:null,_curStyle:null,_styleEl:null,_resizeTimeout:null,_resizeCallback:null,_transitionCallback:null,_transitionCallbackTimeout:null,_listeners:[],_initialized:!1,_autoUpdate:!1,_lastWindowHeight:0,_scrollToActive(e=!1,t=500){const s=this.getLayoutMenu();if(!s)return;let i=s.querySelector("li.menu-item.active:not(.open)");if(i){const s=(e,t,s,i)=>(e/=i/2)<1?s/2*e*e+t:-s/2*((e-=1)*(e-2)-1)+t,n=this.getLayoutMenu().querySelector(".menu-inner");if("string"==typeof i&&(i=document.querySelector(i)),"number"!=typeof i&&(i=i.getBoundingClientRect().top+n.scrollTop),i<parseInt(2*n.clientHeight/3,10))return;const o=n.scrollTop,a=i-o-parseInt(n.clientHeight/2,10),l=+new Date;if(!0===e){const e=()=>{const i=+new Date-l,r=s(i,o,a,t);n.scrollTop=r,i<t?requestAnimationFrame(e):n.scrollTop=a};e()}else n.scrollTop=a}},_swipeIn(e,t){const{Hammer:s}=window;if(void 0!==s&&"string"==typeof e){const i=document.querySelector(e);if(i){new s(i).on("panright",t)}}},_swipeOut(e,t){const{Hammer:s}=window;void 0!==s&&"string"==typeof e&&setTimeout((()=>{const i=document.querySelector(e);if(i){const e=new s(i);e.get("pan").set({direction:s.DIRECTION_ALL,threshold:250}),e.on("panleft",t)}}),500)},_overlayTap(e,t){const{Hammer:s}=window;if(void 0!==s&&"string"==typeof e){const i=document.querySelector(e);if(i){new s(i).on("tap",t)}}},_addClass(e,t=this.ROOT_EL){t&&void 0!==t.length?t.forEach((t=>{t&&e.split(" ").forEach((e=>t.classList.add(e)))})):t&&e.split(" ").forEach((e=>t.classList.add(e)))},_removeClass(e,t=this.ROOT_EL){t&&void 0!==t.length?t.forEach((t=>{t&&e.split(" ").forEach((e=>t.classList.remove(e)))})):t&&e.split(" ").forEach((e=>t.classList.remove(e)))},_toggleClass(e=this.ROOT_EL,t,s){e.classList.contains(t)?e.classList.replace(t,s):e.classList.replace(s,t)},_hasClass(e,t=this.ROOT_EL){let s=!1;return e.split(" ").forEach((e=>{t.classList.contains(e)&&(s=!0)})),s},_findParent(e,t){if(e&&"BODY"===e.tagName.toUpperCase()||"HTML"===e.tagName.toUpperCase())return null;for(e=e.parentNode;e&&"BODY"!==e.tagName.toUpperCase()&&!e.classList.contains(t);)e=e.parentNode;return e=e&&"BODY"!==e.tagName.toUpperCase()?e:null},_triggerWindowEvent(e){if("undefined"!=typeof window)if(document.createEvent){let t;"function"==typeof Event?t=new Event(e):(t=document.createEvent("Event"),t.initEvent(e,!1,!0)),window.dispatchEvent(t)}else window.fireEvent(`on${e}`,document.createEventObject())},_triggerEvent(e){this._triggerWindowEvent(`layout${e}`),this._listeners.filter((t=>t.event===e)).forEach((e=>e.callback.call(null)))},_updateInlineStyle(e=0,t=0){this._styleEl||(this._styleEl=document.createElement("style"),this._styleEl.type="text/css",document.head.appendChild(this._styleEl));const i=s.replace(/\{navbarHeight\}/gi,e).replace(/\{footerHeight\}/gi,t);this._curStyle!==i&&(this._curStyle=i,this._styleEl.textContent=i)},_removeInlineStyle(){this._styleEl&&document.head.removeChild(this._styleEl),this._styleEl=null,this._curStyle=null},_redrawLayoutMenu(){const e=this.getLayoutMenu();if(e&&e.querySelector(".menu")){const t=e.querySelector(".menu-inner"),{scrollTop:s}=t,i=document.documentElement.scrollTop;return e.style.display="none",e.style.display="",t.scrollTop=s,document.documentElement.scrollTop=i,!0}return!1},_supportsTransitionEnd(){if(window.QUnit)return!1;const e=document.body||document.documentElement;if(!e)return!1;let s=!1;return t.forEach((t=>{void 0!==e.style[t]&&(s=!0)})),s},_getNavbarHeight(){const e=this.getLayoutNavbar();if(!e)return 0;if(!this.isSmallScreen())return e.getBoundingClientRect().height;const t=e.cloneNode(!0);t.id=null,t.style.visibility="hidden",t.style.position="absolute",Array.prototype.slice.call(t.querySelectorAll(".collapse.show")).forEach((e=>this._removeClass("show",e))),e.parentNode.insertBefore(t,e);const s=t.getBoundingClientRect().height;return t.parentNode.removeChild(t),s},_getFooterHeight(){const e=this.getLayoutFooter();return e?e.getBoundingClientRect().height:0},_getAnimationDuration(e){const t=window.getComputedStyle(e).transitionDuration;return parseFloat(t)*(-1!==t.indexOf("ms")?1:1e3)},_setMenuHoverState(e){this[e?"_addClass":"_removeClass"]("layout-menu-hover")},_setCollapsed(e){this.isSmallScreen()?e?this._removeClass("layout-menu-expanded"):setTimeout((()=>{this._addClass("layout-menu-expanded")}),this._redrawLayoutMenu()?5:0):this[e?"_addClass":"_removeClass"]("layout-menu-collapsed")},_bindLayoutAnimationEndEvent(t,s){const i=this.getMenu(),n=i?this._getAnimationDuration(i)+50:0;if(!n)return t.call(this),void s.call(this);this._transitionCallback=e=>{e.target===i&&(this._unbindLayoutAnimationEndEvent(),s.call(this))},e.forEach((e=>{i.addEventListener(e,this._transitionCallback,!1)})),t.call(this),this._transitionCallbackTimeout=setTimeout((()=>{this._transitionCallback.call(this,{target:i})}),n)},_unbindLayoutAnimationEndEvent(){const t=this.getMenu();this._transitionCallbackTimeout&&(clearTimeout(this._transitionCallbackTimeout),this._transitionCallbackTimeout=null),t&&this._transitionCallback&&e.forEach((e=>{t.removeEventListener(e,this._transitionCallback,!1)})),this._transitionCallback&&(this._transitionCallback=null)},_bindWindowResizeEvent(){this._unbindWindowResizeEvent();const e=()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),this._resizeTimeout=null),this._triggerEvent("resize")};this._resizeCallback=()=>{this._resizeTimeout&&clearTimeout(this._resizeTimeout),this._resizeTimeout=setTimeout(e,this.RESIZE_DELAY)},window.addEventListener("resize",this._resizeCallback,!1)},_unbindWindowResizeEvent(){this._resizeTimeout&&(clearTimeout(this._resizeTimeout),this._resizeTimeout=null),this._resizeCallback&&(window.removeEventListener("resize",this._resizeCallback,!1),this._resizeCallback=null)},_bindMenuMouseEvents(){if(this._menuMouseEnter&&this._menuMouseLeave&&this._windowTouchStart)return;const e=this.getLayoutMenu();if(!e)return this._unbindMenuMouseEvents();this._menuMouseEnter||(this._menuMouseEnter=()=>this.isSmallScreen()||!this._hasClass("layout-menu-collapsed")||this.isOffcanvas()||this._hasClass("layout-transitioning")?this._setMenuHoverState(!1):this._setMenuHoverState(!0),e.addEventListener("mouseenter",this._menuMouseEnter,!1),e.addEventListener("touchstart",this._menuMouseEnter,!1)),this._menuMouseLeave||(this._menuMouseLeave=()=>{this._setMenuHoverState(!1)},e.addEventListener("mouseleave",this._menuMouseLeave,!1)),this._windowTouchStart||(this._windowTouchStart=e=>{e&&e.target&&this._findParent(e.target,".layout-menu")||this._setMenuHoverState(!1)},window.addEventListener("touchstart",this._windowTouchStart,!0))},_unbindMenuMouseEvents(){if(!this._menuMouseEnter&&!this._menuMouseLeave&&!this._windowTouchStart)return;const e=this.getLayoutMenu();this._menuMouseEnter&&(e&&(e.removeEventListener("mouseenter",this._menuMouseEnter,!1),e.removeEventListener("touchstart",this._menuMouseEnter,!1)),this._menuMouseEnter=null),this._menuMouseLeave&&(e&&e.removeEventListener("mouseleave",this._menuMouseLeave,!1),this._menuMouseLeave=null),this._windowTouchStart&&(e&&window.addEventListener("touchstart",this._windowTouchStart,!0),this._windowTouchStart=null),this._setMenuHoverState(!1)},scrollToActive(e=!1){this._scrollToActive(e)},swipeIn(e,t){this._swipeIn(e,t)},swipeOut(e,t){this._swipeOut(e,t)},overlayTap(e,t){this._overlayTap(e,t)},scrollPageTo(e,t=500){const s=document.scrollingElement;"string"==typeof e&&(e=document.querySelector(e)),"number"!=typeof e&&(e=e.getBoundingClientRect().top+s.scrollTop);const i=s.scrollTop,n=e-i,o=+new Date,a=()=>{const l=+new Date-o,r=(u=l,c=i,d=n,(u/=t/2)<1?d/2*u*u+c:-d/2*((u-=1)*(u-2)-1)+c);var u,c,d;s.scrollTop=r,l<t?requestAnimationFrame(a):s.scrollTop=e};a()},setCollapsed(e=i("collapsed"),t=!0){this.getLayoutMenu()&&(this._unbindLayoutAnimationEndEvent(),t&&this._supportsTransitionEnd()?(this._addClass("layout-transitioning"),e&&this._setMenuHoverState(!1),this._bindLayoutAnimationEndEvent((()=>{this._setCollapsed(e)}),(()=>{this._removeClass("layout-transitioning"),this._triggerWindowEvent("resize"),this._triggerEvent("toggle"),this._setMenuHoverState(!1)}))):(this._addClass("layout-no-transition"),e&&this._setMenuHoverState(!1),this._setCollapsed(e),setTimeout((()=>{this._removeClass("layout-no-transition"),this._triggerWindowEvent("resize"),this._triggerEvent("toggle"),this._setMenuHoverState(!1)}),1)))},toggleCollapsed(e=!0){this.setCollapsed(!this.isCollapsed(),e)},setPosition(e=i("fixed"),t=i("offcanvas")){this._removeClass("layout-menu-offcanvas layout-menu-fixed layout-menu-fixed-offcanvas"),!e&&t?this._addClass("layout-menu-offcanvas"):e&&!t?(this._addClass("layout-menu-fixed"),this._redrawLayoutMenu()):e&&t&&(this._addClass("layout-menu-fixed-offcanvas"),this._redrawLayoutMenu()),this.update()},getLayoutMenu:()=>document.querySelector(".layout-menu"),getMenu(){const e=this.getLayoutMenu();return e?this._hasClass("menu",e)?e:e.querySelector(".menu"):null},getLayoutNavbar:()=>document.querySelector(".layout-navbar"),getLayoutFooter:()=>document.querySelector(".content-footer"),getLayoutContainer:()=>document.querySelector(".layout-page"),setNavbarFixed(e=i("fixed")){this[e?"_addClass":"_removeClass"]("layout-navbar-fixed"),this.update()},setNavbar(e){"sticky"===e?(this._addClass("layout-navbar-fixed"),this._removeClass("layout-navbar-hidden")):"hidden"===e?(this._addClass("layout-navbar-hidden"),this._removeClass("layout-navbar-fixed")):(this._removeClass("layout-navbar-hidden"),this._removeClass("layout-navbar-fixed")),this.update()},setFooterFixed(e=i("fixed")){this[e?"_addClass":"_removeClass"]("layout-footer-fixed"),this.update()},setContentLayout(e=i("contentLayout")){setTimeout((()=>{const t=document.querySelector(".content-wrapper > div"),s=document.querySelector(".layout-navbar"),i=document.querySelector(".layout-navbar > div"),n=document.querySelector(".layout-navbar .search-input-wrapper"),o=document.querySelector(".layout-navbar .search-input-wrapper .search-input"),a=document.querySelector(".content-footer > div"),l=[].slice.call(document.querySelectorAll(".container-fluid")),r=[].slice.call(document.querySelectorAll(".container-xxl")),u=document.querySelector(".menu-vertical");let c,d=!1;document.querySelector(".content-wrapper > .menu-horizontal > div")&&(d=!0,c=document.querySelector(".content-wrapper > .menu-horizontal > div")),"compact"===e?(l.some((e=>[t,a].includes(e)))&&(this._removeClass("container-fluid",[t,a]),this._addClass("container-xxl",[t,a])),o&&(this._removeClass("container-fluid",[o]),this._addClass("container-xxl",[o])),u&&l.some((e=>[s].includes(e)))&&(this._removeClass("container-fluid",[s]),this._addClass("container-xxl",[s])),d&&(this._removeClass("container-fluid",c),this._addClass("container-xxl",c),i&&(this._removeClass("container-fluid",i),this._addClass("container-xxl",i)),n&&(this._removeClass("container-fluid",n),this._addClass("container-xxl",n)))):(r.some((e=>[t,a].includes(e)))&&(this._removeClass("container-xxl",[t,a]),this._addClass("container-fluid",[t,a])),o&&(this._removeClass("container-xxl",[o]),this._addClass("container-fluid",[o])),u&&r.some((e=>[s].includes(e)))&&(this._removeClass("container-xxl",[s]),this._addClass("container-fluid",[s])),d&&(this._removeClass("container-xxl",c),this._addClass("container-fluid",c),i&&(this._removeClass("container-xxl",i),this._addClass("container-fluid",i)),n&&(this._removeClass("container-xxl",n),this._addClass("container-fluid",n))))}),100)},update(){(this.getLayoutNavbar()&&(!this.isSmallScreen()&&this.isLayoutNavbarFull()&&this.isFixed()||this.isNavbarFixed())||this.getLayoutFooter()&&this.isFooterFixed())&&this._updateInlineStyle(this._getNavbarHeight(),this._getFooterHeight()),this._bindMenuMouseEvents()},setAutoUpdate(e=i("enable")){e&&!this._autoUpdate?(this.on("resize.Helpers:autoUpdate",(()=>this.update())),this._autoUpdate=!0):!e&&this._autoUpdate&&(this.off("resize.Helpers:autoUpdate"),this._autoUpdate=!1)},updateCustomOptionCheck(e){if(e.checked){if("radio"===e.type){[].slice.call(e.closest(".row").querySelectorAll(".custom-option")).map((function(e){e.closest(".custom-option").classList.remove("checked")}))}e.closest(".custom-option").classList.add("checked")}else e.closest(".custom-option").classList.remove("checked")},isRtl:()=>"rtl"===document.querySelector("body").getAttribute("dir")||"rtl"===document.querySelector("html").getAttribute("dir"),isMobileDevice:()=>void 0!==window.orientation||-1!==navigator.userAgent.indexOf("IEMobile"),isSmallScreen(){return(window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)<this.LAYOUT_BREAKPOINT},isLayoutNavbarFull:()=>!!document.querySelector(".layout-wrapper.layout-navbar-full"),isCollapsed(){return this.isSmallScreen()?!this._hasClass("layout-menu-expanded"):this._hasClass("layout-menu-collapsed")},isFixed(){return this._hasClass("layout-menu-fixed layout-menu-fixed-offcanvas")},isOffcanvas(){return this._hasClass("layout-menu-offcanvas layout-menu-fixed-offcanvas")},isNavbarFixed(){return this._hasClass("layout-navbar-fixed")||!this.isSmallScreen()&&this.isFixed()&&this.isLayoutNavbarFull()},isFooterFixed(){return this._hasClass("layout-footer-fixed")},isLightStyle:()=>document.documentElement.classList.contains("light-style"),isDarkStyle:()=>document.documentElement.classList.contains("dark-style"),on(e=i("event"),t=i("callback")){const[s]=e.split(".");let[,...n]=e.split(".");n=n.join(".")||null,this._listeners.push({event:s,namespace:n,callback:t})},off(e=i("event")){const[t]=e.split(".");let[,...s]=e.split(".");s=s.join(".")||null,this._listeners.filter((e=>e.event===t&&e.namespace===s)).forEach((e=>this._listeners.splice(this._listeners.indexOf(e),1)))},init(){this._initialized||(this._initialized=!0,this._updateInlineStyle(0),this._bindWindowResizeEvent(),this.off("init._Helpers"),this.on("init._Helpers",(()=>{this.off("resize._Helpers:redrawMenu"),this.on("resize._Helpers:redrawMenu",(()=>{this.isSmallScreen()&&!this.isCollapsed()&&this._redrawLayoutMenu()})),"number"==typeof document.documentMode&&document.documentMode<11&&(this.off("resize._Helpers:ie10RepaintBody"),this.on("resize._Helpers:ie10RepaintBody",(()=>{if(this.isFixed())return;const{scrollTop:e}=document.documentElement;document.body.style.display="none",document.body.style.display="block",document.documentElement.scrollTop=e})))})),this._triggerEvent("init"))},destroy(){this._initialized&&(this._initialized=!1,this._removeClass("layout-transitioning"),this._removeInlineStyle(),this._unbindLayoutAnimationEndEvent(),this._unbindWindowResizeEvent(),this._unbindMenuMouseEvents(),this.setAutoUpdate(!1),this.off("init._Helpers"),this._listeners.filter((e=>"init"!==e.event)).forEach((e=>this._listeners.splice(this._listeners.indexOf(e),1))))},initPasswordToggle(){const e=document.querySelectorAll(".form-password-toggle i");null!=e&&e.forEach((e=>{e.addEventListener("click",(t=>{t.preventDefault();const s=e.closest(".form-password-toggle"),i=s.querySelector("i"),n=s.querySelector("input");"text"===n.getAttribute("type")?(n.setAttribute("type","password"),i.classList.replace("ti-eye","ti-eye-off")):"password"===n.getAttribute("type")&&(n.setAttribute("type","text"),i.classList.replace("ti-eye-off","ti-eye"))}))}))},initCustomOptionCheck(){const e=this;[].slice.call(document.querySelectorAll(".custom-option .form-check-input")).map((function(t){e.updateCustomOptionCheck(t),t.addEventListener("click",(s=>{e.updateCustomOptionCheck(t)}))}))},initSpeechToText(){const e=window.SpeechRecognition||window.webkitSpeechRecognition,t=document.querySelectorAll(".speech-to-text");if(null!=e&&null!=t){const t=new e;document.querySelectorAll(".speech-to-text i").forEach((e=>{let s=!1;e.addEventListener("click",(()=>{e.closest(".input-group").querySelector(".form-control").focus(),t.onspeechstart=()=>{s=!0},!1===s&&t.start(),t.onerror=()=>{s=!1},t.onresult=t=>{e.closest(".input-group").querySelector(".form-control").value=t.results[0][0].transcript},t.onspeechend=()=>{s=!1,t.stop()}}))}))}},initNavbarDropdownScrollbar(){const e=document.querySelectorAll(".navbar-dropdown .scrollable-container"),{PerfectScrollbar:t}=window;void 0!==t&&null!=e&&e.forEach((e=>{new t(e,{wheelPropagation:!1,suppressScrollX:!0})}))},ajaxCall:e=>new Promise(((t,s)=>{const i=new XMLHttpRequest;i.open("GET",e),i.onload=()=>200===i.status?t(i.response):s(Error(i.statusText)),i.onerror=e=>s(Error(`Network Error: ${e}`)),i.send()})),initSidebarToggle(){document.querySelectorAll('[data-bs-toggle="sidebar"]').forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-target"),s=e.getAttribute("data-overlay"),i=document.querySelectorAll(".app-overlay");document.querySelectorAll(t).forEach((e=>{e.classList.toggle("show"),null!=s&&!1!==s&&void 0!==i&&(e.classList.contains("show")?i[0].classList.add("show"):i[0].classList.remove("show"),i[0].addEventListener("click",(t=>{t.currentTarget.classList.remove("show"),e.classList.remove("show")})))}))}))}))}};"undefined"!=typeof window&&(n.init(),n.isMobileDevice()&&window.chrome&&document.documentElement.classList.add("layout-menu-100vh"),"complete"===document.readyState?n.update():document.addEventListener("DOMContentLoaded",(function e(){n.update(),document.removeEventListener("DOMContentLoaded",e)}))),window.Helpers=n;
