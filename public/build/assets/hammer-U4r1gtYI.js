var t;
/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 <PERSON><PERSON>;
 * Licensed under the MIT license */t={exports:{}},function(e,i,n,r){var s,o=["","webkit","Moz","MS","ms","o"],a=i.createElement("div"),h=Math.round,u=Math.abs,c=Date.now;function l(t,e,i){return setTimeout(T(t,i),e)}function p(t,e,i){return!!Array.isArray(t)&&(f(t,i[e],i),!0)}function f(t,e,i){var n;if(t)if(t.forEach)t.forEach(e,i);else if(t.length!==r)for(n=0;n<t.length;)e.call(i,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&e.call(i,t[n],n,t)}function v(t,i,n){var r="DEPRECATED METHOD: "+i+"\n"+n+" AT \n";return function(){var i=new Error("get-stack-trace"),n=i&&i.stack?i.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",s=e.console&&(e.console.warn||e.console.log);return s&&s.call(e.console,r,n),t.apply(this,arguments)}}s="function"!=typeof Object.assign?function(t){if(t===r||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var n=arguments[i];if(n!==r&&null!==n)for(var s in n)n.hasOwnProperty(s)&&(e[s]=n[s])}return e}:Object.assign;var d=v((function(t,e,i){for(var n=Object.keys(e),s=0;s<n.length;)(!i||i&&t[n[s]]===r)&&(t[n[s]]=e[n[s]]),s++;return t}),"extend","Use `assign`."),m=v((function(t,e){return d(t,e,!0)}),"merge","Use `assign`.");function g(t,e,i){var n,r=e.prototype;(n=t.prototype=Object.create(r)).constructor=t,n._super=r,i&&s(n,i)}function T(t,e){return function(){return t.apply(e,arguments)}}function y(t,e){return"function"==typeof t?t.apply(e&&e[0]||r,e):t}function E(t,e){return t===r?e:t}function I(t,e,i){f(S(e),(function(e){t.addEventListener(e,i,!1)}))}function A(t,e,i){f(S(e),(function(e){t.removeEventListener(e,i,!1)}))}function _(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function C(t,e){return t.indexOf(e)>-1}function S(t){return t.trim().split(/\s+/g)}function b(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var n=0;n<t.length;){if(i&&t[n][i]==e||!i&&t[n]===e)return n;n++}return-1}function P(t){return Array.prototype.slice.call(t,0)}function D(t,e,i){for(var n=[],r=[],s=0;s<t.length;){var o=t[s][e];b(r,o)<0&&n.push(t[s]),r[s]=o,s++}return n=n.sort((function(t,i){return t[e]>i[e]}))}function x(t,e){for(var i,n,s=e[0].toUpperCase()+e.slice(1),a=0;a<o.length;){if((n=(i=o[a])?i+s:e)in t)return n;a++}return r}var w=1;function O(t){var i=t.ownerDocument||t;return i.defaultView||i.parentWindow||e}var R="ontouchstart"in e,M=x(e,"PointerEvent")!==r,z=R&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),N="touch",X="mouse",Y=24,F=["x","y"],W=["clientX","clientY"];function q(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){y(t.options.enable,[t])&&i.handler(e)},this.init()}function k(t,e,i){var n=i.pointers.length,s=i.changedPointers.length,o=1&e&&n-s==0,a=12&e&&n-s==0;i.isFirst=!!o,i.isFinal=!!a,o&&(t.session={}),i.eventType=e,function(t,e){var i=t.session,n=e.pointers,s=n.length;i.firstInput||(i.firstInput=H(e)),s>1&&!i.firstMultiple?i.firstMultiple=H(e):1===s&&(i.firstMultiple=!1);var o=i.firstInput,a=i.firstMultiple,h=a?a.center:o.center,l=e.center=L(n);e.timeStamp=c(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=G(h,l),e.distance=j(h,l),function(t,e){var i=e.center,n=t.offsetDelta||{},r=t.prevDelta||{},s=t.prevInput||{};1!==e.eventType&&4!==s.eventType||(r=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},n=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=r.x+(i.x-n.x),e.deltaY=r.y+(i.y-n.y)}(i,e),e.offsetDirection=V(e.deltaX,e.deltaY);var p,f,v=U(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=v.x,e.overallVelocityY=v.y,e.overallVelocity=u(v.x)>u(v.y)?v.x:v.y,e.scale=a?(p=a.pointers,j((f=n)[0],f[1],W)/j(p[0],p[1],W)):1,e.rotation=a?function(t,e){return G(e[1],e[0],W)+G(t[1],t[0],W)}(a.pointers,n):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,n,s,o,a=t.lastInterval||e,h=e.timeStamp-a.timeStamp;if(8!=e.eventType&&(h>25||a.velocity===r)){var c=e.deltaX-a.deltaX,l=e.deltaY-a.deltaY,p=U(h,c,l);n=p.x,s=p.y,i=u(p.x)>u(p.y)?p.x:p.y,o=V(c,l),t.lastInterval=e}else i=a.velocity,n=a.velocityX,s=a.velocityY,o=a.direction;e.velocity=i,e.velocityX=n,e.velocityY=s,e.direction=o}(i,e);var d=t.element;_(e.srcEvent.target,d)&&(d=e.srcEvent.target),e.target=d}(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function H(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:h(t.pointers[i].clientX),clientY:h(t.pointers[i].clientY)},i++;return{timeStamp:c(),pointers:e,center:L(e),deltaX:t.deltaX,deltaY:t.deltaY}}function L(t){var e=t.length;if(1===e)return{x:h(t[0].clientX),y:h(t[0].clientY)};for(var i=0,n=0,r=0;r<e;)i+=t[r].clientX,n+=t[r].clientY,r++;return{x:h(i/e),y:h(n/e)}}function U(t,e,i){return{x:e/t||0,y:i/t||0}}function V(t,e){return t===e?1:u(t)>=u(e)?t<0?2:4:e<0?8:16}function j(t,e,i){i||(i=F);var n=e[i[0]]-t[i[0]],r=e[i[1]]-t[i[1]];return Math.sqrt(n*n+r*r)}function G(t,e,i){i||(i=F);var n=e[i[0]]-t[i[0]],r=e[i[1]]-t[i[1]];return 180*Math.atan2(r,n)/Math.PI}q.prototype={handler:function(){},init:function(){this.evEl&&I(this.element,this.evEl,this.domHandler),this.evTarget&&I(this.target,this.evTarget,this.domHandler),this.evWin&&I(O(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&A(this.element,this.evEl,this.domHandler),this.evTarget&&A(this.target,this.evTarget,this.domHandler),this.evWin&&A(O(this.element),this.evWin,this.domHandler)}};var Z={mousedown:1,mousemove:2,mouseup:4},B="mousedown",$="mousemove mouseup";function J(){this.evEl=B,this.evWin=$,this.pressed=!1,q.apply(this,arguments)}g(J,q,{handler:function(t){var e=Z[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:X,srcEvent:t}))}});var K={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},Q={2:N,3:"pen",4:X,5:"kinect"},tt="pointerdown",et="pointermove pointerup pointercancel";function it(){this.evEl=tt,this.evWin=et,q.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}e.MSPointerEvent&&!e.PointerEvent&&(tt="MSPointerDown",et="MSPointerMove MSPointerUp MSPointerCancel"),g(it,q,{handler:function(t){var e=this.store,i=!1,n=t.type.toLowerCase().replace("ms",""),r=K[n],s=Q[t.pointerType]||t.pointerType,o=s==N,a=b(e,t.pointerId,"pointerId");1&r&&(0===t.button||o)?a<0&&(e.push(t),a=e.length-1):12&r&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:s,srcEvent:t}),i&&e.splice(a,1))}});var nt={touchstart:1,touchmove:2,touchend:4,touchcancel:8};function rt(){this.evTarget="touchstart",this.evWin="touchstart touchmove touchend touchcancel",this.started=!1,q.apply(this,arguments)}function st(t,e){var i=P(t.touches),n=P(t.changedTouches);return 12&e&&(i=D(i.concat(n),"identifier")),[i,n]}g(rt,q,{handler:function(t){var e=nt[t.type];if(1===e&&(this.started=!0),this.started){var i=st.call(this,t,e);12&e&&i[0].length-i[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:N,srcEvent:t})}}});var ot={touchstart:1,touchmove:2,touchend:4,touchcancel:8},at="touchstart touchmove touchend touchcancel";function ht(){this.evTarget=at,this.targetIds={},q.apply(this,arguments)}function ut(t,e){var i=P(t.touches),n=this.targetIds;if(3&e&&1===i.length)return n[i[0].identifier]=!0,[i,i];var r,s,o=P(t.changedTouches),a=[],h=this.target;if(s=i.filter((function(t){return _(t.target,h)})),1===e)for(r=0;r<s.length;)n[s[r].identifier]=!0,r++;for(r=0;r<o.length;)n[o[r].identifier]&&a.push(o[r]),12&e&&delete n[o[r].identifier],r++;return a.length?[D(s.concat(a),"identifier"),a]:void 0}function ct(){q.apply(this,arguments);var t=T(this.handler,this);this.touch=new ht(this.manager,t),this.mouse=new J(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function lt(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,pt.call(this,e)):12&t&&pt.call(this,e)}function pt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY};this.lastTouches.push(i);var n=this.lastTouches;setTimeout((function(){var t=n.indexOf(i);t>-1&&n.splice(t,1)}),2500)}}function ft(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var r=this.lastTouches[n],s=Math.abs(e-r.x),o=Math.abs(i-r.y);if(s<=25&&o<=25)return!0}return!1}g(ht,q,{handler:function(t){var e=ot[t.type],i=ut.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:N,srcEvent:t})}}),g(ct,q,{handler:function(t,e,i){var n=i.pointerType==N,r=i.pointerType==X;if(!(r&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)lt.call(this,e,i);else if(r&&ft.call(this,i))return;this.callback(t,e,i)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var vt=x(a.style,"touchAction"),dt=vt!==r,mt="compute",gt="auto",Tt="manipulation",yt="none",Et="pan-x",It="pan-y",At=function(){if(!dt)return!1;var t={},i=e.CSS&&e.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){t[n]=!i||e.CSS.supports("touch-action",n)})),t}();function _t(t,e){this.manager=t,this.set(e)}_t.prototype={set:function(t){t==mt&&(t=this.compute()),dt&&this.manager.element.style&&At[t]&&(this.manager.element.style[vt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return f(this.manager.recognizers,(function(e){y(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(C(t,yt))return yt;var e=C(t,Et),i=C(t,It);return e&&i?yt:e||i?e?Et:It:C(t,Tt)?Tt:gt}(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var n=this.actions,r=C(n,yt)&&!At[yt],s=C(n,It)&&!At[It],o=C(n,Et)&&!At[Et];if(r){var a=1===t.pointers.length,h=t.distance<2,u=t.deltaTime<250;if(a&&h&&u)return}if(!o||!s)return r||s&&6&i||o&&i&Y?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var Ct=32;function St(t){this.options=s({},this.defaults,t||{}),this.id=w++,this.manager=null,this.options.enable=E(this.options.enable,!0),this.state=1,this.simultaneous={},this.requireFail=[]}function bt(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}function Pt(t){return 16==t?"down":8==t?"up":2==t?"left":4==t?"right":""}function Dt(t,e){var i=e.manager;return i?i.get(t):t}function xt(){St.apply(this,arguments)}function wt(){xt.apply(this,arguments),this.pX=null,this.pY=null}function Ot(){xt.apply(this,arguments)}function Rt(){St.apply(this,arguments),this._timer=null,this._input=null}function Mt(){xt.apply(this,arguments)}function zt(){xt.apply(this,arguments)}function Nt(){St.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function Xt(t,e){return(e=e||{}).recognizers=E(e.recognizers,Xt.defaults.preset),new Yt(t,e)}function Yt(t,e){var i;this.options=s({},Xt.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(M?it:z?ht:R?ct:J))(i,k),this.touchAction=new _t(this,this.options.touchAction),Ft(this,!0),f(this.options.recognizers,(function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}function Ft(t,e){var i,n=t.element;n.style&&(f(t.options.cssProps,(function(r,s){i=x(n.style,s),e?(t.oldCssProps[i]=n.style[i],n.style[i]=r):n.style[i]=t.oldCssProps[i]||""})),e||(t.oldCssProps={}))}St.prototype={defaults:{},set:function(t){return s(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(p(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Dt(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return p(t,"dropRecognizeWith",this)||(t=Dt(t,this),delete this.simultaneous[t.id]),this},requireFailure:function(t){if(p(t,"requireFailure",this))return this;var e=this.requireFail;return-1===b(e,t=Dt(t,this))&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(p(t,"dropRequireFailure",this))return this;t=Dt(t,this);var e=b(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,i=this.state;function n(i){e.manager.emit(i,t)}i<8&&n(e.options.event+bt(i)),n(e.options.event),t.additionalEvent&&n(t.additionalEvent),i>=8&&n(e.options.event+bt(i))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=Ct},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},recognize:function(t){var e=s({},t);if(!y(this.options.enable,[this,e]))return this.reset(),void(this.state=Ct);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},g(xt,St,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,i=t.eventType,n=6&e,r=this.attrTest(t);return n&&(8&i||!r)?16|e:n||r?4&i?8|e:2&e?4|e:2:Ct}}),g(wt,xt,{defaults:{event:"pan",threshold:10,pointers:1,direction:30},getTouchAction:function(){var t=this.options.direction,e=[];return 6&t&&e.push(It),t&Y&&e.push(Et),e},directionTest:function(t){var e=this.options,i=!0,n=t.distance,r=t.direction,s=t.deltaX,o=t.deltaY;return r&e.direction||(6&e.direction?(r=0===s?1:s<0?2:4,i=s!=this.pX,n=Math.abs(t.deltaX)):(r=0===o?1:o<0?8:16,i=o!=this.pY,n=Math.abs(t.deltaY))),t.direction=r,i&&n>e.threshold&&r&e.direction},attrTest:function(t){return xt.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Pt(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),g(Ot,xt,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[yt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||2&this.state)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),g(Rt,St,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[gt]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,r=t.deltaTime>e.time;if(this._input=t,!n||!i||12&t.eventType&&!r)this.reset();else if(1&t.eventType)this.reset(),this._timer=l((function(){this.state=8,this.tryEmit()}),e.time,this);else if(4&t.eventType)return 8;return Ct},reset:function(){clearTimeout(this._timer)},emit:function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=c(),this.manager.emit(this.options.event,this._input)))}}),g(Mt,xt,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[yt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||2&this.state)}}),g(zt,xt,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},getTouchAction:function(){return wt.prototype.getTouchAction.call(this)},attrTest:function(t){var e,i=this.options.direction;return 30&i?e=t.overallVelocity:6&i?e=t.overallVelocityX:i&Y&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&i&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&u(e)>this.options.velocity&&4&t.eventType},emit:function(t){var e=Pt(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),g(Nt,St,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Tt]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,r=t.deltaTime<e.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(n&&r&&i){if(4!=t.eventType)return this.failTimeout();var s=!this.pTime||t.timeStamp-this.pTime<e.interval,o=!this.pCenter||j(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,o&&s?this.count+=1:this.count=1,this._input=t,0==this.count%e.taps)return this.hasRequireFailures()?(this._timer=l((function(){this.state=8,this.tryEmit()}),e.interval,this),2):8}return Ct},failTimeout:function(){return this._timer=l((function(){this.state=Ct}),this.options.interval,this),Ct},reset:function(){clearTimeout(this._timer)},emit:function(){8==this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),Xt.VERSION="2.0.7",Xt.defaults={domEvents:!1,touchAction:mt,enable:!0,inputTarget:null,inputClass:null,preset:[[Mt,{enable:!1}],[Ot,{enable:!1},["rotate"]],[zt,{direction:6}],[wt,{direction:6},["swipe"]],[Nt],[Nt,{event:"doubletap",taps:2},["tap"]],[Rt]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Yt.prototype={set:function(t){return s(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?2:1},recognize:function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var n=this.recognizers,r=e.curRecognizer;(!r||r&&8&r.state)&&(r=e.curRecognizer=null);for(var s=0;s<n.length;)i=n[s],2===e.stopped||r&&i!=r&&!i.canRecognizeWith(r)?i.reset():i.recognize(t),!r&&14&i.state&&(r=e.curRecognizer=i),s++}},get:function(t){if(t instanceof St)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event==t)return e[i];return null},add:function(t){if(p(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(p(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,i=b(e,t);-1!==i&&(e.splice(i,1),this.touchAction.update())}return this},on:function(t,e){if(t!==r&&e!==r){var i=this.handlers;return f(S(t),(function(t){i[t]=i[t]||[],i[t].push(e)})),this}},off:function(t,e){if(t!==r){var i=this.handlers;return f(S(t),(function(t){e?i[t]&&i[t].splice(b(i[t],e),1):delete i[t]})),this}},emit:function(t,e){this.options.domEvents&&function(t,e){var n=i.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<n.length;)n[r](e),r++}},destroy:function(){this.element&&Ft(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},s(Xt,{INPUT_START:1,INPUT_MOVE:2,INPUT_END:4,INPUT_CANCEL:8,STATE_POSSIBLE:1,STATE_BEGAN:2,STATE_CHANGED:4,STATE_ENDED:8,STATE_RECOGNIZED:8,STATE_CANCELLED:16,STATE_FAILED:Ct,DIRECTION_NONE:1,DIRECTION_LEFT:2,DIRECTION_RIGHT:4,DIRECTION_UP:8,DIRECTION_DOWN:16,DIRECTION_HORIZONTAL:6,DIRECTION_VERTICAL:Y,DIRECTION_ALL:30,Manager:Yt,Input:q,TouchAction:_t,TouchInput:ht,MouseInput:J,PointerEventInput:it,TouchMouseInput:ct,SingleTouchInput:rt,Recognizer:St,AttrRecognizer:xt,Tap:Nt,Pan:wt,Swipe:zt,Pinch:Ot,Rotate:Mt,Press:Rt,on:I,off:A,each:f,merge:m,extend:d,assign:s,inherit:g,bindFn:T,prefixed:x}),(void 0!==e?e:"undefined"!=typeof self?self:{}).Hammer=Xt,t.exports?t.exports=Xt:e.Hammer=Xt}(window,document);
