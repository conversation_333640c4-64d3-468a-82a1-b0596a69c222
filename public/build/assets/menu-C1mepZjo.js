const t=["transitionend","webkitTransitionEnd","oTransitionEnd"];class e{constructor(t,n={},i=null){if(this._el=t,this._horizontal="horizontal"===n.orientation,this._animate=!1!==n.animate,this._accordion=!1!==n.accordion,this._showDropdownOnHover=Boolean(n.showDropdownOnHover),this._closeChildren=Boolean(n.closeChildren),this._rtl="rtl"===document.documentElement.getAttribute("dir")||"rtl"===document.body.getAttribute("dir"),this._onOpen=n.onOpen||(()=>{}),this._onOpened=n.onOpened||(()=>{}),this._onClose=n.onClose||(()=>{}),this._onClosed=n.onClosed||(()=>{}),this._psScroll=null,this._topParent=null,this._menuBgClass=null,t.classList.add("menu"),t.classList[this._animate?"remove":"add"]("menu-no-animation"),this._horizontal){t.classList.add("menu-horizontal"),t.classList.remove("menu-vertical"),this._inner=t.querySelector(".menu-inner");const e=this._inner.parentNode;this._prevBtn=t.querySelector(".menu-horizontal-prev"),this._prevBtn||(this._prevBtn=document.createElement("a"),this._prevBtn.href="#",this._prevBtn.className="menu-horizontal-prev",e.appendChild(this._prevBtn)),this._wrapper=t.querySelector(".menu-horizontal-wrapper"),this._wrapper||(this._wrapper=document.createElement("div"),this._wrapper.className="menu-horizontal-wrapper",this._wrapper.appendChild(this._inner),e.appendChild(this._wrapper)),this._nextBtn=t.querySelector(".menu-horizontal-next"),this._nextBtn||(this._nextBtn=document.createElement("a"),this._nextBtn.href="#",this._nextBtn.className="menu-horizontal-next",e.appendChild(this._nextBtn)),this._innerPosition=0,this.update()}else{t.classList.add("menu-vertical"),t.classList.remove("menu-horizontal");const n=i||window.PerfectScrollbar;n?(this._scrollbar=new n(t.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!e._hasClass("layout-menu-fixed layout-menu-fixed-offcanvas")}),window.Helpers.menuPsScroll=this._scrollbar):t.querySelector(".menu-inner").classList.add("overflow-auto")}const s=t.classList;for(let e=0;e<s.length;e++)s[e].startsWith("bg-")&&(this._menuBgClass=s[e]);t.setAttribute("data-bg-class",this._menuBgClass),this._horizontal&&window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?this.switchMenu("vertical"):this._bindEvents(),t.menuInstance=this}_bindEvents(){this._evntElClick=t=>{if(t.target.closest("ul")&&t.target.closest("ul").classList.contains("menu-inner")){const n=e._findParent(t.target,"menu-item",!1);n&&(this._topParent=n.childNodes[0])}const n=t.target.classList.contains("menu-toggle")?t.target:e._findParent(t.target,"menu-toggle",!1);n&&(t.preventDefault(),"true"!==n.getAttribute("data-hover")&&this.toggle(n))},(!this._showDropdownOnHover&&this._horizontal||!this._horizontal||window.Helpers.isMobileDevice)&&this._el.addEventListener("click",this._evntElClick),this._evntWindowResize=()=>{this.update(),this._lastWidth!==window.innerWidth&&(this._lastWidth=window.innerWidth,this.update());const t=document.querySelector("[data-template^='horizontal-menu']");this._horizontal||t||this.manageScroll()},window.addEventListener("resize",this._evntWindowResize),this._horizontal&&(this._evntPrevBtnClick=t=>{t.preventDefault(),this._prevBtn.classList.contains("disabled")||this._slide("prev")},this._prevBtn.addEventListener("click",this._evntPrevBtnClick),this._evntNextBtnClick=t=>{t.preventDefault(),this._nextBtn.classList.contains("disabled")||this._slide("next")},this._nextBtn.addEventListener("click",this._evntNextBtnClick),this._evntBodyClick=t=>{!this._inner.contains(t.target)&&this._el.querySelectorAll(".menu-inner > .menu-item.open").length&&this.closeAll()},document.body.addEventListener("click",this._evntBodyClick),this._showDropdownOnHover&&(this._evntElMouseOver=t=>{if(t.target!==t.currentTarget&&!t.target.parentNode.classList.contains("open")){const e=t.target.classList.contains("menu-toggle")?t.target:null;e&&(t.preventDefault(),"true"!==e.getAttribute("data-hover")&&this.toggle(e))}t.stopPropagation()},this._horizontal&&window.screen.width>window.Helpers.LAYOUT_BREAKPOINT&&this._el.addEventListener("mouseover",this._evntElMouseOver),this._evntElMouseOut=t=>{const n=t.currentTarget,i=t.target,s=t.toElement||t.relatedTarget;if(i.closest("ul")&&i.closest("ul").classList.contains("menu-inner")&&(this._topParent=i),i!==n&&(i.parentNode.classList.contains("open")||!i.classList.contains("menu-toggle"))&&s&&s.parentNode&&!s.parentNode.classList.contains("menu-link")){if(this._topParent&&!e.childOf(s,this._topParent.parentNode)){const e=this._topParent.classList.contains("menu-toggle")?this._topParent:null;e&&(t.preventDefault(),"true"!==e.getAttribute("data-hover")&&(this.toggle(e),this._topParent=null))}if(e.childOf(s,i.parentNode))return;const n=i.classList.contains("menu-toggle")?i:null;n&&(t.preventDefault(),"true"!==n.getAttribute("data-hover")&&this.toggle(n))}t.stopPropagation()},this._horizontal&&window.screen.width>window.Helpers.LAYOUT_BREAKPOINT&&this._el.addEventListener("mouseout",this._evntElMouseOut)))}static childOf(t,e){if(t.parentNode){for(;(t=t.parentNode)&&t!==e;);return!!t}return!1}_unbindEvents(){this._evntElClick&&(this._el.removeEventListener("click",this._evntElClick),this._evntElClick=null),this._evntElMouseOver&&(this._el.removeEventListener("mouseover",this._evntElMouseOver),this._evntElMouseOver=null),this._evntElMouseOut&&(this._el.removeEventListener("mouseout",this._evntElMouseOut),this._evntElMouseOut=null),this._evntWindowResize&&(window.removeEventListener("resize",this._evntWindowResize),this._evntWindowResize=null),this._evntBodyClick&&(document.body.removeEventListener("click",this._evntBodyClick),this._evntBodyClick=null),this._evntInnerMousemove&&(this._inner.removeEventListener("mousemove",this._evntInnerMousemove),this._evntInnerMousemove=null),this._evntInnerMouseleave&&(this._inner.removeEventListener("mouseleave",this._evntInnerMouseleave),this._evntInnerMouseleave=null)}static _isRoot(t){return!e._findParent(t,"menu-item",!1)}static _findParent(t,e,n=!0){if("BODY"===t.tagName.toUpperCase())return null;for(t=t.parentNode;"BODY"!==t.tagName.toUpperCase()&&!t.classList.contains(e);)t=t.parentNode;if(!(t="BODY"!==t.tagName.toUpperCase()?t:null)&&n)throw new Error(`Cannot find \`.${e}\` parent element`);return t}static _findChild(t,e){const n=t.childNodes,i=[];for(let s=0,o=n.length;s<o;s++)if(n[s].classList){let t=0;for(let i=0;i<e.length;i++)n[s].classList.contains(e[i])&&(t+=1);e.length===t&&i.push(n[s])}return i}static _findMenu(t){let e=t.childNodes[0],n=null;for(;e&&!n;)e.classList&&e.classList.contains("menu-sub")&&(n=e),e=e.nextSibling;if(!n)throw new Error("Cannot find `.menu-sub` element for the current `.menu-toggle`");return n}static _hasClass(t,e=window.Helpers.ROOT_EL){let n=!1;return t.split(" ").forEach((t=>{e.classList.contains(t)&&(n=!0)})),n}open(t,n=this._closeChildren){const i=this._findUnopenedParent(e._getItem(t,!0),n);if(!i)return;const s=e._getLink(i,!0);e._promisify(this._onOpen,this,i,s,e._findMenu(i)).then((()=>{this._horizontal&&e._isRoot(i)?(this._toggleDropdown(!0,i,n),this._onOpened&&this._onOpened(this,i,s,e._findMenu(i))):this._animate&&!this._horizontal?(window.requestAnimationFrame((()=>this._toggleAnimation(!0,i,!1))),this._accordion&&this._closeOther(i,n)):this._animate?(this._toggleDropdown(!0,i,n),this._onOpened&&this._onOpened(this,i,s,e._findMenu(i))):(i.classList.add("open"),this._onOpened&&this._onOpened(this,i,s,e._findMenu(i)),this._accordion&&this._closeOther(i,n))})).catch((()=>{}))}close(t,n=this._closeChildren,i=!1){const s=e._getItem(t,!0),o=e._getLink(t,!0);s.classList.contains("open")&&!s.classList.contains("disabled")&&e._promisify(this._onClose,this,s,o,e._findMenu(s),i).then((()=>{if(this._horizontal&&e._isRoot(s))this._toggleDropdown(!1,s,n),this._onClosed&&this._onClosed(this,s,o,e._findMenu(s));else if(this._animate&&!this._horizontal)window.requestAnimationFrame((()=>this._toggleAnimation(!1,s,n)));else{if(s.classList.remove("open"),n){const t=s.querySelectorAll(".menu-item.open");for(let e=0,n=t.length;e<n;e++)t[e].classList.remove("open")}this._onClosed&&this._onClosed(this,s,o,e._findMenu(s))}})).catch((()=>{}))}_closeOther(t,n){const i=e._findChild(t.parentNode,["menu-item","open"]);for(let e=0,s=i.length;e<s;e++)i[e]!==t&&this.close(i[e],n)}toggle(t,n=this._closeChildren){const i=e._getItem(t,!0);i.classList.contains("open")?this.close(i,n):this.open(i,n)}_toggleDropdown(t,n,i){const s=e._findMenu(n),o=n;let l=!1;if(t){e._findParent(n,"menu-sub",!1)&&(l=!0,n=this._topParent?this._topParent.parentNode:n);const t=Math.round(this._wrapper.getBoundingClientRect().width),r=this._innerPosition,a=this._getItemOffset(n),h=Math.round(n.getBoundingClientRect().width);a-5<=-1*r?this._innerPosition=-1*a:a+r+h+5>=t&&(this._innerPosition=h>t?-1*a:-1*(a+h-t)),o.classList.add("open");const d=Math.round(s.getBoundingClientRect().width);l?a+this._innerPosition+2*d>t&&d<t&&d>=h&&(s.style.left=[this._rtl?"100%":"-100%"]):a+this._innerPosition+d>t&&d<t&&d>h&&(s.style[this._rtl?"marginRight":"marginLeft"]=`-${d-h}px`),this._closeOther(o,i),this._updateSlider()}else{const t=e._findChild(n,["menu-toggle"]);if(t.length&&t[0].removeAttribute("data-hover","true"),n.classList.remove("open"),s.style[this._rtl?"marginRight":"marginLeft"]=null,i){const t=s.querySelectorAll(".menu-item.open");for(let e=0,n=t.length;e<n;e++)t[e].classList.remove("open")}}}_slide(t){const e=Math.round(this._wrapper.getBoundingClientRect().width),n=this._innerWidth;let i;"next"===t?(i=this._getSlideNextPos(),n+i<e&&(i=e-n)):(i=this._getSlidePrevPos(),i>0&&(i=0)),this._innerPosition=i,this.update()}_getSlideNextPos(){const t=Math.round(this._wrapper.getBoundingClientRect().width),e=this._innerPosition;let n=this._inner.childNodes[0],i=0;for(;n;){if(n.tagName){const s=Math.round(n.getBoundingClientRect().width);if(i+e-5<=t&&i+e+s+5>=t){s>t&&i===-1*e&&(i+=s);break}i+=s}n=n.nextSibling}return-1*i}_getSlidePrevPos(){const t=Math.round(this._wrapper.getBoundingClientRect().width),e=this._innerPosition;let n=this._inner.childNodes[0],i=0;for(;n;){if(n.tagName){const s=Math.round(n.getBoundingClientRect().width);if(i-5<=-1*e&&i+s+5>=-1*e){s<=t&&(i=i+s-t);break}i+=s}n=n.nextSibling}return-1*i}static _getItem(t,n){let i=null;const s=n?"menu-toggle":"menu-link";if(t.classList.contains("menu-item")?e._findChild(t,[s]).length&&(i=t):t.classList.contains(s)&&(i=t.parentNode.classList.contains("menu-item")?t.parentNode:null),!i)throw new Error((n?"Toggable ":"")+"`.menu-item` element not found.");return i}static _getLink(t,n){let i=[];const s=n?"menu-toggle":"menu-link";if(t.classList.contains(s)?i=[t]:t.classList.contains("menu-item")&&(i=e._findChild(t,[s])),!i.length)throw new Error(`\`${s}\` element not found.`);return i[0]}_findUnopenedParent(t,n){let i=[],s=null;for(;t;)t.classList.contains("disabled")?(s=null,i=[]):(t.classList.contains("open")||(s=t),i.push(t)),t=e._findParent(t,"menu-item",!1);if(!s)return null;if(1===i.length)return s;i=i.slice(0,i.indexOf(s));for(let o=0,l=i.length;o<l;o++)if(i[o].classList.add("open"),this._accordion){const t=e._findChild(i[o].parentNode,["menu-item","open"]);for(let e=0,s=t.length;e<s;e++)if(t[e]!==i[o]&&(t[e].classList.remove("open"),n)){const n=t[e].querySelectorAll(".menu-item.open");for(let t=0,e=n.length;t<e;t++)n[t].classList.remove("open")}}return s}_toggleAnimation(t,n,i){const s=e._getLink(n,!0),o=e._findMenu(n);e._unbindAnimationEndEvent(n);const l=Math.round(s.getBoundingClientRect().height);n.style.overflow="hidden";const r=()=>{n.classList.remove("menu-item-animating"),n.classList.remove("menu-item-closing"),n.style.overflow=null,n.style.height=null,this._horizontal||this.update()};t?(n.style.height=`${l}px`,n.classList.add("menu-item-animating"),n.classList.add("open"),e._bindAnimationEndEvent(n,(()=>{r(),this._onOpened(this,n,s,o)})),setTimeout((()=>{n.style.height=`${l+Math.round(o.getBoundingClientRect().height)}px`}),50)):(n.style.height=`${l+Math.round(o.getBoundingClientRect().height)}px`,n.classList.add("menu-item-animating"),n.classList.add("menu-item-closing"),e._bindAnimationEndEvent(n,(()=>{if(n.classList.remove("open"),r(),i){const t=n.querySelectorAll(".menu-item.open");for(let e=0,n=t.length;e<n;e++)t[e].classList.remove("open")}this._onClosed(this,n,s,o)})),setTimeout((()=>{n.style.height=`${l}px`}),50))}static _bindAnimationEndEvent(n,i){const s=t=>{t.target===n&&(e._unbindAnimationEndEvent(n),i(t))};let o=window.getComputedStyle(n).transitionDuration;o=parseFloat(o)*(-1!==o.indexOf("ms")?1:1e3),n._menuAnimationEndEventCb=s,t.forEach((t=>n.addEventListener(t,n._menuAnimationEndEventCb,!1))),n._menuAnimationEndEventTimeout=setTimeout((()=>{s({target:n})}),o+50)}_getItemOffset(t){let e=this._inner.childNodes[0],n=0;for(;e!==t;)e.tagName&&(n+=Math.round(e.getBoundingClientRect().width)),e=e.nextSibling;return n}_updateSlider(t=null,e=null,n=null){const i=null!==t?t:Math.round(this._wrapper.getBoundingClientRect().width),s=null!==e?e:this._innerWidth,o=null!==n?n:this._innerPosition;s<i||window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?(this._prevBtn.classList.add("d-none"),this._nextBtn.classList.add("d-none")):(this._prevBtn.classList.remove("d-none"),this._nextBtn.classList.remove("d-none")),s>i&&window.innerWidth>window.Helpers.LAYOUT_BREAKPOINT&&(0===o?this._prevBtn.classList.add("disabled"):this._prevBtn.classList.remove("disabled"),s+o<=i?this._nextBtn.classList.add("disabled"):this._nextBtn.classList.remove("disabled"))}static _promisify(t,...e){const n=t(...e);return n instanceof Promise?n:!1===n?Promise.reject():Promise.resolve()}get _innerWidth(){const t=this._inner.childNodes;let e=0;for(let n=0,i=t.length;n<i;n++)t[n].tagName&&(e+=Math.round(t[n].getBoundingClientRect().width));return e}get _innerPosition(){return parseInt(this._inner.style[this._rtl?"marginRight":"marginLeft"]||"0px",10)}set _innerPosition(t){return this._inner.style[this._rtl?"marginRight":"marginLeft"]=`${t}px`,t}static _unbindAnimationEndEvent(e){const n=e._menuAnimationEndEventCb;e._menuAnimationEndEventTimeout&&(clearTimeout(e._menuAnimationEndEventTimeout),e._menuAnimationEndEventTimeout=null),n&&(t.forEach((t=>e.removeEventListener(t,n,!1))),e._menuAnimationEndEventCb=null)}closeAll(t=this._closeChildren){const e=this._el.querySelectorAll(".menu-inner > .menu-item.open");for(let n=0,i=e.length;n<i;n++)this.close(e[n],t)}static setDisabled(t,n){e._getItem(t,!1).classList[n?"add":"remove"]("disabled")}static isActive(t){return e._getItem(t,!1).classList.contains("active")}static isOpened(t){return e._getItem(t,!1).classList.contains("open")}static isDisabled(t){return e._getItem(t,!1).classList.contains("disabled")}update(){if(this._horizontal){this.closeAll();const t=Math.round(this._wrapper.getBoundingClientRect().width),e=this._innerWidth;let n=this._innerPosition;t-n>e&&(n=t-e,n>0&&(n=0),this._innerPosition=n),this._updateSlider(t,e,n)}else this._scrollbar&&this._scrollbar.update()}manageScroll(){const{PerfectScrollbar:t}=window,e=document.querySelector(".menu-inner");if(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT)null!==this._scrollbar&&(this._scrollbar.destroy(),this._scrollbar=null),e.classList.add("overflow-auto");else{if(null===this._scrollbar){const e=new t(document.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!1});this._scrollbar=e}e.classList.remove("overflow-auto")}}switchMenu(t){this._unbindEvents();const e=document.querySelector("nav.layout-navbar"),n=document.querySelector("#navbar-collapse"),i=document.querySelector("#layout-menu div"),s=document.querySelector("#layout-menu"),o=["layout-menu-horizontal","menu","menu-horizontal","container-fluid","flex-grow-0"],l=["layout-menu","menu","menu-vertical"],r=document.querySelector(".menu-horizontal-wrapper"),a=document.querySelector(".menu-inner"),h=document.querySelector(".app-brand"),d=document.querySelector(".layout-menu-toggle"),c=document.querySelectorAll(".menu-inner .active");if("vertical"===t){this._horizontal=!1,i.insertBefore(h,r),i.insertBefore(a,r),i.classList.add("flex-column","p-0"),s.classList.remove(...s.classList),s.classList.add(...l,this._menuBgClass),h.classList.remove("d-none","d-lg-flex"),d.classList.remove("d-none"),a.classList.add("overflow-auto");for(let t=0;t<c.length-1;++t)c[t].classList.add("open")}else{this._horizontal=!0,e.children[0].insertBefore(h,n),h.classList.add("d-none","d-lg-flex"),r.appendChild(a),i.classList.remove("flex-column","p-0"),s.classList.remove(...s.classList),s.classList.add(...o,this._menuBgClass),d.classList.add("d-none"),a.classList.remove("overflow-auto");for(let t=0;t<c.length;++t)c[t].classList.remove("open")}this._bindEvents()}destroy(){if(!this._el)return;this._unbindEvents();const t=this._el.querySelectorAll(".menu-item");for(let i=0,s=t.length;i<s;i++)e._unbindAnimationEndEvent(t[i]),t[i].classList.remove("menu-item-animating"),t[i].classList.remove("open"),t[i].style.overflow=null,t[i].style.height=null;const n=this._el.querySelectorAll(".menu-menu");for(let e=0,i=n.length;e<i;e++)n[e].style.marginRight=null,n[e].style.marginLeft=null;this._el.classList.remove("menu-no-animation"),this._wrapper&&(this._prevBtn.parentNode.removeChild(this._prevBtn),this._nextBtn.parentNode.removeChild(this._nextBtn),this._wrapper.parentNode.insertBefore(this._inner,this._wrapper),this._wrapper.parentNode.removeChild(this._wrapper),this._inner.style.marginLeft=null,this._inner.style.marginRight=null),this._el.menuInstance=null,delete this._el.menuInstance,this._el=null,this._horizontal=null,this._animate=null,this._accordion=null,this._showDropdownOnHover=null,this._closeChildren=null,this._rtl=null,this._onOpen=null,this._onOpened=null,this._onClose=null,this._onClosed=null,this._scrollbar&&(this._scrollbar.destroy(),this._scrollbar=null),this._inner=null,this._prevBtn=null,this._wrapper=null,this._nextBtn=null}}window.Menu=e;
