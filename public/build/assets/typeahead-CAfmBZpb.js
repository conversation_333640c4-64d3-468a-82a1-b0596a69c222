import{c as t}from"./_commonjsHelpers-MdiGH4nz.js";import{r as e}from"./jquery-DFf3aua2.js";var n,i,r,s,o,a,u,c,h,l,f,d;
/*!
 * typeahead.js 0.11.1
 * https://github.com/twitter/typeahead.js
 * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT
 */(n={exports:{}}).exports=(i=e(),r={isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:i.isArray,isFunction:i.isFunction,isObject:i.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(t){return t instanceof i},toStr:function(t){return r.isUndefined(t)||null===t?"":t+""},bind:i.proxy,each:function(t,e){function n(t,n){return e(n,t)}i.each(t,n)},map:i.map,filter:i.grep,every:function(t,e){var n=!0;return t?(i.each(t,(function(i,r){if(!(n=e.call(null,r,i,t)))return!1})),!!n):n},some:function(t,e){var n=!1;return t?(i.each(t,(function(i,r){if(n=e.call(null,r,i,t))return!1})),!!n):n},mixin:i.extend,identity:function(t){return t},clone:function(t){return i.extend(!0,{},t)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(t){return i.isFunction(t)?t:e;function e(){return String(t)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,r;return function(){var s,o,a=this,u=arguments;return s=function(){i=null,n||(r=t.apply(a,u))},o=n&&!i,clearTimeout(i),i=setTimeout(s,e),o&&(r=t.apply(a,u)),r}},throttle:function(t,e){var n,i,r,s,o,a;return o=0,a=function(){o=new Date,r=null,s=t.apply(n,i)},function(){var u=new Date,c=e-(u-o);return n=this,i=arguments,c<=0?(clearTimeout(r),r=null,o=u,s=t.apply(n,i)):r||(r=setTimeout(a,c)),s}},stringify:function(t){return r.isString(t)?t:JSON.stringify(t)},noop:function(){}},s="0.11.1",o=function(){return{nonword:e,whitespace:t,obj:{nonword:n(e),whitespace:n(t)}};function t(t){return(t=r.toStr(t))?t.split(/\s+/):[]}function e(t){return(t=r.toStr(t))?t.split(/\W+/):[]}function n(t){return function(e){return e=r.isArray(e)?e:[].slice.call(arguments,0),function(n){var i=[];return r.each(e,(function(e){i=i.concat(t(r.toStr(n[e])))})),i}}}}(),a=function(){function t(t){this.maxSize=r.isNumber(t)?t:100,this.reset(),this.maxSize<=0&&(this.set=this.get=i.noop)}function e(){this.head=this.tail=null}function n(t,e){this.key=t,this.val=e,this.prev=this.next=null}return r.mixin(t.prototype,{set:function(t,e){var i,r=this.list.tail;this.size>=this.maxSize&&(this.list.remove(r),delete this.hash[r.key],this.size--),(i=this.hash[t])?(i.val=e,this.list.moveToFront(i)):(i=new n(t,e),this.list.add(i),this.hash[t]=i,this.size++)},get:function(t){var e=this.hash[t];if(e)return this.list.moveToFront(e),e.val},reset:function(){this.size=0,this.hash={},this.list=new e}}),r.mixin(e.prototype,{add:function(t){this.head&&(t.next=this.head,this.head.prev=t),this.head=t,this.tail=this.tail||t},remove:function(t){t.prev?t.prev.next=t.next:this.head=t.next,t.next?t.next.prev=t.prev:this.tail=t.prev},moveToFront:function(t){this.remove(t),this.add(t)}}),t}(),u=function(){var t;try{(t=window.localStorage).setItem("~~~","!"),t.removeItem("~~~")}catch(u){t=null}function e(e,n){this.prefix=["__",e,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+r.escapeRegExChars(this.prefix)),this.ls=n||t,!this.ls&&this._noop()}return r.mixin(e.prototype,{_prefix:function(t){return this.prefix+t},_ttlKey:function(t){return this._prefix(t)+this.ttlKey},_noop:function(){this.get=this.set=this.remove=this.clear=this.isExpired=r.noop},_safeSet:function(t,e){try{this.ls.setItem(t,e)}catch(u){"QuotaExceededError"===u.name&&(this.clear(),this._noop())}},get:function(t){return this.isExpired(t)&&this.remove(t),o(this.ls.getItem(this._prefix(t)))},set:function(t,e,i){return r.isNumber(i)?this._safeSet(this._ttlKey(t),s(n()+i)):this.ls.removeItem(this._ttlKey(t)),this._safeSet(this._prefix(t),s(e))},remove:function(t){return this.ls.removeItem(this._ttlKey(t)),this.ls.removeItem(this._prefix(t)),this},clear:function(){var t,e=a(this.keyMatcher);for(t=e.length;t--;)this.remove(e[t]);return this},isExpired:function(t){var e=o(this.ls.getItem(this._ttlKey(t)));return!!(r.isNumber(e)&&n()>e)}}),e;function n(){return(new Date).getTime()}function s(t){return JSON.stringify(r.isUndefined(t)?null:t)}function o(t){return i.parseJSON(t)}function a(e){var n,i,r=[],s=t.length;for(n=0;n<s;n++)(i=t.key(n)).match(e)&&r.push(i.replace(e,""));return r}}(),c=function(){var t=0,e={},n=6,s=new a(10);function o(t){t=t||{},this.cancelled=!1,this.lastReq=null,this._send=t.transport,this._get=t.limiter?t.limiter(this._get):this._get,this._cache=!1===t.cache?new a(0):s}return o.setMaxPendingRequests=function(t){n=t},o.resetCache=function(){s.reset()},r.mixin(o.prototype,{_fingerprint:function(t){return(t=t||{}).url+t.type+i.param(t.data||{})},_get:function(i,r){var s,o,a=this;function u(t){r(null,t),a._cache.set(s,t)}function c(){r(!0)}function h(){t--,delete e[s],a.onDeckRequestArgs&&(a._get.apply(a,a.onDeckRequestArgs),a.onDeckRequestArgs=null)}s=this._fingerprint(i),this.cancelled||s!==this.lastReq||((o=e[s])?o.done(u).fail(c):t<n?(t++,e[s]=this._send(i).done(u).fail(c).always(h)):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(t,e){var n,s;e=e||i.noop,t=r.isString(t)?{url:t}:t||{},s=this._fingerprint(t),this.cancelled=!1,this.lastReq=s,(n=this._cache.get(s))?e(null,n):this._get(t,e)},cancel:function(){this.cancelled=!0}}),o}(),h=window.SearchIndex=function(){var t="c",e="i";function n(t){(t=t||{}).datumTokenizer&&t.queryTokenizer||i.error("datumTokenizer and queryTokenizer are both required"),this.identify=t.identify||r.stringify,this.datumTokenizer=t.datumTokenizer,this.queryTokenizer=t.queryTokenizer,this.reset()}return r.mixin(n.prototype,{bootstrap:function(t){this.datums=t.datums,this.trie=t.trie},add:function(n){var i=this;n=r.isArray(n)?n:[n],r.each(n,(function(n){var a,u;i.datums[a=i.identify(n)]=n,u=s(i.datumTokenizer(n)),r.each(u,(function(n){var r,s,u;for(r=i.trie,s=n.split("");u=s.shift();)(r=r[t][u]||(r[t][u]=o()))[e].push(a)}))}))},get:function(t){var e=this;return r.map(t,(function(t){return e.datums[t]}))},search:function(n){var i,o,c=this;return i=s(this.queryTokenizer(n)),r.each(i,(function(n){var i,r,s,a;if(o&&0===o.length)return!1;for(i=c.trie,r=n.split("");i&&(s=r.shift());)i=i[t][s];if(!i||0!==r.length)return o=[],!1;a=i[e].slice(0),o=o?u(o,a):a})),o?r.map(a(o),(function(t){return c.datums[t]})):[]},all:function(){var t=[];for(var e in this.datums)t.push(this.datums[e]);return t},reset:function(){this.datums={},this.trie=o()},serialize:function(){return{datums:this.datums,trie:this.trie}}}),n;function s(t){return t=r.filter(t,(function(t){return!!t})),t=r.map(t,(function(t){return t.toLowerCase()}))}function o(){var n={};return n[e]=[],n[t]={},n}function a(t){for(var e={},n=[],i=0,r=t.length;i<r;i++)e[t[i]]||(e[t[i]]=!0,n.push(t[i]));return n}function u(t,e){var n=0,i=0,r=[];t=t.sort(),e=e.sort();for(var s=t.length,o=e.length;n<s&&i<o;)t[n]<e[i]?n++:(t[n]>e[i]||(r.push(t[n]),n++),i++);return r}}(),l=function(){var t;function e(t){this.url=t.url,this.ttl=t.ttl,this.cache=t.cache,this.prepare=t.prepare,this.transform=t.transform,this.transport=t.transport,this.thumbprint=t.thumbprint,this.storage=new u(t.cacheKey)}return t={data:"data",protocol:"protocol",thumbprint:"thumbprint"},r.mixin(e.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},store:function(e){this.cache&&(this.storage.set(t.data,e,this.ttl),this.storage.set(t.protocol,location.protocol,this.ttl),this.storage.set(t.thumbprint,this.thumbprint,this.ttl))},fromCache:function(){var e,n={};return this.cache?(n.data=this.storage.get(t.data),n.protocol=this.storage.get(t.protocol),n.thumbprint=this.storage.get(t.thumbprint),e=n.thumbprint!==this.thumbprint||n.protocol!==location.protocol,n.data&&!e?n.data:null):null},fromNetwork:function(t){var e,n=this;function i(){t(!0)}function r(e){t(null,n.transform(e))}t&&(e=this.prepare(this._settings()),this.transport(e).fail(i).done(r))},clear:function(){return this.storage.clear(),this}}),e}(),f=function(){function t(t){this.url=t.url,this.prepare=t.prepare,this.transform=t.transform,this.transport=new c({cache:t.cache,limiter:t.limiter,transport:t.transport})}return r.mixin(t.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},get:function(t,e){var n,i=this;if(e)return t=t||"",n=this.prepare(t,this._settings()),this.transport.get(n,r);function r(t,n){e(t?[]:i.transform(n))}},cancelLastRequest:function(){this.transport.cancel()}}),t}(),d=function(){return function(n){var s,o;return s={initialize:!0,identify:r.stringify,datumTokenizer:null,queryTokenizer:null,sufficient:5,sorter:null,local:[],prefetch:null,remote:null},!(n=r.mixin(s,n||{})).datumTokenizer&&i.error("datumTokenizer is required"),!n.queryTokenizer&&i.error("queryTokenizer is required"),o=n.sorter,n.sorter=o?function(t){return t.sort(o)}:r.identity,n.local=r.isFunction(n.local)?n.local():n.local,n.prefetch=t(n.prefetch),n.remote=e(n.remote),n};function t(t){var e;return t?(e={url:null,ttl:864e5,cache:!0,cacheKey:null,thumbprint:"",prepare:r.identity,transform:r.identity,transport:null},t=r.isString(t)?{url:t}:t,!(t=r.mixin(e,t)).url&&i.error("prefetch requires url to be set"),t.transform=t.filter||t.transform,t.cacheKey=t.cacheKey||t.url,t.thumbprint=s+t.thumbprint,t.transport=t.transport?a(t.transport):i.ajax,t):null}function e(t){var e;if(t)return e={url:null,cache:!0,prepare:null,replace:null,wildcard:null,limiter:null,rateLimitBy:"debounce",rateLimitWait:300,transform:r.identity,transport:null},t=r.isString(t)?{url:t}:t,!(t=r.mixin(e,t)).url&&i.error("remote requires url to be set"),t.transform=t.filter||t.transform,t.prepare=n(t),t.limiter=o(t),t.transport=t.transport?a(t.transport):i.ajax,delete t.replace,delete t.wildcard,delete t.rateLimitBy,delete t.rateLimitWait,t}function n(t){var e,n,i;return e=t.prepare,n=t.replace,i=t.wildcard,e||(e=n?r:t.wildcard?s:o);function r(t,e){return e.url=n(e.url,t),e}function s(t,e){return e.url=e.url.replace(i,encodeURIComponent(t)),e}function o(t,e){return e}}function o(t){var e,n,i;return e=t.limiter,n=t.rateLimitBy,i=t.rateLimitWait,e||(e=/^throttle$/i.test(n)?o(i):s(i)),e;function s(t){return function(e){return r.debounce(e,t)}}function o(t){return function(e){return r.throttle(e,t)}}}function a(t){return function(e){var n=i.Deferred();return t(e,s,o),n;function s(t){r.defer((function(){n.resolve(t)}))}function o(t){r.defer((function(){n.reject(t)}))}}}}(),function(){var t;function e(t){t=d(t),this.sorter=t.sorter,this.identify=t.identify,this.sufficient=t.sufficient,this.local=t.local,this.remote=t.remote?new f(t.remote):null,this.prefetch=t.prefetch?new l(t.prefetch):null,this.index=new h({identify:this.identify,datumTokenizer:t.datumTokenizer,queryTokenizer:t.queryTokenizer}),!1!==t.initialize&&this.initialize()}return t=window&&window.Bloodhound,e.noConflict=function(){return window&&(window.Bloodhound=t),e},e.tokenizers=o,r.mixin(e.prototype,{__ttAdapter:function(){var t=this;return this.remote?e:n;function e(e,n,i){return t.search(e,n,i)}function n(e,n){return t.search(e,n)}},_loadPrefetch:function(){var t,e,n=this;return t=i.Deferred(),this.prefetch?(e=this.prefetch.fromCache())?(this.index.bootstrap(e),t.resolve()):this.prefetch.fromNetwork(r):t.resolve(),t.promise();function r(e,i){if(e)return t.reject();n.add(i),n.prefetch.store(n.index.serialize()),t.resolve()}},_initialize:function(){var t=this;return this.clear(),(this.initPromise=this._loadPrefetch()).done(e),this.initPromise;function e(){t.add(t.local)}},initialize:function(t){return!this.initPromise||t?this._initialize():this.initPromise},add:function(t){return this.index.add(t),this},get:function(t){return t=r.isArray(t)?t:[].slice.call(arguments),this.index.get(t)},search:function(t,e,n){var i,s=this;return i=this.sorter(this.index.search(t)),e(this.remote?i.slice():i),this.remote&&i.length<this.sufficient?this.remote.get(t,o):this.remote&&this.remote.cancelLastRequest(),this;function o(t){var e=[];r.each(t,(function(t){!r.some(i,(function(e){return s.identify(t)===s.identify(e)}))&&e.push(t)})),n&&n(e)}},all:function(){return this.index.all()},clear:function(){return this.index.reset(),this},clearPrefetchCache:function(){return this.prefetch&&this.prefetch.clear(),this},clearRemoteCache:function(){return c.resetCache(),this},ttAdapter:function(){return this.__ttAdapter()}}),e}()),n.exports=function(t){var e={isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,n){function i(t,e){return n(e,t)}t.each(e,i)},map:t.map,filter:t.grep,every:function(e,n){var i=!0;return e?(t.each(e,(function(t,r){if(!(i=n.call(null,r,t,e)))return!1})),!!i):i},some:function(e,n){var i=!1;return e?(t.each(e,(function(t,r){if(i=n.call(null,r,t,e))return!1})),!!i):i},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:n;function n(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,r;return function(){var s,o,a=this,u=arguments;return s=function(){i=null,n||(r=t.apply(a,u))},o=n&&!i,clearTimeout(i),i=setTimeout(s,e),o&&(r=t.apply(a,u)),r}},throttle:function(t,e){var n,i,r,s,o,a;return o=0,a=function(){o=new Date,r=null,s=t.apply(n,i)},function(){var u=new Date,c=e-(u-o);return n=this,i=arguments,c<=0?(clearTimeout(r),r=null,o=u,s=t.apply(n,i)):r||(r=setTimeout(a,c)),s}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}},n=function(){var t={wrapper:"twitter-typeahead",input:"tt-input",hint:"tt-hint",menu:"tt-menu",dataset:"tt-dataset",suggestion:"tt-suggestion",selectable:"tt-selectable",empty:"tt-empty",open:"tt-open",cursor:"tt-cursor",highlight:"tt-highlight"};return n;function n(n){var o,a;return a=e.mixin({},t,n),{css:(o={css:s(),classes:a,html:i(a),selectors:r(a)}).css,html:o.html,classes:o.classes,selectors:o.selectors,mixin:function(t){e.mixin(t,o)}}}function i(t){return{wrapper:'<span class="'+t.wrapper+'"></span>',menu:'<div class="'+t.menu+'"></div>'}}function r(t){var n={};return e.each(t,(function(t,e){n[e]="."+t})),n}function s(){var t={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},menu:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return e.isMsie()&&e.mixin(t.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),t}}(),i=function(){var n,i;function r(e){e&&e.el||t.error("EventBus initialized without el"),this.$el=t(e.el)}return n="typeahead:",i={render:"rendered",cursorchange:"cursorchanged",select:"selected",autocomplete:"autocompleted"},e.mixin(r.prototype,{_trigger:function(e,i){var r;return r=t.Event(n+e),(i=i||[]).unshift(r),this.$el.trigger.apply(this.$el,i),r},before:function(t){var e;return e=[].slice.call(arguments,1),this._trigger("before"+t,e).isDefaultPrevented()},trigger:function(t){var e;this._trigger(t,[].slice.call(arguments,1)),(e=i[t])&&this._trigger(e,[].slice.call(arguments,1))}}),r}(),r=function(){var t=/\s+/,e=u();return{onSync:r,onAsync:i,off:s,trigger:o};function n(e,n,i,r){var s;if(!i)return this;for(n=n.split(t),i=r?c(i,r):i,this._callbacks=this._callbacks||{};s=n.shift();)this._callbacks[s]=this._callbacks[s]||{sync:[],async:[]},this._callbacks[s][e].push(i);return this}function i(t,e,i){return n.call(this,"async",t,e,i)}function r(t,e,i){return n.call(this,"sync",t,e,i)}function s(e){var n;if(!this._callbacks)return this;for(e=e.split(t);n=e.shift();)delete this._callbacks[n];return this}function o(n){var i,r,s,o,u;if(!this._callbacks)return this;for(n=n.split(t),s=[].slice.call(arguments,1);(i=n.shift())&&(r=this._callbacks[i]);)o=a(r.sync,this,[i].concat(s)),u=a(r.async,this,[i].concat(s)),o()&&e(u);return this}function a(t,e,n){return i;function i(){for(var i,r=0,s=t.length;!i&&r<s;r+=1)i=!1===t[r].apply(e,n);return!i}}function u(){return window.setImmediate?function(t){setImmediate((function(){t()}))}:function(t){setTimeout((function(){t()}),0)}}function c(t,e){return t.bind?t.bind(e):function(){t.apply(e,[].slice.call(arguments,0))}}}(),s=function(t){var n={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(r){var s;function o(e){var n,i,o;return(n=s.exec(e.data))&&(o=t.createElement(r.tagName),r.className&&(o.className=r.className),(i=e.splitText(n.index)).splitText(n[0].length),o.appendChild(i.cloneNode(!0)),e.parentNode.replaceChild(o,i)),!!n}function a(t,e){for(var n,i=3,r=0;r<t.childNodes.length;r++)(n=t.childNodes[r]).nodeType===i?r+=e(n)?1:0:a(n,e)}(r=e.mixin({},n,r)).node&&r.pattern&&(r.pattern=e.isArray(r.pattern)?r.pattern:[r.pattern],s=i(r.pattern,r.caseSensitive,r.wordsOnly),a(r.node,o))};function i(t,n,i){for(var r,s=[],o=0,a=t.length;o<a;o++)s.push(e.escapeRegExChars(t[o]));return r=i?"\\b("+s.join("|")+")\\b":"("+s.join("|")+")",n?new RegExp(r):new RegExp(r,"i")}}(window.document),o=function(){var n;function i(n,i){(n=n||{}).input||t.error("input is missing"),i.mixin(this),this.$hint=t(n.hint),this.$input=t(n.input),this.query=this.$input.val(),this.queryWhenFocused=this.hasFocus()?this.query:null,this.$overflowHelper=s(this.$input),this._checkLanguageDirection(),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=e.noop)}return n={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},i.normalizeQuery=function(t){return e.toStr(t).replace(/^\s*/g,"").replace(/\s{2,}/g," ")},e.mixin(i.prototype,r,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.queryWhenFocused=this.query,this.trigger("focused")},_onKeydown:function(t){var e=n[t.which||t.keyCode];this._managePreventDefault(e,t),e&&this._shouldTrigger(e,t)&&this.trigger(e+"Keyed",t)},_onInput:function(){this._setQuery(this.getInputValue()),this.clearHintIfInvalid(),this._checkLanguageDirection()},_managePreventDefault:function(t,e){var n;switch(t){case"up":case"down":n=!a(e);break;default:n=!1}n&&e.preventDefault()},_shouldTrigger:function(t,e){return"tab"!==t||!a(e)},_checkLanguageDirection:function(){var t=(this.$input.css("direction")||"ltr").toLowerCase();this.dir!==t&&(this.dir=t,this.$hint.attr("dir",t),this.trigger("langDirChanged",t))},_setQuery:function(t,e){var n,i;i=!!(n=o(t,this.query))&&this.query.length!==t.length,this.query=t,e||n?!e&&i&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},bind:function(){var t,i,r,s,o=this;return t=e.bind(this._onBlur,this),i=e.bind(this._onFocus,this),r=e.bind(this._onKeydown,this),s=e.bind(this._onInput,this),this.$input.on("blur.tt",t).on("focus.tt",i).on("keydown.tt",r),!e.isMsie()||e.isMsie()>9?this.$input.on("input.tt",s):this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",(function(t){n[t.which||t.keyCode]||e.defer(e.bind(o._onInput,o,t))})),this},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getLangDir:function(){return this.dir},getQuery:function(){return this.query||""},setQuery:function(t,e){this.setInputValue(t),this._setQuery(t,e)},hasQueryChangedSinceLastFocus:function(){return this.query!==this.queryWhenFocused},getInputValue:function(){return this.$input.val()},setInputValue:function(t){this.$input.val(t),this.clearHintIfInvalid(),this._checkLanguageDirection()},resetInputValue:function(){this.setInputValue(this.query)},getHint:function(){return this.$hint.val()},setHint:function(t){this.$hint.val(t)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var t,e,n;n=(t=this.getInputValue())!==(e=this.getHint())&&0===e.indexOf(t),(""===t||!n||this.hasOverflow())&&this.clearHint()},hasFocus:function(){return this.$input.is(":focus")},hasOverflow:function(){var t=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=t},isCursorAtEnd:function(){var t,n,i;return t=this.$input.val().length,n=this.$input[0].selectionStart,e.isNumber(n)?n===t:!document.selection||((i=document.selection.createRange()).moveStart("character",-t),t===i.text.length)},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$overflowHelper.remove(),this.$hint=this.$input=this.$overflowHelper=t("<div>")}}),i;function s(e){return t('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:e.css("font-family"),fontSize:e.css("font-size"),fontStyle:e.css("font-style"),fontVariant:e.css("font-variant"),fontWeight:e.css("font-weight"),wordSpacing:e.css("word-spacing"),letterSpacing:e.css("letter-spacing"),textIndent:e.css("text-indent"),textRendering:e.css("text-rendering"),textTransform:e.css("text-transform")}).insertAfter(e)}function o(t,e){return i.normalizeQuery(t)===i.normalizeQuery(e)}function a(t){return t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}}(),a=function(){var n,i;function o(n,r){(n=n||{}).templates=n.templates||{},n.templates.notFound=n.templates.notFound||n.templates.empty,n.source||t.error("missing source"),n.node||t.error("missing node"),n.name&&!c(n.name)&&t.error("invalid dataset name: "+n.name),r.mixin(this),this.highlight=!!n.highlight,this.name=n.name||i(),this.limit=n.limit||5,this.displayFn=a(n.display||n.displayKey),this.templates=u(n.templates,this.displayFn),this.source=n.source.__ttAdapter?n.source.__ttAdapter():n.source,this.async=e.isUndefined(n.async)?this.source.length>2:!!n.async,this._resetLastSuggestion(),this.$el=t(n.node).addClass(this.classes.dataset).addClass(this.classes.dataset+"-"+this.name)}return n={val:"tt-selectable-display",obj:"tt-selectable-object"},i=e.getIdGenerator(),o.extractData=function(e){var i=t(e);return i.data(n.obj)?{val:i.data(n.val)||"",obj:i.data(n.obj)||null}:null},e.mixin(o.prototype,r,{_overwrite:function(t,e){(e=e||[]).length?this._renderSuggestions(t,e):this.async&&this.templates.pending?this._renderPending(t):!this.async&&this.templates.notFound?this._renderNotFound(t):this._empty(),this.trigger("rendered",this.name,e,!1)},_append:function(t,e){(e=e||[]).length&&this.$lastSuggestion.length?this._appendSuggestions(t,e):e.length?this._renderSuggestions(t,e):!this.$lastSuggestion.length&&this.templates.notFound&&this._renderNotFound(t),this.trigger("rendered",this.name,e,!0)},_renderSuggestions:function(t,e){var n;n=this._getSuggestionsFragment(t,e),this.$lastSuggestion=n.children().last(),this.$el.html(n).prepend(this._getHeader(t,e)).append(this._getFooter(t,e))},_appendSuggestions:function(t,e){var n,i;i=(n=this._getSuggestionsFragment(t,e)).children().last(),this.$lastSuggestion.after(n),this.$lastSuggestion=i},_renderPending:function(t){var e=this.templates.pending;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_renderNotFound:function(t){var e=this.templates.notFound;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_empty:function(){this.$el.empty(),this._resetLastSuggestion()},_getSuggestionsFragment:function(i,r){var o,a=this;return o=document.createDocumentFragment(),e.each(r,(function(e){var r,s;s=a._injectQuery(i,e),r=t(a.templates.suggestion(s)).data(n.obj,e).data(n.val,a.displayFn(e)).addClass(a.classes.suggestion+" "+a.classes.selectable),o.appendChild(r[0])})),this.highlight&&s({className:this.classes.highlight,node:o,pattern:i}),t(o)},_getFooter:function(t,e){return this.templates.footer?this.templates.footer({query:t,suggestions:e,dataset:this.name}):null},_getHeader:function(t,e){return this.templates.header?this.templates.header({query:t,suggestions:e,dataset:this.name}):null},_resetLastSuggestion:function(){this.$lastSuggestion=t()},_injectQuery:function(t,n){return e.isObject(n)?e.mixin({_query:t},n):n},update:function(e){var n=this,i=!1,r=!1,s=0;function o(t){r||(r=!0,t=(t||[]).slice(0,n.limit),s=t.length,n._overwrite(e,t),s<n.limit&&n.async&&n.trigger("asyncRequested",e))}function a(r){r=r||[],!i&&s<n.limit&&(n.cancel=t.noop,s+=r.length,n._append(e,r.slice(0,n.limit-s)),n.async&&n.trigger("asyncReceived",e))}this.cancel(),this.cancel=function(){i=!0,n.cancel=t.noop,n.async&&n.trigger("asyncCanceled",e)},this.source(e,o,a),!r&&o([])},cancel:t.noop,clear:function(){this._empty(),this.cancel(),this.trigger("cleared")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=t("<div>")}}),o;function a(t){return t=t||e.stringify,e.isFunction(t)?t:n;function n(e){return e[t]}}function u(n,i){return{notFound:n.notFound&&e.templatify(n.notFound),pending:n.pending&&e.templatify(n.pending),header:n.header&&e.templatify(n.header),footer:n.footer&&e.templatify(n.footer),suggestion:n.suggestion||r};function r(e){return t("<div>").text(i(e))}}function c(t){return/^[_a-zA-Z0-9-]+$/.test(t)}}(),u=function(){function n(n,i){var r=this;function s(e){var n=r.$node.find(e.node).first();return e.node=n.length?n:t("<div>").appendTo(r.$node),new a(e,i)}(n=n||{}).node||t.error("node is required"),i.mixin(this),this.$node=t(n.node),this.query=null,this.datasets=e.map(n.datasets,s)}return e.mixin(n.prototype,r,{_onSelectableClick:function(e){this.trigger("selectableClicked",t(e.currentTarget))},_onRendered:function(t,e,n,i){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetRendered",e,n,i)},_onCleared:function(){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetCleared")},_propagate:function(){this.trigger.apply(this,arguments)},_allDatasetsEmpty:function(){return e.every(this.datasets,t);function t(t){return t.isEmpty()}},_getSelectables:function(){return this.$node.find(this.selectors.selectable)},_removeCursor:function(){var t=this.getActiveSelectable();t&&t.removeClass(this.classes.cursor)},_ensureVisible:function(t){var e,n,i,r;n=(e=t.position().top)+t.outerHeight(!0),i=this.$node.scrollTop(),r=this.$node.height()+parseInt(this.$node.css("paddingTop"),10)+parseInt(this.$node.css("paddingBottom"),10),e<0?this.$node.scrollTop(i+e):r<n&&this.$node.scrollTop(i+(n-r))},bind:function(){var t,n=this;return t=e.bind(this._onSelectableClick,this),this.$node.on("click.tt",this.selectors.selectable,t),e.each(this.datasets,(function(t){t.onSync("asyncRequested",n._propagate,n).onSync("asyncCanceled",n._propagate,n).onSync("asyncReceived",n._propagate,n).onSync("rendered",n._onRendered,n).onSync("cleared",n._onCleared,n)})),this},isOpen:function(){return this.$node.hasClass(this.classes.open)},open:function(){this.$node.addClass(this.classes.open)},close:function(){this.$node.removeClass(this.classes.open),this._removeCursor()},setLanguageDirection:function(t){this.$node.attr("dir",t)},selectableRelativeToCursor:function(t){var e,n,i;return n=this.getActiveSelectable(),e=this._getSelectables(),-1===(i=(i=((i=(n?e.index(n):-1)+t)+1)%(e.length+1)-1)<-1?e.length-1:i)?null:e.eq(i)},setCursor:function(t){this._removeCursor(),(t=t&&t.first())&&(t.addClass(this.classes.cursor),this._ensureVisible(t))},getSelectableData:function(t){return t&&t.length?a.extractData(t):null},getActiveSelectable:function(){var t=this._getSelectables().filter(this.selectors.cursor).first();return t.length?t:null},getTopSelectable:function(){var t=this._getSelectables().first();return t.length?t:null},update:function(t){var n=t!==this.query;return n&&(this.query=t,e.each(this.datasets,i)),n;function i(e){e.update(t)}},empty:function(){function t(t){t.clear()}e.each(this.datasets,t),this.query=null,this.$node.addClass(this.classes.empty)},destroy:function(){function n(t){t.destroy()}this.$node.off(".tt"),this.$node=t("<div>"),e.each(this.datasets,n)}}),n}(),c=function(){var t=u.prototype;function n(){u.apply(this,[].slice.call(arguments,0))}return e.mixin(n.prototype,u.prototype,{open:function(){return!this._allDatasetsEmpty()&&this._show(),t.open.apply(this,[].slice.call(arguments,0))},close:function(){return this._hide(),t.close.apply(this,[].slice.call(arguments,0))},_onRendered:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onRendered.apply(this,[].slice.call(arguments,0))},_onCleared:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onCleared.apply(this,[].slice.call(arguments,0))},setLanguageDirection:function(e){return this.$node.css("ltr"===e?this.css.ltr:this.css.rtl),t.setLanguageDirection.apply(this,[].slice.call(arguments,0))},_hide:function(){this.$node.hide()},_show:function(){this.$node.css("display","block")}}),n}(),h=function(){function n(n,r){var s,o,a,u,c,h,l,f,d,p,g;(n=n||{}).input||t.error("missing input"),n.menu||t.error("missing menu"),n.eventBus||t.error("missing event bus"),r.mixin(this),this.eventBus=n.eventBus,this.minLength=e.isNumber(n.minLength)?n.minLength:1,this.input=n.input,this.menu=n.menu,this.enabled=!0,this.active=!1,this.input.hasFocus()&&this.activate(),this.dir=this.input.getLangDir(),this._hacks(),this.menu.bind().onSync("selectableClicked",this._onSelectableClicked,this).onSync("asyncRequested",this._onAsyncRequested,this).onSync("asyncCanceled",this._onAsyncCanceled,this).onSync("asyncReceived",this._onAsyncReceived,this).onSync("datasetRendered",this._onDatasetRendered,this).onSync("datasetCleared",this._onDatasetCleared,this),s=i(this,"activate","open","_onFocused"),o=i(this,"deactivate","_onBlurred"),a=i(this,"isActive","isOpen","_onEnterKeyed"),u=i(this,"isActive","isOpen","_onTabKeyed"),c=i(this,"isActive","_onEscKeyed"),h=i(this,"isActive","open","_onUpKeyed"),l=i(this,"isActive","open","_onDownKeyed"),f=i(this,"isActive","isOpen","_onLeftKeyed"),d=i(this,"isActive","isOpen","_onRightKeyed"),p=i(this,"_openIfActive","_onQueryChanged"),g=i(this,"_openIfActive","_onWhitespaceChanged"),this.input.bind().onSync("focused",s,this).onSync("blurred",o,this).onSync("enterKeyed",a,this).onSync("tabKeyed",u,this).onSync("escKeyed",c,this).onSync("upKeyed",h,this).onSync("downKeyed",l,this).onSync("leftKeyed",f,this).onSync("rightKeyed",d,this).onSync("queryChanged",p,this).onSync("whitespaceChanged",g,this).onSync("langDirChanged",this._onLangDirChanged,this)}return e.mixin(n.prototype,{_hacks:function(){var n,i;n=this.input.$input||t("<div>"),i=this.menu.$node||t("<div>"),n.on("blur.tt",(function(t){var r,s,o;r=document.activeElement,s=i.is(r),o=i.has(r).length>0,e.isMsie()&&(s||o)&&(t.preventDefault(),t.stopImmediatePropagation(),e.defer((function(){n.focus()})))})),i.on("mousedown.tt",(function(t){t.preventDefault()}))},_onSelectableClicked:function(t,e){this.select(e)},_onDatasetCleared:function(){this._updateHint()},_onDatasetRendered:function(t,e,n,i){this._updateHint(),this.eventBus.trigger("render",n,i,e)},_onAsyncRequested:function(t,e,n){this.eventBus.trigger("asyncrequest",n,e)},_onAsyncCanceled:function(t,e,n){this.eventBus.trigger("asynccancel",n,e)},_onAsyncReceived:function(t,e,n){this.eventBus.trigger("asyncreceive",n,e)},_onFocused:function(){this._minLengthMet()&&this.menu.update(this.input.getQuery())},_onBlurred:function(){this.input.hasQueryChangedSinceLastFocus()&&this.eventBus.trigger("change",this.input.getQuery())},_onEnterKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())&&this.select(n)&&e.preventDefault()},_onTabKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())?this.select(n)&&e.preventDefault():(n=this.menu.getTopSelectable())&&this.autocomplete(n)&&e.preventDefault()},_onEscKeyed:function(){this.close()},_onUpKeyed:function(){this.moveCursor(-1)},_onDownKeyed:function(){this.moveCursor(1)},_onLeftKeyed:function(){"rtl"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onRightKeyed:function(){"ltr"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onQueryChanged:function(t,e){this._minLengthMet(e)?this.menu.update(e):this.menu.empty()},_onWhitespaceChanged:function(){this._updateHint()},_onLangDirChanged:function(t,e){this.dir!==e&&(this.dir=e,this.menu.setLanguageDirection(e))},_openIfActive:function(){this.isActive()&&this.open()},_minLengthMet:function(t){return(t=e.isString(t)?t:this.input.getQuery()||"").length>=this.minLength},_updateHint:function(){var t,n,i,r,s,a;t=this.menu.getTopSelectable(),n=this.menu.getSelectableData(t),i=this.input.getInputValue(),!n||e.isBlankString(i)||this.input.hasOverflow()?this.input.clearHint():(r=o.normalizeQuery(i),s=e.escapeRegExChars(r),(a=new RegExp("^(?:"+s+")(.+$)","i").exec(n.val))&&this.input.setHint(i+a[1]))},isEnabled:function(){return this.enabled},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},isActive:function(){return this.active},activate:function(){return!!this.isActive()||!(!this.isEnabled()||this.eventBus.before("active"))&&(this.active=!0,this.eventBus.trigger("active"),!0)},deactivate:function(){return!this.isActive()||!this.eventBus.before("idle")&&(this.active=!1,this.close(),this.eventBus.trigger("idle"),!0)},isOpen:function(){return this.menu.isOpen()},open:function(){return this.isOpen()||this.eventBus.before("open")||(this.menu.open(),this._updateHint(),this.eventBus.trigger("open")),this.isOpen()},close:function(){return this.isOpen()&&!this.eventBus.before("close")&&(this.menu.close(),this.input.clearHint(),this.input.resetInputValue(),this.eventBus.trigger("close")),!this.isOpen()},setVal:function(t){this.input.setQuery(e.toStr(t))},getVal:function(){return this.input.getQuery()},select:function(t){var e=this.menu.getSelectableData(t);return!(!e||this.eventBus.before("select",e.obj)||(this.input.setQuery(e.val,!0),this.eventBus.trigger("select",e.obj),this.close(),0))},autocomplete:function(t){var e,n;return e=this.input.getQuery(),!(!(n=this.menu.getSelectableData(t))||e===n.val||this.eventBus.before("autocomplete",n.obj)||(this.input.setQuery(n.val),this.eventBus.trigger("autocomplete",n.obj),0))},moveCursor:function(t){var e,n,i,r;return e=this.input.getQuery(),n=this.menu.selectableRelativeToCursor(t),r=(i=this.menu.getSelectableData(n))?i.obj:null,!(this._minLengthMet()&&this.menu.update(e)||this.eventBus.before("cursorchange",r)||(this.menu.setCursor(n),i?this.input.setInputValue(i.val):(this.input.resetInputValue(),this._updateHint()),this.eventBus.trigger("cursorchange",r),0))},destroy:function(){this.input.destroy(),this.menu.destroy()}}),n;function i(t){var n=[].slice.call(arguments,1);return function(){var i=[].slice.call(arguments);e.each(n,(function(e){return t[e].apply(t,i)}))}}}();!function(){var r,s,a;function l(e,n){e.each((function(){var e,i=t(this);(e=i.data(s.typeahead))&&n(e,i)}))}function f(t,e){return t.clone().addClass(e.classes.hint).removeData().css(e.css.hint).css(p(t)).prop("readonly",!0).removeAttr("id name placeholder required").attr({autocomplete:"off",spellcheck:"false",tabindex:-1})}function d(t,e){t.data(s.attrs,{dir:t.attr("dir"),autocomplete:t.attr("autocomplete"),spellcheck:t.attr("spellcheck"),style:t.attr("style")}),t.addClass(e.classes.input).attr({autocomplete:"off",spellcheck:!1});try{!t.attr("dir")&&t.attr("dir","auto")}catch(n){}return t}function p(t){return{backgroundAttachment:t.css("background-attachment"),backgroundClip:t.css("background-clip"),backgroundColor:t.css("background-color"),backgroundImage:t.css("background-image"),backgroundOrigin:t.css("background-origin"),backgroundPosition:t.css("background-position"),backgroundRepeat:t.css("background-repeat"),backgroundSize:t.css("background-size")}}function g(t){var n,i;n=t.data(s.www),i=t.parent().filter(n.selectors.wrapper),e.each(t.data(s.attrs),(function(n,i){e.isUndefined(n)?t.removeAttr(i):t.attr(i,n)})),t.removeData(s.typeahead).removeData(s.www).removeData(s.attr).removeClass(n.classes.input),i.length&&(t.detach().insertAfter(i),i.remove())}function m(n){var i;return(i=e.isJQuery(n)||e.isElement(n)?t(n).first():[]).length?i:null}r=t.fn.typeahead,s={www:"tt-www",attrs:"tt-attrs",typeahead:"tt-typeahead"},a={initialize:function(r,a){var l;return a=e.isArray(a)?a:[].slice.call(arguments,1),l=n((r=r||{}).classNames),this.each(p);function p(){var n,p,g,y,v,_,b,w,S,x,A;e.each(a,(function(t){t.highlight=!!r.highlight})),n=t(this),p=t(l.html.wrapper),g=m(r.hint),y=m(r.menu),v=!1!==r.hint&&!g,_=!1!==r.menu&&!y,v&&(g=f(n,l)),_&&(y=t(l.html.menu).css(l.css.menu)),g&&g.val(""),n=d(n,l),(v||_)&&(p.css(l.css.wrapper),n.css(v?l.css.input:l.css.inputWithNoHint),n.wrap(p).parent().prepend(v?g:null).append(_?y:null)),A=_?c:u,b=new i({el:n}),w=new o({hint:g,input:n},l),S=new A({node:y,datasets:a},l),x=new h({input:w,menu:S,eventBus:b,minLength:r.minLength},l),n.data(s.www,l),n.data(s.typeahead,x)}},isEnabled:function(){var t;return l(this.first(),(function(e){t=e.isEnabled()})),t},enable:function(){return l(this,(function(t){t.enable()})),this},disable:function(){return l(this,(function(t){t.disable()})),this},isActive:function(){var t;return l(this.first(),(function(e){t=e.isActive()})),t},activate:function(){return l(this,(function(t){t.activate()})),this},deactivate:function(){return l(this,(function(t){t.deactivate()})),this},isOpen:function(){var t;return l(this.first(),(function(e){t=e.isOpen()})),t},open:function(){return l(this,(function(t){t.open()})),this},close:function(){return l(this,(function(t){t.close()})),this},select:function(e){var n=!1,i=t(e);return l(this.first(),(function(t){n=t.select(i)})),n},autocomplete:function(e){var n=!1,i=t(e);return l(this.first(),(function(t){n=t.autocomplete(i)})),n},moveCursor:function(t){var e=!1;return l(this.first(),(function(n){e=n.moveCursor(t)})),e},val:function(t){var e;return arguments.length?(l(this,(function(e){e.setVal(t)})),this):(l(this.first(),(function(t){e=t.getVal()})),e)},destroy:function(){return l(this,(function(t,e){g(e),t.destroy()})),this}},t.fn.typeahead=function(t){return a[t]?a[t].apply(this,[].slice.call(arguments,1)):a.initialize.apply(this,arguments)},t.fn.typeahead.noConflict=function(){return t.fn.typeahead=r,this}}()}(e());
