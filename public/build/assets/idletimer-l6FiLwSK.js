/*! Idle Timer - v1.1.1 - 2020-06-25
 * https://github.com/thorst/jquery-idletimer
 * Copyright (c) 2020 Paul Irish; Licensed MIT */
var e;(e=jQuery).idleTimer=function(i,t){var n;"object"==typeof i?(n=i,i=null):"number"==typeof i&&(n={timeout:i},i=null),t=t||document,n=e.extend({idle:!1,timeout:3e4,events:"mousemove keydown wheel DOMMouseScroll mousewheel mousedown touchstart touchmove MSPointerDown MSPointerMove"},n);var r,a=e(t),l=a.data("idleTimerObj")||{},d=function(i){var n=e.data(t,"idleTimerObj")||{};n.idle=!n.idle,n.olddate=+new Date;var r=e.Event((n.idle?"idle":"active")+".idleTimer");e(t).trigger(r,[t,e.extend({},n),i])},o=function(i){var n=e.data(t,"idleTimerObj")||{};if(("storage"!==i.type||i.originalEvent.key===n.timerSyncId)&&null==n.remaining){if("mousemove"===i.type){if(i.pageX===n.pageX&&i.pageY===n.pageY)return;if(void 0===i.pageX&&void 0===i.pageY)return;if(+new Date-n.olddate<200)return}clearTimeout(n.tId),n.idle&&d(i),n.lastActive=+new Date,n.pageX=i.pageX,n.pageY=i.pageY,"storage"!==i.type&&n.timerSyncId&&"undefined"!=typeof localStorage&&localStorage.setItem(n.timerSyncId,n.lastActive),n.tId=setTimeout(d,n.timeout)}},u=function(){var i=e.data(t,"idleTimerObj")||{};i.idle=i.idleBackup,i.olddate=+new Date,i.lastActive=i.olddate,i.remaining=null,clearTimeout(i.tId),i.idle||(i.tId=setTimeout(d,i.timeout))};if(null===i&&void 0!==l.idle)return u(),a;if(null===i);else{if(null!==i&&void 0===l.idle)return!1;if("destroy"===i)return r=e.data(t,"idleTimerObj")||{},clearTimeout(r.tId),a.removeData("idleTimerObj"),a.off("._idleTimer"),a;if("pause"===i)return function(){var i=e.data(t,"idleTimerObj")||{};null==i.remaining&&(i.remaining=i.timeout-(+new Date-i.olddate),clearTimeout(i.tId))}(),a;if("resume"===i)return function(){var i=e.data(t,"idleTimerObj")||{};null!=i.remaining&&(i.idle||(i.tId=setTimeout(d,i.remaining)),i.remaining=null)}(),a;if("reset"===i)return u(),a;if("getRemainingTime"===i)return function(){var i=e.data(t,"idleTimerObj")||{};if(i.idle)return 0;if(null!=i.remaining)return i.remaining;var n=i.timeout-(+new Date-i.lastActive);return n<0&&(n=0),n}();if("getElapsedTime"===i)return+new Date-l.olddate;if("getLastActiveTime"===i)return l.lastActive;if("isIdle"===i)return l.idle}return a.on((n.events+" ").split(" ").join("._idleTimer ").trim(),(function(e){o(e)})),n.timerSyncId&&e(window).on("storage",o),(l=e.extend({},{olddate:+new Date,lastActive:+new Date,idle:n.idle,idleBackup:n.idle,timeout:n.timeout,remaining:null,timerSyncId:n.timerSyncId,tId:null,pageX:null,pageY:null})).idle||(l.tId=setTimeout(d,l.timeout)),e.data(t,"idleTimerObj",l),a},e.fn.idleTimer=function(i){return this[0]?e.idleTimer(i,this[0]):this};
