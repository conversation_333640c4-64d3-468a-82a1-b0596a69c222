var t,e,n,i,r,u,a,c,o,f,s,p,l,h,d,v,m,g,y,b,$,k,x,T,w,C,V,E,j;t=jQuery,$=function(t){return t},k=function(e){return t.isArray(e)},x=function(t){return!k(t)&&t instanceof Object},T=function(e,n){return t.inArray(n,e)},w=function(t,e){for(var n in t)t.hasOwnProperty(n)&&e(t[n],n,t)},C=function(t){return t[t.length-1]},V=function(){var t,e={};return w((t=arguments,Array.prototype.slice.call(t)),(function(t){w(t,(function(t,n){e[n]=t}))})),e},E=function(t,e,n){return k(t)?function(t,e){var n=[];return w(t,(function(t,i,r){n.push(e(t,i,r))})),n}(t,e):function(t,e,n){var i={};return w(t,(function(t,r,u){r=n?n(r,t):r,i[r]=e(t,r,u)})),i}(t,e,n)},j=function(t,e,n){return E(t,(function(t,n){return t[e].apply(t,[])}))},e=jQuery,n=function(t,e){var n,i,r=(i={},(n=n||{}).publish=function(t,e){w(i[t],(function(t){t(e)}))},n.subscribe=function(t,e){i[t]=i[t]||[],i[t].push(e)},n.unsubscribe=function(t){w(i,(function(e){var n=T(e,t);-1!==n&&e.splice(n,1)}))},n),u=t.$;return r.getType=function(){throw'implement me (return type. "text", "radio", etc.)'},r.$=function(t){return t?u.find(t):u},r.disable=function(){r.$().prop("disabled",!0),r.publish("isEnabled",!1)},r.enable=function(){r.$().prop("disabled",!1),r.publish("isEnabled",!0)},e.equalTo=function(t,e){return t===e},e.publishChange=function(){var t;return function(n,i){var u=r.get();e.equalTo(u,t)||r.publish("change",{e:n,domElement:i}),t=u}}(),r},i=function(t,e){var i=n(t,e);return i.get=function(){return i.$().val()},i.set=function(t){i.$().val(t)},i.clear=function(){i.set("")},e.buildSetter=function(t){return function(e){t.call(i,e)}},i},r=function(t,e){t=k(t)?t:[t],e=k(e)?e:[e];var n=!0;return t.length!==e.length?n=!1:w(t,(function(t){(function(t,e){return-1!==T(t,e)})(e,t)||(n=!1)})),n},u=function(t){var e={},n=i(t,e);return n.getType=function(){return"button"},n.$().on("change",(function(t){e.publishChange(t,this)})),n},a=function(t){var n={},u=i(t,n);return u.getType=function(){return"checkbox"},u.get=function(){var t=[];return u.$().filter(":checked").each((function(){t.push(e(this).val())})),t},u.set=function(t){t=k(t)?t:[t],u.$().each((function(){e(this).prop("checked",!1)})),w(t,(function(t){u.$().filter('[value="'+t+'"]').prop("checked",!0)}))},n.equalTo=r,u.$().change((function(t){n.publishChange(t,this)})),u},c=function(t){var e=m(t);return e.getType=function(){return"email"},e},o=function(t){var i={},r=n(t,i);return r.getType=function(){return"file"},r.get=function(){return C(r.$().val().split("\\"))},r.clear=function(){this.$().each((function(){e(this).wrap("<form>").closest("form").get(0).reset(),e(this).unwrap()}))},r.$().change((function(t){i.publishChange(t,this)})),r},f=function(t){var e={},n=i(t,e);return n.getType=function(){return"hidden"},n.$().change((function(t){e.publishChange(t,this)})),n},s=function(t){var i={},r=n(t,i);return r.getType=function(){return"file[multiple]"},r.get=function(){var t,e=r.$().get(0).files||[],n=[];for(t=0;t<(e.length||0);t+=1)n.push(e[t].name);return n},r.clear=function(){this.$().each((function(){e(this).wrap("<form>").closest("form").get(0).reset(),e(this).unwrap()}))},r.$().change((function(t){i.publishChange(t,this)})),r},p=function(t){var e={},n=i(t,e);return n.getType=function(){return"select[multiple]"},n.get=function(){return n.$().val()||[]},n.set=function(t){n.$().val(""===t?[]:k(t)?t:[t])},e.equalTo=r,n.$().change((function(t){e.publishChange(t,this)})),n},l=function(t){var e=m(t);return e.getType=function(){return"password"},e},h=function(t){var n={},r=i(t,n);return r.getType=function(){return"radio"},r.get=function(){return r.$().filter(":checked").val()||null},r.set=function(t){t?r.$().filter('[value="'+t+'"]').prop("checked",!0):r.$().each((function(){e(this).prop("checked",!1)}))},r.$().change((function(t){n.publishChange(t,this)})),r},d=function(t){var e={},n=i(t,e);return n.getType=function(){return"range"},n.$().change((function(t){e.publishChange(t,this)})),n},v=function(t){var e={},n=i(t,e);return n.getType=function(){return"select"},n.$().change((function(t){e.publishChange(t,this)})),n},m=function(t){var e={},n=i(t,e);return n.getType=function(){return"text"},n.$().on("change keyup keydown",(function(t){e.publishChange(t,this)})),n},g=function(t){var e={},n=i(t,e);return n.getType=function(){return"textarea"},n.$().on("change keyup keydown",(function(t){e.publishChange(t,this)})),n},y=function(t){var e=m(t);return e.getType=function(){return"url"},e},b=function(t){var n={},i=t.$,r=t.constructorOverride||{button:u,text:m,url:y,email:c,password:l,range:d,textarea:g,select:v,"select[multiple]":p,radio:h,checkbox:a,file:o,"file[multiple]":s,hidden:f},b=function(t,u){(x(u)?u:i.find(u)).each((function(){var i=e(this).attr("name");n[i]=r[t]({$:e(this)})}))},$=function(t,u){var a=[],c=x(u)?u:i.find(u);x(u)?n[c.attr("name")]=r[t]({$:c}):(c.each((function(){-1===T(a,e(this).attr("name"))&&a.push(e(this).attr("name"))})),w(a,(function(e){n[e]=r[t]({$:i.find('input[name="'+e+'"]')})})))};return i.is("input, select, textarea")?i.is('input[type="button"], button, input[type="submit"]')?b("button",i):i.is("textarea")?b("textarea",i):i.is('input[type="text"]')||i.is("input")&&!i.attr("type")?b("text",i):i.is('input[type="password"]')?b("password",i):i.is('input[type="email"]')?b("email",i):i.is('input[type="url"]')?b("url",i):i.is('input[type="range"]')?b("range",i):i.is("select")?i.is("[multiple]")?b("select[multiple]",i):b("select",i):i.is('input[type="file"]')?i.is("[multiple]")?b("file[multiple]",i):b("file",i):i.is('input[type="hidden"]')?b("hidden",i):i.is('input[type="radio"]')?$("radio",i):i.is('input[type="checkbox"]')?$("checkbox",i):b("text",i):(b("button",'input[type="button"], button, input[type="submit"]'),b("text",'input[type="text"]'),b("password",'input[type="password"]'),b("email",'input[type="email"]'),b("url",'input[type="url"]'),b("range",'input[type="range"]'),b("textarea","textarea"),b("select","select:not([multiple])"),b("select[multiple]","select[multiple]"),b("file",'input[type="file"]:not([multiple])'),b("file[multiple]",'input[type="file"][multiple]'),b("hidden",'input[type="hidden"]'),$("radio",'input[type="radio"]'),$("checkbox",'input[type="checkbox"]')),n},e.fn.inputVal=function(t){var n=e(this),i=b({$:n});return n.is("input, textarea, select")?void 0===t?i[n.attr("name")].get():(i[n.attr("name")].set(t),n):void 0===t?j(i,"get"):(w(t,(function(t,e){i[e].set(t)})),n)},e.fn.inputOnChange=function(t){var n=e(this),i=b({$:n});return w(i,(function(e){e.subscribe("change",(function(e){t.call(e.domElement,e.e)}))})),n},e.fn.inputDisable=function(){var t=e(this);return j(b({$:t}),"disable"),t},e.fn.inputEnable=function(){var t=e(this);return j(b({$:t}),"enable"),t},e.fn.inputClear=function(){var t=e(this);return j(b({$:t}),"clear"),t},t.fn.repeaterVal=function(){var e,n,i=function(t){if(1===t.length&&(0===t[0].key.length||1===t[0].key.length&&!t[0].key[0]))return t[0].val;w(t,(function(t){t.head=t.key.shift()}));var e,n,r=(e={},w(t,(function(t){e[t.head]||(e[t.head]=[]),e[t.head].push(t)})),e);return/^[0-9]+$/.test(t[0].head)?(n=[],w(r,(function(t){n.push(i(t))}))):(n={},w(r,(function(t,e){n[e]=i(t)}))),n};return i((e=t(this).inputVal(),n=[],w(e,(function(t,e){var i=[];"undefined"!==e&&(i.push(e.match(/^[^\[]*/)[0]),i=i.concat(E(e.match(/\[[^\]]*\]/g),(function(t){return t.replace(/[\[\]]/g,"")}))),n.push({val:t,key:i}))})),n))},t.fn.repeater=function(e){var n;return e=e||{},t(this).each((function(){var i=t(this),r=e.show||function(){t(this).show()},u=e.hide||function(t){t()},a=i.find("[data-repeater-list]").first(),c=function(e,n){return e.filter((function(){return!n||0===t(this).closest((e=n,i="selector",E(e,(function(t){return t[i]}))).join(",")).length;var e,i}))},o=function(){return c(a.find("[data-repeater-item]"),e.repeaters)},f=a.find("[data-repeater-item]").first().clone().hide(),s=c(c(t(this).find("[data-repeater-item]"),e.repeaters).first().find("[data-repeater-delete]"),e.repeaters);e.isFirstItemUndeletable&&s&&s.remove();var p=function(){var t=a.data("repeater-list");return e.$parent?e.$parent.data("item-name")+"["+t+"]":t},l=function(n){e.repeaters&&n.each((function(){var n=t(this);w(e.repeaters,(function(t){n.find(t.selector).repeater(V(t,{$parent:n}))}))}))},h=function(t,e,n){t&&w(t,(function(t){n.call(e.find(t.selector)[0],t)}))},d=function(e,n,i){e.each((function(e){var r=t(this);r.data("item-name",n+"["+e+"]"),c(r.find("[name]"),i).each((function(){var u=t(this),a=u.attr("name").match(/\[[^\]]+\]/g),o=a?C(a).replace(/\[|\]/g,""):u.attr("name"),f=n+"["+e+"]["+o+"]"+(u.is(":checkbox")||u.attr("multiple")?"[]":"");u.attr("name",f),h(i,r,(function(i){var r=t(this);d(c(r.find("[data-repeater-item]"),i.repeaters||[]),n+"["+e+"]["+r.find("[data-repeater-list]").first().data("repeater-list")+"]",i.repeaters)}))}))})),a.find("input[name][checked]").removeAttr("checked").prop("checked",!0)};d(o(),p(),e.repeaters),l(o()),e.initEmpty&&o().remove(),e.ready&&e.ready((function(){d(o(),p(),e.repeaters)}));var v=function(){var n=function(i,r,u){if(r||e.defaultValues){var a={};c(i.find("[name]"),u).each((function(){var e=t(this).attr("name").match(/\[([^\]]*)(\]|\]\[\])$/)[1];a[e]=t(this).attr("name")})),i.inputVal(E((o=r||e.defaultValues,f=function(t,e){return a[e]},k(o)?(s=[],w(o,(function(t,e,n){f(t,e,n)&&s.push(t)}))):(s={},w(o,(function(t,e,n){f(t,e,n)&&(s[e]=t)}))),s),$,(function(t){return a[t]})))}var o,f,s;h(u,i,(function(e){var i=t(this);c(i.find("[data-repeater-item]"),e.repeaters).each((function(){var u=i.find("[data-repeater-list]").data("repeater-list");if(r&&r[u]){var a=t(this).clone();i.find("[data-repeater-item]").remove(),w(r[u],(function(t){var r=a.clone();n(r,t,e.repeaters||[]),i.find("[data-repeater-list]").append(r)}))}else n(t(this),e.defaultValues,e.repeaters||[])}))}))};return function(i,r){a.append(i),d(o(),p(),e.repeaters),i.find("[name]").each((function(){t(this).inputClear()})),n(i,r||e.defaultValues,e.repeaters)}}(),m=function(t){var n=f.clone();v(n,t),e.repeaters&&l(n),r.call(n.get(0))};n=function(t){o().remove(),w(t,m)},c(i.find("[data-repeater-create]"),e.repeaters).click((function(){m()})),a.on("click","[data-repeater-delete]",(function(){var n=t(this).closest("[data-repeater-item]").get(0);u.call(n,(function(){t(n).remove(),d(o(),p(),e.repeaters)}))}))})),this.setList=n,this};
