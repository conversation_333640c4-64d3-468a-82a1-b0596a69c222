!function(){let o,r;isDarkStyle?(config.colors_dark.cardColor,r=config.colors_dark.textMuted,config.colors_dark.bodyColor,o=config.colors_dark.headingColor):(config.colors.cardColor,r=config.colors.textMuted,config.colors.bodyColor,o=config.colors.headingColor);const e=document.querySelectorAll(".chart-progress");e&&e.forEach((function(r){const e=function(r,e,a){return{chart:{height:"true"==a?58:48,width:"true"==a?58:38,type:"radialBar"},plotOptions:{radialBar:{hollow:{size:"true"==a?"50%":"25%"},dataLabels:{show:"true"==a,value:{offsetY:-10,fontSize:"15px",fontWeight:500,fontFamily:"Public Sans",color:o}},track:{background:config.colors_label.secondary}}},stroke:{lineCap:"round"},colors:[r],grid:{padding:{top:"true"==a?-12:-15,bottom:"true"==a?-17:-15,left:"true"==a?-17:-5,right:-15}},series:[e],labels:"true"==a?[""]:["Progress"]}}(config.colors[r.dataset.color],r.dataset.series,r.dataset.progress_variant?r.dataset.progress_variant:"false");new ApexCharts(r,e).render()}));const a=document.querySelector("#reportBarChart"),t={chart:{height:200,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{barHeight:"60%",columnWidth:"60%",startingShape:"rounded",endingShape:"rounded",borderRadius:4,distributed:!0}},grid:{show:!1,padding:{top:-20,bottom:0,left:-10,right:-10}},colors:[config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors.primary,config.colors_label.primary,config.colors_label.primary],dataLabels:{enabled:!1},series:[{data:[40,95,60,45,90,50,75]}],legend:{show:!1},xaxis:{categories:["Mo","Tu","We","Th","Fr","Sa","Su"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:r,fontSize:"13px"}}},yaxis:{labels:{show:!1}}};if(void 0!==typeof a&&null!==a){new ApexCharts(a,t).render()}const l=document.querySelector("#swiper-with-pagination-cards");l&&new Swiper(l,{loop:!0,autoplay:{delay:2500,disableOnInteraction:!1},pagination:{clickable:!0,el:".swiper-pagination"}})}();
