import{r as t}from"./jquery-DFf3aua2.js";import"./_commonjsHelpers-MdiGH4nz.js";var e,i;i=function(t){var e=Array.prototype.slice,i=Array.prototype.splice,r={topSpacing:0,bottomSpacing:0,className:"is-sticky",wrapperClassName:"sticky-wrapper",center:!1,getWidthFrom:"",widthFromWrapper:!0,responsiveWidth:!1,zIndex:"auto"},s=t(window),n=t(document),o=[],c=s.height(),p=function(){for(var e=s.scrollTop(),i=n.height(),r=i-c,p=e>r?r-e:0,a=0,l=o.length;a<l;a++){var d=o[a],h=d.stickyWrapper.offset().top-d.topSpacing-p;if(d.stickyWrapper.css("height",d.stickyElement.outerHeight()),e<=h)null!==d.currentTop&&(d.stickyElement.css({width:"",position:"",top:"","z-index":""}),d.stickyElement.parent().removeClass(d.className),d.stickyElement.trigger("sticky-end",[d]),d.currentTop=null);else{var u,g=i-d.stickyElement.outerHeight()-d.topSpacing-d.bottomSpacing-e-p;g<0?g+=d.topSpacing:g=d.topSpacing,d.currentTop!==g&&(d.getWidthFrom?u=t(d.getWidthFrom).width()||null:d.widthFromWrapper&&(u=d.stickyWrapper.width()),null==u&&(u=d.stickyElement.width()),d.stickyElement.css("width",u).css("position","fixed").css("top",g).css("z-index",d.zIndex),d.stickyElement.parent().addClass(d.className),null===d.currentTop?d.stickyElement.trigger("sticky-start",[d]):d.stickyElement.trigger("sticky-update",[d]),d.currentTop===d.topSpacing&&d.currentTop>g||null===d.currentTop&&g<d.topSpacing?d.stickyElement.trigger("sticky-bottom-reached",[d]):null!==d.currentTop&&g===d.topSpacing&&d.currentTop<g&&d.stickyElement.trigger("sticky-bottom-unreached",[d]),d.currentTop=g);var m=d.stickyWrapper.parent();d.stickyElement.offset().top+d.stickyElement.outerHeight()>=m.offset().top+m.outerHeight()&&d.stickyElement.offset().top<=d.topSpacing?d.stickyElement.css("position","absolute").css("top","").css("bottom",0).css("z-index",""):d.stickyElement.css("position","fixed").css("top",g).css("bottom","").css("z-index",d.zIndex)}}},a=function(){c=s.height();for(var e=0,i=o.length;e<i;e++){var r=o[e],n=null;r.getWidthFrom?r.responsiveWidth&&(n=t(r.getWidthFrom).width()):r.widthFromWrapper&&(n=r.stickyWrapper.width()),null!=n&&r.stickyElement.css("width",n)}},l={init:function(e){var i=t.extend({},r,e);return this.each((function(){var e=t(this),s=e.attr("id"),n=s?s+"-"+r.wrapperClassName:r.wrapperClassName,c=t("<div></div>").attr("id",n).addClass(i.wrapperClassName);e.wrapAll(c);var p=e.parent();i.center&&p.css({width:e.outerWidth(),marginLeft:"auto",marginRight:"auto"}),"right"===e.css("float")&&e.css({float:"none"}).parent().css({float:"right"}),i.stickyElement=e,i.stickyWrapper=p,i.currentTop=null,o.push(i),l.setWrapperHeight(this),l.setupChangeListeners(this)}))},setWrapperHeight:function(e){var i=t(e),r=i.parent();r&&r.css("height",i.outerHeight())},setupChangeListeners:function(t){window.MutationObserver?new window.MutationObserver((function(e){(e[0].addedNodes.length||e[0].removedNodes.length)&&l.setWrapperHeight(t)})).observe(t,{subtree:!0,childList:!0}):(t.addEventListener("DOMNodeInserted",(function(){l.setWrapperHeight(t)}),!1),t.addEventListener("DOMNodeRemoved",(function(){l.setWrapperHeight(t)}),!1))},update:p,unstick:function(e){return this.each((function(){for(var e=t(this),r=-1,s=o.length;s-- >0;)o[s].stickyElement.get(0)===this&&(i.call(o,s,1),r=s);-1!==r&&(e.unwrap(),e.css({width:"",position:"",top:"",float:"","z-index":""}))}))}};window.addEventListener?(window.addEventListener("scroll",p,!1),window.addEventListener("resize",a,!1)):window.attachEvent&&(window.attachEvent("onscroll",p),window.attachEvent("onresize",a)),t.fn.sticky=function(i){return l[i]?l[i].apply(this,e.call(arguments,1)):"object"!=typeof i&&i?void t.error("Method "+i+" does not exist on jQuery.sticky"):l.init.apply(this,arguments)},t.fn.unstick=function(i){return l[i]?l[i].apply(this,e.call(arguments,1)):"object"!=typeof i&&i?void t.error("Method "+i+" does not exist on jQuery.sticky"):l.unstick.apply(this,arguments)},t((function(){setTimeout(p,0)}))},(e={exports:{}}).exports?e.exports=i(t()):i(jQuery);
