!function(){const e=document.querySelector("#menu-1"),o=document.querySelector("#menu-1-toggle-collapsed");e&&new <PERSON>u(e),o&&(o.onclick=function(){e.classList.toggle("menu-collapsed")});const n=document.querySelector("#menu-2");n&&new Menu(n,{orientation:"horizontal"});const c=document.querySelector("#menu-3");c&&new Menu(c,{orientation:"horizontal",showDropdownOnHover:!0});const t=document.querySelector("#menu-5"),l=document.querySelector("#menu-5-toggle-collapsed");t&&new Menu(t,{animate:!1}),l&&(l.onclick=function(){t.classList.toggle("menu-collapsed")});const u=document.querySelector("#menu-6");u&&new <PERSON>u(u,{orientation:"horizontal",animate:!1,closeChildren:!0});const r=document.querySelector("#menu-7"),s=document.querySelector("#menu-7-toggle-collapsed");r&&new <PERSON>u(r,{accordion:!1}),s&&(s.onclick=function(){r.classList.toggle("menu-collapsed")});const m=document.querySelector("#menu-8");m&&new Menu(m,{orientation:"horizontal",accordion:!1});const a=document.querySelectorAll(".menus-9"),i=document.querySelector("#menus-9-toggle-collapsed");a&&a.forEach((e=>{new Menu(e)})),i&&(i.onclick=function(){a.forEach((e=>{e.classList.toggle("menu-collapsed")}))});const d=document.querySelectorAll(".menus-10"),g=document.querySelector("#menus-10-toggle-collapsed");d&&d.forEach((e=>{new Menu(e)})),g&&(g.onclick=function(){d.forEach((e=>{e.classList.toggle("menu-collapsed")}))});const q=document.querySelectorAll(".menus-11");q&&q.forEach((e=>{new Menu(e,{orientation:"horizontal"})}));const y=document.querySelectorAll(".menus-12"),S=document.querySelector("#menus-12-toggle-collapsed");y&&y.forEach((e=>{new Menu(e)})),S&&(S.onclick=function(){y.forEach((e=>{e.classList.toggle("menu-collapsed")}))})}();
