<?php

namespace App\Livewire;

use App\Models\Author;
use App\Models\Category;
use App\RolesEnum;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuthorsFilterComponent extends Component
{
    use WithPagination;

    protected $paginationTheme = 'tailwind';

    // Search and filter parameters
    public $query = '';
    public $categories = [];
    public $yearFrom = '';
    public $yearTo = '';
    public $sort = 'name_asc';
    
    // For loading state
    public $isLoading = false;
    
    // For displaying counts
    public $authorsCount = 0;
    
    // Admin status
    protected $isAdmin = false;

    // Listen for sidebar filter changes
    protected $listeners = ['filterUpdated' => '$refresh'];
    
    public function mount()
    {
        Log::info('mount');
        $this->isAdmin = Auth::check() && (Auth::user()->role == RolesEnum::ADMIN || Auth::user()->role == RolesEnum::SUPER_ADMIN);
        
        if ($this->isAdmin) {
            $this->authorsCount = Author::count();
        } else {
            $this->authorsCount = Author::whereHas('books', function($q) {
                $q->where('website', 1);
            })->count();
        }
    }

    // Reset pagination when filters change
    public function updatedQuery()
    {
        Log::info('updatedQuery');
        $this->resetPage();
    }

    public function updatedCategories()
    {
        Log::info('updatedCategories');
        $this->resetPage();
    }

    public function updatedYearFrom()
    {
        Log::info('updatedYearFrom');
        $this->resetPage();
    }

    public function updatedYearTo()
    {
        Log::info('updatedYearTo');
        $this->resetPage();
    }

    public function updatedSort()
    {
        Log::info('updatedSort');
        $this->resetPage();
    }
    
    // Manual refresh method that can be called from the view
    public function refresh()
    {
        Log::info('manual refresh called');
    }

    // Get filtered authors
    public function getFilteredAuthors()
    {
        $query = Author::withCount('books');
        Log::info('getFilteredAuthors');

        // Apply visibility filter for non-admin users to only see authors with visible books
        if (!$this->isAdmin) {
            $query->whereHas('books', function($q) {
                $q->where('website', 1);
            });
        }
        
        // Apply search filter
        if (!empty($this->query)) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->query . '%')
                  ->orWhere('nickname', 'like', '%' . $this->query . '%');
            });
        }
        
        // Apply category filter (authors who have books in these categories)
        if (!empty($this->categories)) {
            $query->whereHas('books', function($q) {
                $q->whereHas('categories', function($sq) {
                    $sq->whereIn('categories.id', $this->categories);
                });
            });
        }
        
        // Apply year range filter (author death date)
        if (!empty($this->yearFrom)) {
            $query->where('death_date', '>=', $this->yearFrom);
        }
        
        if (!empty($this->yearTo)) {
            $query->where('death_date', '<=', $this->yearTo);
        }
        
        // Apply sorting
        switch ($this->sort) {
            case 'name_asc':
                $query->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $query->orderBy('name', 'desc');
                break;
            case 'death_date_asc':
                $query->orderBy('death_date', 'asc');
                break;
            case 'death_date_desc':
                $query->orderBy('death_date', 'desc');
                break;
            case 'books_count_asc':
                $query->orderBy('books_count', 'asc');
                break;
            case 'books_count_desc':
                $query->orderBy('books_count', 'desc');
                break;
            default:
                $query->orderBy('name', 'asc');
        }
        
        return $query->paginate(18);
    }

    // Get all categories for sidebar
    public function getCategories()
    {
        $query = Category::withCount('books')->orderBy('title');
        
        // Filter for non-admin users
        if (!$this->isAdmin) {
            $query->whereHas('books', function($q) {
                $q->where('website', 1);
            });
        }
        
        return $query->get();
    }

    public function render()
    {
        Log::info('render');
        $this->isLoading = true;
        
        $authors = $this->getFilteredAuthors();
        $allCategories = $this->getCategories();
        $this->authorsCount = $authors->total();
        
        $this->isLoading = false;
        
        return view('livewire.authors-filter-component', [
            'authors' => $authors,
            'allCategories' => $allCategories,
            'authorsCount' => $this->authorsCount
        ]);
    }
} 