<?php

namespace App\Livewire;

use App\Models\PopularSearch;
use Illuminate\Support\Facades\Request;

class PopularSearchesTable extends DataTable
{
    public $updateModalView = 'modules.popular-search.modal-update';
    public $featuredFilter = null;
    public $activeFilter = null;

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن البحث الشائع بالكلمة المفتاحية';
        $this->featuredFilter = Request::query('featured');
        $this->activeFilter = Request::query('active');
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'كلمة البحث', 'type' => 'string', 'field' => 'search_term'],
            ['title' => 'عدد النقرات', 'type' => 'number', 'field' => 'click_count'],
            ['title' => 'مميز', 'type' => 'boolean', 'field' => 'is_featured', 'renderType' => 'featured'],
            ['title' => 'نشط', 'type' => 'boolean', 'field' => 'is_active', 'renderType' => 'active'],
            ['title' => 'تاريخ الإنشاء', 'type' => 'date', 'field' => 'created_at'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['delete', 'edit'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }

    public function render()
    {
        $query = PopularSearch::query();
        
        // Apply search
        if (!empty($this->search)) {
            $query->where('search_term', 'like', '%' . $this->search . '%');
        }
        
        // Apply filters
        if ($this->featuredFilter !== null) {
            $query->where('is_featured', $this->featuredFilter);
        }
        
        if ($this->activeFilter !== null) {
            $query->where('is_active', $this->activeFilter);
        }
        
        // Get the data with sorting and pagination
        $popularSearches = $query
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);

        return view('components.table.common-table', [
            'data' => $popularSearches,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
            'updateModalView' => $this->updateModalView,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            if ($column['field'] === 'created_at') {
                return $item->created_at ? $item->created_at->format('Y-m-d H:i:s') : '';
            }
            return $item->{$column['field']};
        }

        switch ($column['renderType']) {
            case 'featured':
                return $item->is_featured 
                    ? '<span class="badge bg-success">نعم</span>' 
                    : '<span class="badge bg-secondary">لا</span>';
                
            case 'active':
                return $item->is_active 
                    ? '<span class="badge bg-success">نعم</span>' 
                    : '<span class="badge bg-secondary">لا</span>';
                
            default:
                return $item->{$column['field']};
        }
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'popular-search';
        $models = 'popular-searches';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف البحث الشائع "' . $item->search_term . '"؟';
        $item['delete'] = route('popular-searches.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => 'javascript:void(0)',
                'onclick' => "openEditModal(" . json_encode($item) . ")",
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}
