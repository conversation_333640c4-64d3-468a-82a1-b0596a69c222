<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Book;

class BookInfoDialog extends Component
{
    public $bookId;
    public $book;
    public $showDialog = false;

    protected $listeners = ['openBookInfoDialog' => 'loadBook'];

    public function loadBook($bookId)
    {
        $this->bookId = $bookId;
        $this->book = Book::with(['author', 'categories', 'files'])->find($bookId);
        $this->showDialog = true;
    }

    public function closeDialog()
    {
        $this->showDialog = false;
    }

    public function render()
    {
        return view('livewire.book-info-dialog');
    }
}