<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\BookSearch;

class SearchFts extends Component
{
    public $searchTerm = '';
    public $searchType = 'default';
    public $autoCompleteResults = [];
    public $showDebug = false;
    public $showResultsModal = false;

    protected $listeners = ['focusSearch'];

    public function mount()
    {
        $this->autoCompleteResults = [];
    }

    public function search()
    {
        if (!empty($this->searchTerm) && strlen($this->searchTerm) > 2) {
            $this->showResultsModal = true;
        }
    }

    public function closeModal()
    {
        $this->showResultsModal = false;
    }

    public function updateSearchTerm()
    {
            dd($this->searchTerm);
            $this->updatedSearchTerm();
    }
  
    public function updatedSearchTerm()
    {
        if (!empty($this->searchTerm) && strlen($this->searchTerm) > 2) {
            $this->autoCompleteResults = BookSearch::search($this->searchTerm, $this->searchType);
        } else {
            $this->autoCompleteResults = [];
        }
    }

    public function updated($propertyName)
    {
        if ($propertyName === 'searchTerm') {
            $this->updatedSearchTerm();
        }
    }

    public function updatedQuery()
    {
    }

    public function selectResult($term)
    {
        $this->searchTerm = $term;
        $this->search();
    }

    public function focusSearch()
    {
        $this->dispatchBrowserEvent('focus-search');
    }

    public function render()
    {
        $results = [];
        $debugInfo = null;
        
        if (!empty($this->searchTerm)) {
            $results = BookSearch::search($this->searchTerm, $this->searchType);
            
            // Debug information if enabled
            if ($this->showDebug && count($results) > 0) {
                $debugInfo = [
                    'attributes' => get_object_vars($results[0]),
                    'class' => get_class($results[0])
                ];
            }
        }
        
        return view('livewire.search-fts', [
            'results' => $results,
            'debugInfo' => $debugInfo
        ]);
    }
}
