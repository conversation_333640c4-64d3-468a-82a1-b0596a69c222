<?php

namespace App\Livewire;

use App\Models\Book;
use Carbon\Carbon;
use Illuminate\Support\Facades\Request;

class BooksTable extends DataTable
{
    public $category;
    public $author;
    public $websiteFilter = false;
    public $applicationFilter = false;
    public $privateUsersFilter = false;
    public $publishedFilter = false;
    public $sqliteFilter = false;
    public $noSqliteFilter = false;
    public $pdfFilter = false;
    public $noPdfFilter = false;
    public $pdfAndUnpublished = false;
    public $analyticsData = [];
    public $selectedAnalyticsFilter = null;


    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن الكتاب بالعنوان أو المؤلف';
        $this->category = Request::query('category');
        $this->author = Request::query('author');
        $this->initializeAnalyticsData();

        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id', 'style' => ['field' => 'private_users', 'value' => 'not_null', 'color' => '#ff0000']],
            ['title' => 'العنوان', 'type' => 'string', 'field' => 'title'],
            ['title' => 'التصنيفات', 'type' => 'string', 'field' => 'categories', 'sortable' => false, 'renderType' => 'categories'],
            ['title' => 'المؤلف', 'type' => 'string', 'field' => 'author.name', 'sortable' => false, 'renderType' => 'author.name'],
            ['title' => 'التحميلات (sqlite)', 'type' => 'number', 'field' => 'download_count', 'renderType' => 'sqliteDownloads', 'sortable' => false],
            ['title' => 'التحميلات (pdf)', 'type' => 'number', 'field' => 'pdf_downloads', 'renderType' => 'pdfDownloads', 'sortable' => false],
            ['title' => 'تاريخ الإضافة', 'type' => 'date', 'field' => 'created_at', 'renderType' => 'clickableCreatedDate'],
            ['title' => 'اخر تحديث', 'type' => 'date', 'field' => 'updated_at', 'renderType' => 'clickableUpdatedDate'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];

        $this->actions = ['delete', 'view', 'folder', 'read', 'edit', 'pdfs', 'privateUsers', 'website', 'application'];
        if ($this->visibleColumns == null) {
            $this->visibleColumns = array_column($this->columns, 'field');
        }
    }

    public function getFilterStatus($filterName)
    {
        return property_exists($this, $filterName) ? $this->$filterName : false;
    }

    // Toggle filter methods
    public function toggleWebsiteFilter()
    {
        $this->websiteFilter = !$this->websiteFilter;
        $this->resetPage();
    }

    public function toggleApplicationFilter()
    {
        $this->applicationFilter = !$this->applicationFilter;
        $this->resetPage();
    }

    public function togglePrivateUsersFilter()
    {
        $this->privateUsersFilter = !$this->privateUsersFilter;
        $this->resetPage();
    }

    public function togglePublishedFilter()
    {
        $this->publishedFilter = !$this->publishedFilter;
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->websiteFilter = false;
        $this->applicationFilter = false;
        $this->privateUsersFilter = false;
        $this->publishedFilter = false;
        $this->sqliteFilter = false;
        $this->noSqliteFilter = false;
        $this->pdfFilter = false;
        $this->noPdfFilter = false;
        $this->pdfAndUnpublished = false;
        $this->selectedAnalyticsFilter = null;
        $this->resetPage();
    }

    public function initializeAnalyticsData()
    {
        // Calculate book statistics
        $booksCount = Book::count();
        $booksWithSqlite = Book::whereNotNull('sqlite')->count();
        $booksWithoutSqlite = $booksCount - $booksWithSqlite;

        // Get books with PDF files
        $booksWithPdf = Book::whereHas('files')->count();
        $booksWithPdfNotPublished = Book::whereHas('files')->where('website',0)->count();

        $booksWithoutPdf = $booksCount - $booksWithPdf;

        // Get books available on website, application, and private books
        $booksOnWebsite = Book::where('website', 1)->OrWhere('application', 1)->count();
        $booksOnApplication = Book::where('application', 1)->count();
        $privateBooks = Book::whereNotNull('private_users')->where('private_users', '!=', '')->count();

        $this->analyticsData = [
            [
                'title' => 'عدد الكتب',
                'description' => 'عدد الكتب الكلي',
                'value' => $booksCount,
                'percent' => 0,
                'icon' => 'book',
                'color' => 'primary',
                'filter' => 'all',
            ],
            [
                'title' => 'كتب متاحة على الموقع',
                'description' => 'عدد الكتب المتاحة على الموقع',
                'value' => $booksOnWebsite,
                'percent' => $booksCount > 0 ? number_format((100 * $booksOnWebsite) / $booksCount, 2) : 0,
                'icon' => 'world',
                'color' => 'success',
                'filter' => 'website',
            ],
            [
                'title' => 'كتب متاحة على التطبيق',
                'description' => 'عدد الكتب المتاحة على التطبيق',
                'value' => $booksOnApplication,
                'percent' => $booksCount > 0 ? number_format((100 * $booksOnApplication) / $booksCount, 2) : 0,
                'icon' => 'device-mobile',
                'color' => 'info',
                'filter' => 'application',
            ],
            [
                'title' => 'كتب خاصة',
                'description' => 'عدد الكتب المقيدة بمستخدمين محددين',
                'value' => $privateBooks,
                'percent' => $booksCount > 0 ? number_format((100 * $privateBooks) / $booksCount, 2) : 0,
                'icon' => 'lock',
                'color' => 'warning',
                'filter' => 'private',
            ],
            [
                'title' => 'كتب مع SQLite',
                'description' => 'عدد الكتب التي تحتوي على ملف SQLite',
                'value' => $booksWithSqlite,
                'percent' => $booksCount > 0 ? number_format((100 * $booksWithSqlite) / $booksCount, 2) : 0,
                'icon' => 'database',
                'color' => 'success',
                'filter' => 'sqlite',
            ],
            [
                'title' => 'كتب بدون SQLite',
                'description' => 'عدد الكتب التي لا تحتوي على ملف SQLite',
                'value' => $booksWithoutSqlite,
                'percent' => $booksCount > 0 ? number_format((100 * $booksWithoutSqlite) / $booksCount, 2) : 0,
                'icon' => 'database-off',
                'color' => 'warning',
                'filter' => 'no-sqlite',
            ],
            [
                'title' => 'كتب مع PDF وغير منشورة',
                'description' => 'الكتب التي تحتوي على ملف PDF وغير منشورة',
                'value' => $booksWithPdfNotPublished,
                'percent' => $booksCount > 0 ? number_format((100 * $booksWithPdfNotPublished) / $booksCount, 2) : 0,
                'icon' => 'file-text',
                'color' => 'info',
                'filter' => 'pdf-unpublished',
            ],
            [
                'title' => 'كتب بدون PDF',
                'description' => 'عدد الكتب التي لا تحتوي على ملف PDF',
                'value' => $booksWithoutPdf,
                'percent' => $booksCount > 0 ? number_format((100 * $booksWithoutPdf) / $booksCount, 2) : 0,
                'icon' => 'file-off',
                'color' => 'danger',
                'filter' => 'no-pdf',
            ],
        ];
    }

    public function selectAnalyticsFilter($filter)
    {
        // Reset all filters first
        $this->resetFilters();

        // Set the selected analytics filter
        $this->selectedAnalyticsFilter = $filter;

        // Apply the corresponding filter
        switch ($filter) {
            case 'website':
                $this->websiteFilter = true;
                break;
            case 'application':
                $this->applicationFilter = true;
                break;
            case 'private':
                $this->privateUsersFilter = true;
                break;
            case 'published':
                $this->publishedFilter = true;
                break;
            case 'sqlite':
                $this->sqliteFilter = true;
                break;
            case 'no-sqlite':
                $this->noSqliteFilter = true;
                break;
            case 'pdf':
                $this->pdfFilter = true;
                break;
            case 'no-pdf':
                $this->noPdfFilter = true;
                break;
            case 'pdf-unpublished':
                $this->pdfAndUnpublished = true;
                break;
            case 'all':
            default:
                // Show all books - no specific filter
                break;
        }

        $this->resetPage();
    }

    public function render()
    {
        $query = Book::search($this->search);

        // Apply category filter
        if ($this->category) {
            $query->whereHas('categories', function ($q) {
                $q->where('category_id', $this->category);
            });
        }

        // Apply author filter
        if ($this->author) {
            $query->where('author_id', $this->author);
        }

        // Apply website filter
        if ($this->websiteFilter) {
            $query->where('website', 1);
        }

        // Apply application filter
        if ($this->applicationFilter) {
            $query->where('application', 1);
        }

        // Apply private users filter
        if ($this->privateUsersFilter) {
            $query->whereNotNull('private_users');
        }

        // Apply published filter (books with content)
        if ($this->publishedFilter) {
            $query->has('contents');
        }

        // Apply SQLite filter
        if ($this->sqliteFilter) {
            $query->whereNotNull('sqlite');
        }

        // Apply no SQLite filter
        if ($this->noSqliteFilter) {
            $query->whereNull('sqlite');
        }

        // Apply PDF filter
        if ($this->pdfFilter) {
            $query->whereHas('files');
        }

        // Apply no PDF filter
        if ($this->noPdfFilter) {
            $query->whereDoesntHave('files');
        }

        // Apply PDF and unpublished filter
        if ($this->pdfAndUnpublished) {
            $query->whereHas('files')->where('website',0);
        }

        $books = $query->withCount('contents')
                     ->orderBy($this->sortField, $this->sortDirection)
                     ->paginate(12);

        return view('modules.book.books-table', [
            'data' => $books,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
            'searchPlaceholder' => $this->searchPlaceholder,
            'analyticsData' => $this->analyticsData,
            'selectedAnalyticsFilter' => $this->selectedAnalyticsFilter,
        ]);
    }

    // New method to handle column rendering
    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            // Make book title clickable
            if ($column['field'] === 'title') {
                return new \Illuminate\Support\HtmlString(
                '<a href="' . route('books.report', $item->id) . '" class="text-light">' . $item->title . '</a>'
                );
            }
            return $item->{$column['field']};
        }
        switch ($column['renderType']) {
            case 'categories':
                return $this->renderCategories($item);
            case 'author.name':
                return $item->author->name ?? '-';
            case 'sqliteDownloads':
                return $item->sqliteDownloadsCount();
            case 'pdfDownloads':
                return $item->pdfDownloadsCount();
            case 'booksCount':
                return '<a href="' . route('books.index', ['author' => $item->id]) . '">' . $item->books_count . '</a>';
            case 'humanDate':
                $date = '';
                try {
                    // Call to a member function diffForHumans() on false
                    if ($item->updated_at == null) {
                        $date = 'NA';
                    } else {
                        // Call to a member function diffForHumans() on string
                        $isString = is_string($item->updated_at);
                        if ($isString) {
                            $item->updated_at = \Carbon\Carbon::parse($item->updated_at);
                            $date = $item->updated_at->format('Y-m-d H:i:s');
                        } else {
                            $date = $item->updated_at->diffForHumans();
                        }
                    }
                } catch (\Exception $e) {
                    $date = $item->created_at->format('Y-m-d H:i:s');
                }

                return $date;
            case 'date':
                $date = '';
                $formattedDate = Carbon::parse($item->updated_at)->format('Y-m-d');

                return $formattedDate;
            case 'clickableUpdatedDate':
                return $this->renderClickableUpdatedDate($item);
            case 'clickableCreatedDate':
                return $this->renderClickableCreatedDate($item);
            default:
                return $item->{$column['field']};
        }
    }

    /**
     * Render a clickable updated_at date field
     */
    protected function renderClickableUpdatedDate($item)
    {
        $timestamp = $item->{$item->getUpdatedAtColumn()};
        $formattedDate = $timestamp ? Carbon::parse($timestamp)->format('Y-m-d') : 'N/A';

        $data = htmlspecialchars(json_encode([
            'id' => $item->id,
            'title' => $item->title,
            'updated_at' => $timestamp ? Carbon::parse($timestamp)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s')
        ]), ENT_QUOTES, 'UTF-8');

        return new \Illuminate\Support\HtmlString(
            '<a href="javascript:void(0)" class="date-link" data-action="updated" data-book=\'' . $data . '\' title="انقر لتعديل تاريخ التحديث">' . $formattedDate . '</a>'
        );
    }

    /**
     * Render a clickable created_at date field
     */
    protected function renderClickableCreatedDate($item)
    {
        $timestamp = $item->{$item->getCreatedAtColumn()};
        $formattedDate = $timestamp ? Carbon::parse($timestamp)->format('Y-m-d') : 'N/A';

        $data = htmlspecialchars(json_encode([
            'id' => $item->id,
            'title' => $item->title,
            'created_at' => $timestamp ? Carbon::parse($timestamp)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s')
        ]), ENT_QUOTES, 'UTF-8');

        return new \Illuminate\Support\HtmlString(
            '<a href="javascript:void(0)" class="date-link" data-action="created" data-book=\'' . $data . '\' title="انقر لتعديل تاريخ الإضافة">' . $formattedDate . '</a>'
        );
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $s3Url = env('SPACES_URL');
        $model = 'book';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف الكتاب ' . $item->title . '؟ - عملية الحذف قد تأخذ بعض الوقت لحذف محتوي الكتاب من قاعدة البيانات FTS';
        $item['delete'] = route('books.destroy', $item->id);
        $item['created_at'] = Carbon::parse($item->created_at)->format('Y-m-d H:i:s');
        $item['updated_at'] = Carbon::parse($item->updated_at)->format('Y-m-d H:i:s');
        $doseBookHasContent = $item->contents_count > 0;
        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => 'openDeleteModal(' . json_encode($item) . ')',
                'url' => 'javascript:void(0)',
            ],
            'folder' => [
                'icon' => 'ti-folder',
                'title' => 'folder',
                'color' => 'text-secondary',
                'url' => route('files.index', [
                    'disk' => 'spaces',
                    'path' => 'books/' . $item->id,
                ]),
                'target' => '_blank',
            ],
            'view' => [
                'icon' => 'ti-eye',
                'title' => 'View',
                'color' => $doseBookHasContent ? 'text-success' : 'text-danger',
                'url' => $doseBookHasContent ? "$baseUrl/admin/{$model}s/{$item->id}" : null,
            ],
            'read' => [
                'icon' => 'ti-book',
                'title' => 'Read',
                'color' => $doseBookHasContent ? 'text-success' : 'text-danger',
                'url' => $doseBookHasContent ? "$baseUrl/{$model}/{$item->id}/read" : null,
                'target' => $doseBookHasContent ? '_blank' : null,
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}/edit",
            ],
            'pdfs' => [
                'icon' => 'ti-pdf',
                'title' => 'PDFs',
                'color' => $item->pdfs->count() > 0 ? 'text-success' : 'text-secondary',
                'url' => "$baseUrl/admin/{$model}/{$item->id}/pdf",
            ],
            'privateUsers' => [
                'icon' => 'ti-lock',
                'title' => 'Private Users',
                'color' => $item->private_users != null ? 'text-danger' : 'text-secondary',
                'tooltip' => 'خاص لبعض المستخدمين',
                'onclick' => 'openPrivateUsersModal(' . json_encode([
                    'id' => $item->id,
                    'title' => $item->title,
                    'private_users' => $item->private_users,
                ]) . ')',
                'url' => 'javascript:void(0)',
            ],
            'website' => [
                'icon' => 'ti-world',
                'title' => 'Website',
                'color' => $item->website == 1 ? 'text-success' : 'text-danger',
                'tooltip' => $item->website == 1 ? 'ظاهر الموقع' : 'غير ظاهر الموقع',
                'onclick' => 'openWebsiteVisibilityModal(' . json_encode([
                    'id' => $item->id,
                    'title' => $item->title,
                    'website' => $item->website,
                ]) . ')',
                'url' => 'javascript:void(0)',
            ],
            'application' => [
                'icon' => 'ti-device-mobile',
                'title' => 'Application',
                'color' => $item->application == 1 ? 'text-success' : 'text-danger',
                'tooltip' => $item->application == 1 ? 'ظاهر التطبيق' : 'غير ظاهر التطبيق',
                'onclick' => 'openApplicationVisibilityModal(' . json_encode([
                    'id' => $item->id,
                    'title' => $item->title,
                    'application' => $item->application,
                ]) . ')',
                'url' => 'javascript:void(0)',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if ($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            // Undefined array key "url"
            if (!isset($action['url']) || $action['url'] == null) {
                $action['url'] = 'javascript:void(0)';
            }

            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }

    protected function renderCategories($book)
    {
        $html = '';
        foreach ($book->categories as $category) {
            $html .= '<span class="badge bg-primary mt-1">' . $category->title . '</span> ';
        }
        return $html;
    }
}
