<?php

namespace App\Livewire;

use App\Models\SearchHistory;
use App\Models\User;
use Illuminate\Support\Facades\Request;

class SearchHistoriesTable extends DataTable
{
    public $searchType = null;
    public $userId = null;

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن سجلات البحث بالكلمة المفتاحية';
        $this->searchType = Request::query('search_type');
        $this->userId = Request::query('user_id');
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'كلمة البحث', 'type' => 'string', 'field' => 'search_term'],
            ['title' => 'نوع البحث', 'type' => 'string', 'field' => 'search_type', 'renderType' => 'search_type'],
            ['title' => 'المستخدم', 'type' => 'string', 'field' => 'user_id', 'renderType' => 'user'],
            ['title' => 'عنوان IP', 'type' => 'string', 'field' => 'ip_address'],
            ['title' => 'تاريخ البحث', 'type' => 'date', 'field' => 'created_at'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['delete'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }

    public function render()
    {
        $query = SearchHistory::query();
        
        // Apply search
        if (!empty($this->search)) {
            $query->where('search_term', 'like', '%' . $this->search . '%');
        }
        
        // Apply filters
        if ($this->searchType) {
            $query->where('search_type', $this->searchType);
        }
        
        if ($this->userId) {
            $query->where('user_id', $this->userId);
        }
        
        // Get the data with sorting and pagination
        $searchHistories = $query
            ->orderBy($this->sortField, $this->sortDirection)
            ->with('user')
            ->paginate(12);

        return view('components.table.common-table', [
            'data' => $searchHistories,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            if ($column['field'] === 'created_at') {
                return $item->created_at ? $item->created_at->format('Y-m-d H:i:s') : '';
            }
            return $item->{$column['field']};
        }

        switch ($column['renderType']) {
            case 'search_type':
                $types = [
                    'all' => 'الكل',
                    'books' => 'الكتب',
                    'authors' => 'المؤلفين',
                    'all_content' => 'كل المحتوى',
                    'current_book' => 'الكتاب الحالي',
                ];
                return $types[$item->search_type] ?? $item->search_type;
                
            case 'user':
                if (!$item->user_id) {
                    return 'زائر';
                }
                if ($item->user) {
                    return '<a href="' . route('users.show', $item->user_id) . '">' . $item->user->name . '</a>';
                }
                return 'مستخدم #' . $item->user_id;
                
            default:
                return $item->{$column['field']};
        }
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'search-history';
        $models = 'search-histories';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف سجل البحث هذا؟';
        $item['delete'] = route('search-histories.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}
