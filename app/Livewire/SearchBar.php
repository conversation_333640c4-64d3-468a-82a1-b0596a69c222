<?php

namespace App\Livewire;

use App\Helpers\TextHelper;
use App\Models\Book;
use App\Models\PopularSearch;
use App\Models\SearchHistory;
use Illuminate\Support\Facades\Request;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Models\BookSearch;

class SearchBar extends Component
{
    public $query = '';
    public $searchType = 'all_content';
    public $showOptions = false;
    public $autoCompleteResults = [];
    public $currentBookId = null;
    public $startOpen = false; // Add property to receive the initial state
    public $isMobileView = false; // Flag for mobile view
    public $showMobileSearch = false; // Control mobile search modal visibility
    public $recentSearches = [];
    public $popularSearches = [];
    public $popularBooks = [];
    public $showAdvancedSearch = false;
    public $isSearching = false; // Flag for loading state
    public $showResults = false; // Flag to control when to show results
    public $showMobileModal = false; // Control mobile modal visibility
    public $resultsCount = null;
    public $screen = '';
    public $searchBarWidth = '736px';

    // Livewire v3 listeners
    protected $listeners = [];

    public function mount()
    {
        // Initialize component
        $this->showOptions = $this->startOpen; // Set initial state based on prop

        // Ensure mobile modal is closed on initialization
        $this->showMobileModal = false;
        $this->showMobileSearch = false;

        // Load recent searches
        $this->recentSearches = SearchHistory::getRecentSearches(5);

        $this->isSearching = false;

        // Load popular searches
        $this->popularSearches = PopularSearch::getPopularSearches(5);

        


    }

    public function toggleOptions()
    {
        $this->showOptions = !$this->showOptions;
    }

    public function toggleMobileSearch()
    {
        $this->showMobileSearch = !$this->showMobileSearch;
        $this->showMobileModal = $this->showMobileSearch; // Sync with modal state
        if ($this->showMobileSearch) {
            // Reset results when opening
            $this->autoCompleteResults = [];
            $this->dispatch('focus-search'); // Focus the search input when modal opens
        }
    }

    public function closeMobileModal()
    {
        $this->showMobileSearch = false;
        $this->showMobileModal = false;
    }

    public function selectSearchType($type, $currentBookId = null)
    {
        $this->searchType = $type;
        $this->currentBookId = $currentBookId;
        $this->showOptions = false;
        $this->isSearching = $this->isSearching ? false : true;
        $this->updatedQuery(); // Refresh results when search type changes
    }

    public function updatedQuery()
    {
        // Reset results if query is empty
        if (empty($this->query)) {
            $this->autoCompleteResults = [];
            $this->showResults = false;
            $this->isSearching = false;
            return;
        }

        $searchQuery = TextHelper::searchForTextFormatter($this->query);

        // Only search if query has more than 3 characters
        if (!empty($searchQuery) && strlen($searchQuery) > 2) {
            // Set searching state to true to show loading shimmer
            $this->isSearching = true;

            // Reset previous results
            $this->autoCompleteResults = [];
            $this->showResults = false;

            try {
                switch ($this->searchType) {
                    case 'current_book':
                        if ($this->currentBookId) {
                            $this->autoCompleteResults = BookSearch::search($searchQuery, 'current_book', $this->currentBookId);
                        } else {
                            $this->autoCompleteResults = [];
                        }
                        break;
                    case 'all_content':
                        $contents = BookSearch::search($searchQuery, 'all_content');
                        $this->resultsCount = BookSearch::searchResultsCount($searchQuery, 'all_content');
                        foreach ($contents as $content) {
                            $book = Book::where('id', $content->book_id)->with('author')->first();
                            $searchItem = [
                                'title' => $content->book_title,
                                'subtitle' => ($book  && $book->author ? $book->author->name .' '. $book->author->formattedDeathDate2() : ''),
                                'desc' => ($content->nass ?? $content->snip) ,
                                'id' => $content->book_id,
                                'page' => $content->page,
                                'part' => $content->part,
                                'image' => null,
                                'type' => 'نص',
                            ];
                            array_push($this->autoCompleteResults, $searchItem);
                        }
                        break;
                    default:
                        $this->autoCompleteResults = [];
                }
            } finally {
                $this->isSearching = false;
                $this->showResults = count($this->autoCompleteResults) > 0 || strlen($searchQuery) > 2;
                $this->showOptions = false; // Hide options when showing results
            }
        } else {
            $this->autoCompleteResults = [];
            $this->showResults = false;
        }
    }

    public function selectResult($term)
    {
        $this->query = TextHelper::searchForTextFormatter($term);

        // Record the search in history and update popular searches
        if (!empty($this->query)) {
            SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
        }

        $this->search();
    }

    public function selectRecentSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();
        $this->search(); // Automatically trigger search after selecting a recent search term
    }

    public function selectPopularSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();

        // Update the click count for this popular search
        PopularSearch::updateSearchCount($term);

        $this->search(); // Automatically trigger search after selecting a popular search term
    }

    public function search()
    {
        // Don't proceed if query is empty
        if (empty(trim($this->query))) {
            return;
        }

        // Close mobile search modal after search is triggered
        $this->showMobileSearch = false;

        // Record the search in history and update popular searches
        SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
    }

    public function openAdvancedSearch()
    {
        $this->showAdvancedSearch = true;
        return redirect()->to(
            '/advanced_search?' .
                http_build_query([
                    'q' => $this->query,
                    'search_type' => $this->searchType,
                ]),
        );
    }

    public function focusSearch()
    {
        if ($this->isMobileView) {
            $this->showMobileSearch = true;
        } else {
            $this->showOptions = true;
        }
        $this->dispatch('focus-search');
    }

    #[On('keyboardShortcut')]
    public function keyboardShortcut($data)
    {
        if (isset($data['key']) && $data['key'] === 'search') {
            $this->focusSearch();
        }
    }

    public function render()
    {
        // Refresh recent searches
        $this->recentSearches = SearchHistory::getRecentSearches(5);
        $this->popularSearches = PopularSearch::getPopularSearches(5);
        $this->popularBooks = Book::with(['author:id,name,death_date', 'categories'])
            ->where('display_on_home', 1)
            ->latest()
            ->take(5)
            ->get();

        return view('livewire.search-bar');
    }
}
