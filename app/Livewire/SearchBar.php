<?php

namespace App\Livewire;

use App\Helpers\TextHelper;
use App\Models\Book;
use App\Models\BookContent;
use App\Models\PopularSearch;
use App\Models\SearchHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Models\BookSearch;

class SearchBar extends Component
{
    public $query = '';
    public $searchType = 'all_content';
    public $showOptions = false;
    public $autoCompleteResults = [];
    public $currentBookId = null;
    public $startOpen = false; // Add property to receive the initial state
    public $isMobileView = false; // Flag for mobile view
    public $showMobileSearch = false; // Control mobile search modal visibility
    public $recentSearches = [];
    public $popularSearches = [];
    public $popularBooks = [];
    public $showAdvancedSearch = false;
    public $isSearching = false; // Flag for loading state
    public $showResults = false; // Flag to control when to show results
    public $showMobileModal = false; // Control mobile modal visibility
    public $resultsCount = null;
    public $screen = '';
    public $searchBarWidth = '736px';

    // Expanded content properties
    public $expandedContent = false;
    public $expandedBookId = null;
    public $expandedPage = null;
    public $expandedPart = null;
    public $expandedBook = null;
    public $expandedContentText = '';
    public $expandedCurrentTitle = '';
    public $expandedPagination = [];
    public $expandedIsLoading = false;

    // Livewire v3 listeners
    protected $listeners = ['expand-book-content'];

    public function mount()
    {
        // Initialize component
        $this->showOptions = $this->startOpen; // Set initial state based on prop

        // Ensure mobile modal is closed on initialization
        $this->showMobileModal = false;
        $this->showMobileSearch = false;

        // Load recent searches
        $this->recentSearches = SearchHistory::getRecentSearches(5);

        $this->isSearching = false;

        // Load popular searches
        $this->popularSearches = PopularSearch::getPopularSearches(5);

        


    }

    public function toggleOptions()
    {
        $this->showOptions = !$this->showOptions;
    }

    public function toggleMobileSearch()
    {
        $this->showMobileSearch = !$this->showMobileSearch;
        $this->showMobileModal = $this->showMobileSearch; // Sync with modal state
        if ($this->showMobileSearch) {
            // Reset results when opening
            $this->autoCompleteResults = [];
            $this->dispatch('focus-search'); // Focus the search input when modal opens
        }
    }

    public function closeMobileModal()
    {
        $this->showMobileSearch = false;
        $this->showMobileModal = false;
    }

    public function selectSearchType($type, $currentBookId = null)
    {
        $this->searchType = $type;
        $this->currentBookId = $currentBookId;
        $this->showOptions = false;
        $this->isSearching = $this->isSearching ? false : true;
        $this->updatedQuery(); // Refresh results when search type changes
    }

    public function updatedQuery()
    {
        // Reset results if query is empty
        if (empty($this->query)) {
            $this->autoCompleteResults = [];
            $this->showResults = false;
            $this->isSearching = false;
            return;
        }

        $searchQuery = TextHelper::searchForTextFormatter($this->query);

        // Only search if query has more than 3 characters
        if (!empty($searchQuery) && strlen($searchQuery) > 2) {
            // Set searching state to true to show loading shimmer
            $this->isSearching = true;

            // Reset previous results
            $this->autoCompleteResults = [];
            $this->showResults = false;

            try {
                switch ($this->searchType) {
                    case 'current_book':
                        if ($this->currentBookId) {
                            $this->autoCompleteResults = BookSearch::search($searchQuery, 'current_book', $this->currentBookId);
                        } else {
                            $this->autoCompleteResults = [];
                        }
                        break;
                    case 'all_content':
                        $contents = BookSearch::search($searchQuery, 'all_content');
                        $this->resultsCount = BookSearch::searchResultsCount($searchQuery, 'all_content');
                        foreach ($contents as $content) {
                            $book = Book::where('id', $content->book_id)->with('author')->first();
                            $searchItem = [
                                'title' => $content->book_title,
                                'subtitle' => ($book  && $book->author ? $book->author->name .' '. $book->author->formattedDeathDate2() : ''),
                                'desc' => ($content->nass ?? $content->snip) ,
                                'id' => $content->book_id,
                                'page' => $content->page,
                                'part' => $content->part,
                                'image' => null,
                                'type' => 'نص',
                            ];
                            array_push($this->autoCompleteResults, $searchItem);
                        }
                        break;
                    default:
                        $this->autoCompleteResults = [];
                }
            } finally {
                $this->isSearching = false;
                $this->showResults = count($this->autoCompleteResults) > 0 || strlen($searchQuery) > 2;
                $this->showOptions = false; // Hide options when showing results
            }
        } else {
            $this->autoCompleteResults = [];
            $this->showResults = false;
        }
    }

    public function selectResult($term)
    {
        $this->query = TextHelper::searchForTextFormatter($term);

        // Record the search in history and update popular searches
        if (!empty($this->query)) {
            SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
        }

        $this->search();
    }

    public function selectRecentSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();
        $this->search(); // Automatically trigger search after selecting a recent search term
    }

    public function selectPopularSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();

        // Update the click count for this popular search
        PopularSearch::updateSearchCount($term);

        $this->search(); // Automatically trigger search after selecting a popular search term
    }

    public function search()
    {
        // Don't proceed if query is empty
        if (empty(trim($this->query))) {
            return;
        }

        // Close mobile search modal after search is triggered
        $this->showMobileSearch = false;

        // Record the search in history and update popular searches
        SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
    }

    public function openAdvancedSearch()
    {
        $this->showAdvancedSearch = true;
        return redirect()->to(
            '/advanced_search?' .
                http_build_query([
                    'q' => $this->query,
                    'search_type' => $this->searchType,
                ]),
        );
    }

    public function focusSearch()
    {
        if ($this->isMobileView) {
            $this->showMobileSearch = true;
        } else {
            $this->showOptions = true;
        }
        $this->dispatch('focus-search');
    }

    #[On('keyboardShortcut')]
    public function keyboardShortcut($data)
    {
        if (isset($data['key']) && $data['key'] === 'search') {
            $this->focusSearch();
        }
    }

    #[On('expand-book-content')]
    public function expandBookContent($bookId, $page, $part = null)
    {
        $this->expandedBookId = $bookId;
        $this->expandedPage = $page;
        $this->expandedPart = $part === 'null' ? null : $part;
        $this->expandedContent = true;
        $this->expandedIsLoading = true;

        $this->loadExpandedContent();
    }

    public function closeExpandedContent()
    {
        $this->expandedContent = false;
        $this->reset(['expandedBookId', 'expandedPage', 'expandedPart', 'expandedBook', 'expandedContentText', 'expandedCurrentTitle', 'expandedPagination']);
    }

    public function navigateExpandedContent($direction)
    {
        if ($direction === 'next' && $this->expandedPagination['nextPage']) {
            $this->expandedPage = $this->expandedPagination['nextPage'];
        } elseif ($direction === 'prev' && $this->expandedPagination['previousPage']) {
            $this->expandedPage = $this->expandedPagination['previousPage'];
        }

        $this->expandedIsLoading = true;
        $this->loadExpandedContent();
    }

    private function loadExpandedContent()
    {
        try {
            $isAuth = Auth::check();

            // Get book data
            $this->expandedBook = Book::with(['author:id,nickname,name,description,death_date', 'categories:id,title', 'titles:book_id,id,title_id,title,level'])
                ->where('id', $this->expandedBookId)
                ->first(['id', 'title', 'image', 'summary', 'author_id', 'created_at']);

            if (!$this->expandedBook) {
                $this->expandedIsLoading = false;
                return;
            }

            if ($this->expandedBook->application != 1 && !$isAuth) {
                $this->expandedIsLoading = false;
                return;
            }

            $this->expandedPart = $this->expandedPart ?? $this->expandedBook->firstPart();

            // Get pagination data
            $this->expandedPagination = [
                'firstPage' => $this->expandedBook->firstPage($this->expandedPart),
                'lastPage' => $this->expandedBook->lastPage($this->expandedPart),
                'totalPages' => $this->expandedBook->totalPages(),
                'firstPart' => $this->expandedBook->firstPart(),
                'lastPart' => $this->expandedBook->lastPart(),
            ];

            $this->expandedPagination['previousPage'] = $this->expandedBook->previousPage($this->expandedPage, $this->expandedPart);
            $this->expandedPagination['nextPage'] = $this->expandedBook->nextPage($this->expandedPage, $this->expandedPart);
            $this->expandedPagination['currentPage'] = $this->expandedPage;
            $this->expandedPagination['currentPart'] = $this->expandedPart;

            // Adjust page if needed
            $this->expandedPage = $this->expandedPagination['firstPage'] > $this->expandedPage ? $this->expandedPagination['firstPage'] : $this->expandedPage;

            // Fetch content
            $query = BookContent::where('book_id', $this->expandedBook->id)->where('page', $this->expandedPage);

            if ($this->expandedPart) {
                $query->where('part', $this->expandedPart);
            }

            $contentModel = $query->first(['nass', 'page_id', 'html']);

            if ($contentModel == null && $this->expandedBook->hasContent() && count($this->expandedBook->contents) > 0) {
                $this->expandedContentText = 'الصفحة غير موجودة';
                $this->expandedIsLoading = false;
                return;
            }

            // Process HTML content
            if ($contentModel != null && ($contentModel->html == null || empty($contentModel->html))) {
                $titles = $this->expandedBook->titles->map(function ($title) {
                    return [
                        'id' => $title->id,
                        'title_id' => $title->title_id,
                        'title' => $title->title,
                        'level' => $title->level,
                    ];
                });

                $nass = \App\Helpers\BookContentStyleHelper::applyStyle($contentModel, $titles);
                $contentModel->html = $nass;
                $contentModel->save();
            } else {
                if ($contentModel != null) {
                    $nass = $contentModel->html;
                }
            }

            $this->expandedContentText = $nass ?? '';

            // Process titles to get current title
            $titlesResult = $this->processExpandedTitles($this->expandedBook, $contentModel->page_id ?? 0);
            $this->expandedCurrentTitle = $titlesResult['currentTitle'];

        } catch (\Exception) {
            $this->expandedContentText = 'حدث خطأ في تحميل المحتوى';
        } finally {
            $this->expandedIsLoading = false;
        }
    }

    private function processExpandedTitles($book, $pageId)
    {
        $titles = $book->titles
            ->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level ?? 1,
                ];
            })
            ->sortBy('title_id')
            ->values();

        $currentTitle = '';
        foreach ($titles as $title) {
            if ($title['title_id'] <= $pageId) {
                $currentTitle = $title['title'];
            } else {
                break;
            }
        }

        return [
            'currentTitle' => $currentTitle,
        ];
    }

    public function render()
    {
        // Refresh recent searches
        $this->recentSearches = SearchHistory::getRecentSearches(5);
        $this->popularSearches = PopularSearch::getPopularSearches(5);
        $this->popularBooks = Book::with(['author:id,name,death_date', 'categories'])
            ->where('display_on_home', 1)
            ->latest()
            ->take(5)
            ->get();

        return view('livewire.search-bar');
    }
}
