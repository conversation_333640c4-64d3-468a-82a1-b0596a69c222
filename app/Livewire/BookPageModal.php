<?php

namespace App\Livewire;

use App\Helpers\BookContentStyleHelper;
use App\Models\Book;
use App\Models\BookContent;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Livewire\Component;

class BookPageModal extends Component
{
    public $isOpen = false;
    public $bookId = null;
    public $page = null;
    public $part = null;
    public $book = null;
    public $content = '';
    public $currentTitle = '';
    public $pagination = [];
    public $isLoading = false;

    // Add a variable to enable or disable caching
    private $enableCaching = true;

    #[On('open-book-page-modal')]
    public function openModal($bookId, $page, $part = null)
    {
        Log::info('BookPageModal: openModal called', ['bookId' => $bookId, 'page' => $page, 'part' => $part]);

        $this->bookId = $bookId;
        $this->page = $page;
        $this->part = $part === 'null' ? null : $part;
        $this->isOpen = true;
        $this->isLoading = true;

        $this->loadBookContent();
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->reset(['bookId', 'page', 'part', 'book', 'content', 'currentTitle', 'pagination']);
    }

    private function loadBookContent()
    {
        try {
            $isAuth = auth()->check();
            $cacheKey = "book_page_modal_{$this->bookId}_{$this->page}_" . ($this->part ?? 'default');

            // Check if caching is enabled
            if ($this->enableCaching && Cache::has($cacheKey)) {
                $viewData = Cache::get($cacheKey);
                $this->book = $viewData['book'];
                $this->content = $viewData['content'];
                $this->currentTitle = $viewData['currentTitle'];
                $this->pagination = $viewData['pagination'];
                $this->isLoading = false;
                return;
            }

            // Get book data
            $bookCacheKey = 'book_' . $this->bookId;
            if (!Cache::has($bookCacheKey) || !$this->enableCaching) {
                $this->book = Book::with(['author:id,nickname,name,description,death_date', 'categories:id,title', 'titles:book_id,id,title_id,title,level'])
                    ->where('id', $this->bookId)
                    ->first(['id', 'title', 'image', 'summary', 'author_id', 'created_at']);

                if (!$this->book) {
                    $this->isLoading = false;
                    return;
                }

                if ($this->book->application != 1 && !$isAuth) {
                    $this->isLoading = false;
                    return;
                }

                if ($this->enableCaching) {
                    Cache::put($bookCacheKey, $this->book, now()->addDays(7));
                }
            } else {
                $this->book = Cache::get($bookCacheKey);
            }

            $this->part = $this->part ?? $this->book->firstPart();

            // Get pagination data
            $paginationCacheKey = "pagination_{$this->bookId}_" . ($this->part ?? 'all');
            if (!Cache::has($paginationCacheKey) || !$this->enableCaching) {
                $this->pagination = [
                    'firstPage' => $this->book->firstPage($this->part),
                    'lastPage' => $this->book->lastPage($this->part),
                    'totalPages' => $this->book->totalPages(),
                    'firstPart' => $this->book->firstPart(),
                    'lastPart' => $this->book->lastPart(),
                ];
                Cache::put($paginationCacheKey, $this->pagination, now()->addDays(1));
            } else {
                $this->pagination = Cache::get($paginationCacheKey);
            }

            $this->pagination['previousPage'] = $this->book->previousPage($this->page, $this->part);
            $this->pagination['nextPage'] = $this->book->nextPage($this->page, $this->part);
            $this->pagination['currentPage'] = $this->page;
            $this->pagination['currentPart'] = $this->part;

            // Adjust page if needed
            $this->page = $this->pagination['firstPage'] > $this->page ? $this->pagination['firstPage'] : $this->page;

            // Fetch content
            $query = BookContent::where('book_id', $this->book->id)->where('page', $this->page);

            if ($this->part) {
                $query->where('part', $this->part);
            }

            $contentModel = $query->first(['nass', 'page_id', 'html']);

            if ($contentModel == null && $this->book->hasContent() && count($this->book->contents) > 0) {
                $this->content = 'الصفحة غير موجودة';
                $this->isLoading = false;
                return;
            }

            // Process HTML content
            if ($contentModel != null && ($contentModel->html == null || empty($contentModel->html))) {
                $titles = $this->book->titles->map(function ($title) {
                    return [
                        'id' => $title->id,
                        'title_id' => $title->title_id,
                        'title' => $title->title,
                        'level' => $title->level,
                    ];
                });

                $nass = BookContentStyleHelper::applyStyle($contentModel, $titles);
                $contentModel->html = $nass;
                $contentModel->save();
            } else {
                if ($contentModel != null) {
                    $nass = $contentModel->html;
                }
            }

            $this->content = $nass ?? '';

            // Process titles to get current title
            $titlesResult = $this->processTitles($this->book, $contentModel->page_id ?? 0);
            $this->currentTitle = $titlesResult['currentTitle'];

            // Create view data array for caching
            $viewData = [
                'book' => $this->book,
                'content' => $this->content,
                'currentTitle' => $this->currentTitle,
                'pagination' => $this->pagination,
            ];

            // Cache the view data if caching is enabled
            if ($this->enableCaching) {
                Cache::put($cacheKey, $viewData, now()->addHours(24));
            }

        } catch (\Exception $e) {
            $this->content = 'حدث خطأ في تحميل المحتوى';
        } finally {
            $this->isLoading = false;
        }
    }

    private function processTitles($book, $pageId)
    {
        // Simplified version of the titles processing from BookPageController
        $titles = $book->titles
            ->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level ?? 1,
                ];
            })
            ->sortBy('title_id')
            ->values();

        $currentTitle = '';
        foreach ($titles as $title) {
            if ($title['title_id'] <= $pageId) {
                $currentTitle = $title['title'];
            } else {
                break;
            }
        }

        return [
            'currentTitle' => $currentTitle,
        ];
    }

    public function navigateToPage($direction)
    {
        if ($direction === 'next' && $this->pagination['nextPage']) {
            $this->page = $this->pagination['nextPage'];
        } elseif ($direction === 'prev' && $this->pagination['previousPage']) {
            $this->page = $this->pagination['previousPage'];
        }

        $this->isLoading = true;
        $this->loadBookContent();
    }

    public function render()
    {
        return view('livewire.book-page-modal');
    }
}
