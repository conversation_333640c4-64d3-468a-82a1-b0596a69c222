<?php

namespace App\Livewire;

use App\Models\Book;
use App\Models\Author;
use App\Models\Category;
use App\RolesEnum;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BooksFilterComponent extends Component
{
    use WithPagination;

    protected $paginationTheme = 'tailwind';

    // Search and filter parameters
    public $query = '';
    public $categories = [];
    public $authors = [];
    public $yearFrom = '';
    public $yearTo = '';
    public $sort = 'newest';

    // For loading state
    public $isLoading = false;

    // For displaying counts
    public $booksCount = 0;

    // For loading more authors
    public $visibleAuthorsCount = 10;
    public $authorSearchTerm = '';

    // Admin status
    protected $isAdmin = false;

    public $showYearFilter = true;

    // Listen for sidebar filter changes
    protected $listeners = ['filterUpdated' => '$refresh'];

    //*this mean this page is for author page if it is set
    public $author;
    public $category;

    public function mount()
    {
        $this->isAdmin = Auth::check() && Auth::user()->isSuperAdmin();
        if ($this->isAdmin) {
            $this->booksCount = Book::count();
        } else {
            $this->booksCount = Book::count();
        }
    }

    public function hydrate() {}

    public function dehydrate() {}

    public function updating($name, $value) {}

    public function updated($name, $value) {}

    // Reset pagination when filters change
    public function updatedQuery()
    {
        $this->resetPage();
    }

    public function updatedCategories()
    {
        $this->resetPage();
    }

    public function updatedAuthors()
    {
        $this->resetPage();
    }

    public function updatedYearFrom()
    {
        $this->resetPage();
    }

    public function updatedYearTo()
    {
        $this->resetPage();
    }

    public function updatedSort()
    {
        $this->resetPage();
    }

    // Manual refresh method that can be called from the view
    public function refresh() {}

    // Load more authors
    public function loadMoreAuthors()
    {
        $this->visibleAuthorsCount += 10;
    }

    // Get filtered books
    public function getFilteredBooks()
    {
        $query = Book::with(['author', 'categories']);

        // Apply search filter
        if (!empty($this->query)) {
            $query->where('title', 'like', '%' . $this->query . '%');
        }

        // Apply category filter
        if (!empty($this->categories)) {
            $query->whereHas('categories', function ($q) {
                $q->whereIn('categories.id', $this->categories);
            });
        }

        // Apply author filter
        if (!empty($this->authors)) {
            $query->whereHas('author', function ($q) {
                $q->whereIn('authors.id', $this->authors);
            });
        }

        // Apply year range filter (author death date)
        if (!empty($this->yearFrom) || !empty($this->yearTo)) {
            $query->whereHas('author', function ($q) {
                if (!empty($this->yearFrom)) {
                    $q->where('death_date', '>=', $this->yearFrom);
                }
                if (!empty($this->yearTo)) {
                    $q->where('death_date', '<=', $this->yearTo);
                }
            });
        }

        if (isset($this->author)) {
            $query->where('author_id', $this->author->id);
        }
        if (isset($this->category)) {
            $query->whereHas('categories', function ($q) {
                $q->where('categories.id', $this->category->id);
            });
        }

        // Apply sorting
        switch ($this->sort) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'title_asc':
                $query->orderBy('title', 'asc');
                break;
            case 'title_desc':
                $query->orderBy('title', 'desc');
                break;
            case 'author_asc':
                $query->join('authors', 'books.author_id', '=', 'authors.id')->orderBy('authors.name', 'asc')->select('books.*', 'authors.name as author_name')->distinct();
                break;
            case 'author_desc':
                $query->join('authors', 'books.author_id', '=', 'authors.id')->orderBy('authors.name', 'desc')->select('books.*', 'authors.name as author_name')->distinct();
                break;
            case 'author_die_data_asc':
                $query->join('authors', 'books.author_id', '=', 'authors.id')->orderBy('authors.death_date', 'asc')->select('books.*', 'authors.death_date as author_die_data')->distinct();
                break;
            case 'author_die_data_desc':
                $query->join('authors', 'books.author_id', '=', 'authors.id')->orderBy('authors.death_date', 'desc')->select('books.*', 'authors.death_date as author_die_data')->distinct();
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        return $query->paginate(12);
    }

    // Get filtered authors for sidebar
    public function getFilteredAuthors()
    {
        $query = Author::withCount('books')->orderBy('name');


        if (!empty($this->authorSearchTerm)) {
            $query->where('name', 'like', '%' . $this->authorSearchTerm . '%');
        }

        $allAuthors = $query->get();

        return $allAuthors;
    }

    // Get all categories for sidebar
    public function getCategories()
    {
        if (isset($this->author)) {
            $query = Category::withCount([
                'books' => function ($q) {
                    $q->where('author_id', $this->author->id);
                },
            ])->whereHas('books', function ($q) {
                $q->where('author_id', $this->author->id);
            });
        } else {
            if (!$this->isAdmin) {
                $query = Category::withCount(['books'])->whereHas('books');
            } else {
                $query = Category::withCount('books');
            }
        }

        return $query->get();
    }

    public function render()
    {
        $this->isLoading = true;

        $books = $this->getFilteredBooks();
        if (!isset($this->author)) {
            $allAuthors = $this->getFilteredAuthors();
        }
        if (!isset($this->category)) {
            $allCategories = $this->getCategories();
        }else{
            //*filter $allAuthors only show authors that have books in this category
            $allAuthors = Author::withCount('books')->whereHas('books', function ($q) {
                $q->whereHas('categories', function ($sq) {
                    $sq->where('categories.id', $this->category->id);
                });
            })->get();
        }
        $this->booksCount = $books->total();

        $this->isLoading = false;

        return view('livewire.books-filter-component', [
            'books' => $books,
            'allAuthors' => $allAuthors ?? null,
            'allCategories' => $allCategories ?? null,
            'booksCount' => $this->booksCount,
        ]);
    }
}
