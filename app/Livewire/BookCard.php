<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Book;
use Illuminate\Support\Facades\Log;

class BookCard extends Component
{
    public $book;
    public $isMobile = false;
    public $size; //small , normal
    public $imageWidth;
    public $imageHeight;
    public $cardWidth;

    public function mount(Book $book, $isMobile = false,$size = 'normal',$imageWidth = '180px',$imageHeight = '250px')
    {
        $this->book = $book;
        $this->size = $size;
        $this->isMobile = $isMobile;
        $this->imageWidth = $this->size === 'small' ? '120px' : $imageWidth;
        $this->imageHeight = $this->size === 'small' ? '160px' : $imageHeight;
        $this->cardWidth = $this->size === 'small' ? '120px' : '180px';
        if($this->imageWidth > $this->cardWidth){
            $this->cardWidth = $this->imageWidth;
        }
    }

    public function showInfoModal()
    {
        // Dispatch an event to open the book info modal using our new component
        $this->dispatch('openBookInfoDialog', $this->book->id);
    }

    public function render()
    {
        // Determine sizes based on the size property
        $cardWidth = $this->cardWidth;
        
        $imgWidth = $this->imageWidth;

        $imgHeight = $this->imageHeight;

        $iconSize = $this->size === 'small' ?
            'h-4 w-4' :
            'h-5 w-5';

        $iconPosition = $this->size === 'small' ?
            'top-1 right-1' :
            'top-2 right-2';

        $titleClass = $this->size === 'small' ?
            'text-sm font-medium line-clamp-2 text-start justify-start' :
            'card-title text-md line-clamp-2';

        $authorClass = $this->size === 'small' ?
            'text-xs text-base-content/80 line-clamp-1 text-start justify-start' :
            'text-sm text-base-content/80 line-clamp-2';

        $padding = $this->size === 'small' ?
            'py-0.5' :
            'py-1';

        return <<<HTML

        <div class="relative group cursor-pointer" style="width: {$cardWidth};">
            <!-- Info Icon Button -->
            <button wire:click.stop="showInfoModal" class="absolute {$iconPosition} z-10 p-1 bg-base-100/80 hover:bg-base-100 rounded-full opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity duration-300" aria-label="Show book info">
                <svg xmlns="http://www.w3.org/2000/svg" class="{$iconSize} text-base-content/80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </button>

            <a href={{ url('admin/v2/book/' . \$book->id) }} class="block w-full h-full">
                <!-- Card Content -->
                <img src="{{ \$book->image }}" alt="{{ \$book->title }}" class="h-full object-contain rounded-md" loading="lazy" style="width: {$imgWidth}; height: {$imgHeight};"/>
                <div class="{$padding} relative">
                    <h3 class="{$titleClass}">{{ \$book->title }}</h3>
                    <p class="{$authorClass}">
                        @if(\$book->author)
                            {{ \$book->author->name }}
                            @if(\$book->author->death_date && \$book->author->death_date != 'معاصر')
                                <span class="opacity-70">(ت: {{ \$book->author->death_date }})</span>
                            @endif
                        @else
                            <span class="opacity-70">غير معروف</span>
                        @endif
                    </p>
                </div>
            </a>
        </div>
        HTML;
    }
}
