<?php

namespace App\Livewire;

use App\Helpers\TextHelper;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookSearch;
use App\Models\Category;
use App\Models\PopularSearch;
use App\Models\SearchFtsModel;
use App\Models\SearchHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Livewire\Component;
use Livewire\WithPagination;

class SearchComponent extends Component
{
    use WithPagination;

    protected $paginationTheme = 'tailwind';

    // Search properties
    public $query = '';
    public $isSearching = false;
    public $showResults = false;
    public $autoCompleteResults = [];
    public $resultsCount = '';
    public $limit = 10;
    public $offset = 0;

    // Filter properties
    public $yearFrom = '';
    public $yearTo = '';
    public $categories = [];
    public $authors = [];
    public $books = [];
    public $authorSearchTerm = '';
    public $bookSearchTerm = '';
    public $visibleAuthorsCount = 10;
    public $visibleBooksCount = 10;

    // Sort properties
    public $sort = 'افتراضي';

    // For mobile view
    public $isMobileView = false;
    public $showMobileSearch = false;
    public $showMobileModal = false;
    public $showOptions = false;

    // For search types
    public $currentBookId = null;

    // For recent searches
    public $recentSearches = [];
    public $popularSearches = [];
    public $hasSearched = false;
    public $screen = '';

    protected $queryString = [
        'query' => ['except' => ''],
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'categories' => ['except' => []],
        'authors' => ['except' => []],
        'books' => ['except' => []],
        'sortAsc' => ['except' => true],
    ];

    public function mount()
    {
        // Load recent searches
        $this->loadRecentSearches();

        // Load popular searches
        $this->loadPopularSearches();

        // If query is already set from URL parameters, perform search
        if (!empty($this->query)) {
            $this->search();
            $this->hasSearched = true;
        }
    }

    public function loadRecentSearches()
    {
        // Get recent searches for the current IP
        $this->recentSearches = SearchHistory::where('ip_address', Request::ip())->orderBy('created_at', 'desc')->take(5)->get()->unique('term')->take(5)->values()->toArray();
    }

    public function loadPopularSearches()
    {
        // Get popular searches
        $this->popularSearches = PopularSearch::where('is_featured', true)->take(5)->get()->toArray();
    }

    public function updatedQuery($ignoreReset = false)
    {
        // Reset pagination when query changes
        if (!$ignoreReset) {
            $this->resetPage();
            $this->offset = 0;
        }

        // Reset results if query is empty
        if (empty($this->query)) {
            $this->autoCompleteResults = [];
            $this->showResults = false;
            $this->isSearching = false;
            return;
        }

        $searchQuery = TextHelper::searchForTextFormatter($this->query);

        // Only search if query has more than 3 characters
        if (!empty($searchQuery) && strlen($searchQuery) > 2) {
            // Set searching state to true to show loading shimmer
            $this->isSearching = true;

            // Reset previous results
            $this->autoCompleteResults = [];
            $this->showResults = false;

            // Get book IDs to include based on filters
            $includedBookIds = $this->getIncludedBookIds();

            // Get content results with filters
            $this->searchWithFilters($searchQuery);

            $this->showResults = true;
        }

        // Set searching state to false when search is complete
        $this->isSearching = false;
    }

    // Get book IDs to include based on category and author filters
    public function getIncludedBookIds()
    {
        if (empty($this->categories) && empty($this->authors) && empty($this->books)) {
            return [];
        }

        $query = Book::query();

        // Apply category filter
        if (!empty($this->categories)) {
            $query->whereHas('categories', function ($q) {
                $q->whereIn('categories.id', $this->categories);
            });
        }

        // Apply author filter
        if (!empty($this->authors)) {
            $query->whereHas('author', function ($q) {
                $q->whereIn('authors.id', $this->authors);
            });
        }

        // Apply book filter
        if (!empty($this->books)) {
            $query->whereIn('id', $this->books);
        }

        return $query->pluck('id')->toArray();
    }

    // Search with filters applied
    public function searchWithFilters($searchQuery)
    {
        $includedBookIds = $this->getIncludedBookIds();

        // Reset results if query is empty
        if (empty($this->query)) {
            $this->autoCompleteResults = [];
            $this->showResults = false;
            $this->isSearching = false;
            return;
        }

        $searchQuery = TextHelper::searchForTextFormatter($this->query);

        // Only search if query has more than 3 characters
        if (!empty($searchQuery) && strlen($searchQuery) > 2) {
            // Set searching state to true to show loading shimmer
            $this->isSearching = true;

            // Reset previous results
            $this->autoCompleteResults = [];
            $this->showResults = false;

            try {
                $contents = BookSearch::search($searchQuery, 'all_content', null, [], $includedBookIds,$this->limit,$this->offset);
                $this->resultsCount = BookSearch::searchResultsCount($searchQuery, 'all_content');
                foreach ($contents as $content) {
                    $book = Book::where('id', $content->book_id)->with('author')->first();
                    $searchItem = [
                        'title' => $content->book_title,
                        'subtitle' => $book && $book->author ? $book->author->name . ' ' . $book->author->formattedDeathDate2() : '',
                        'desc' => $content->nass ?? $content->snip,
                        'id' => $content->book_id,
                        'page' => $content->page,
                        'part' => $content->part,
                        'image' => null,
                        'type' => 'نص',
                    ];
                    array_push($this->autoCompleteResults, $searchItem);
                }
            } finally {
                $this->isSearching = false;
                $this->showResults = count($this->autoCompleteResults) > 0 || strlen($searchQuery) > 2;
                $this->showOptions = false; // Hide options when showing results
            }
        } else {
            $this->autoCompleteResults = [];
            $this->showResults = false;
        }
    }

    public function selectResult($term)
    {
        $this->query = TextHelper::searchForTextFormatter($term);

        // Record the search in history and update popular searches
        if (!empty($this->query)) {
            SearchHistory::recordSearch($this->query, 'content', Request::ip());
        }

        $this->search();
    }

    public function search()
    {
        // Don't proceed if query is empty
        if (empty(trim($this->query))) {
            return;
        }

        // Close mobile search modal after search is triggered
        $this->showMobileSearch = false;

        // Record the search in history and update popular searches
        SearchHistory::recordSearch($this->query, 'content', Request::ip());

        // Reload recent searches
        $this->loadRecentSearches();

        // Set hasSearched flag
        $this->hasSearched = true;

        // Reset pagination and perform search
        $this->resetPage();
        $this->offset = 0;
        $this->updatedQuery(); // Refresh results based on current search type
    }

    public function selectRecentSearch($term)
    {
        $this->query = $term;
        $this->search();
    }

    // Handle filter updates
    public function updatedCategories()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedAuthors()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedBooks()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedYearFrom()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedYearTo()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedSortBy()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    public function updatedSortAsc()
    {
        $this->resetPage();
        $this->offset = 0;
        if (!empty($this->query) && strlen($this->query) > 2) {
            $this->updatedQuery();
        }
    }

    // Get filtered authors for sidebar
    public function getFilteredAuthors()
    {
        $query = Author::withCount('books')->orderBy('name');

        if (!empty($this->authorSearchTerm)) {
            $query->where('name', 'like', '%' . $this->authorSearchTerm . '%');
        }

        return $query->get();
    }

    public function getFilteredBooks()
    {
        $query = Book::query();

        if (!empty($this->bookSearchTerm)) {
            $query->where('title', 'like', '%' . $this->bookSearchTerm . '%');
        }

        return $query->get();
    }

    // Get all categories for sidebar
    public function getCategories()
    {
        $query = Category::withCount('books');

        return $query->get();
    }

    public function loadMoreAuthors()
    {
        $this->visibleAuthorsCount += 10;
    }

    public function loadMoreBooks()
    {
        $this->visibleBooksCount += 10;
    }

    public function toggleMobileSearch()
    {
        $this->showMobileSearch = !$this->showMobileSearch;
        $this->showMobileModal = !$this->showMobileModal;
    }

    public function closeMobileModal()
    {
        $this->showMobileSearch = false;
        $this->showMobileModal = false;
    }

    // Pagination methods
    public function getCurrentPage()
    {
        return floor($this->offset / $this->limit) + 1;
    }

    public function getTotalPages()
    {
        return $this->resultsCount > 0 ? ceil($this->resultsCount / $this->limit) : 0;
    }

    public function goToPage($page)
    {
        $totalPages = $this->getTotalPages();
        if ($page >= 1 && $page <= $totalPages) {
            $this->offset = ($page - 1) * $this->limit;
            $this->updatedQuery(true);
        }
    }

    public function nextPage()
    {
        $totalPages = $this->getTotalPages();
        $currentPage = $this->getCurrentPage();
        if ($currentPage < $totalPages) {
            $this->goToPage($currentPage + 1);
        }
    }

    public function previousPage()
    {
        $currentPage = $this->getCurrentPage();
        if ($currentPage > 1) {
            $this->goToPage($currentPage - 1);
        }
    }

    public function getPageNumbers()
    {
        $totalPages = $this->getTotalPages();
        $currentPage = $this->getCurrentPage();
        $pages = [];

        if ($totalPages <= 7) {
            // Show all pages if total is 7 or less
            for ($i = 1; $i <= $totalPages; $i++) {
                $pages[] = $i;
            }
        } else {
            // Show first page
            $pages[] = 1;

            if ($currentPage > 4) {
                $pages[] = '...';
            }

            // Show pages around current page
            $start = max(2, $currentPage - 2);
            $end = min($totalPages - 1, $currentPage + 2);

            for ($i = $start; $i <= $end; $i++) {
                if (!in_array($i, $pages)) {
                    $pages[] = $i;
                }
            }

            if ($currentPage < $totalPages - 3) {
                $pages[] = '...';
            }

            // Show last page
            if (!in_array($totalPages, $pages)) {
                $pages[] = $totalPages;
            }
        }

        return $pages;
    }

    public function render()
    {
        $allAuthors = $this->getFilteredAuthors();
        $allCategories = $this->getCategories();
        $allBooks = $this->getFilteredBooks();

        // Set the books count based on search results

        return view('livewire.search-component', [
            'allAuthors' => $allAuthors,
            'allCategories' => $allCategories,
            'allBooks' => $allBooks,
        ]);
    }
}
