<?php

namespace App\Livewire;

use App\Models\Author;

class AuthorsTable extends DataTable
{
    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن المؤلفين بالاسم او سنة الوفاة';
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'الاسم', 'type' => 'string', 'field' => 'name'],
            ['title' => 'اللقب', 'type' => 'string', 'field' => 'nickname'],
            ['title' => 'سنة الوفاة', 'type' => 'string', 'field' => 'death_date'],
            ['title' => 'الكتب', 'type' => 'string', 'field' => 'books_count', 'renderType' => 'booksCount'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['delete', 'edit'];
        $this->visibleColumns = array_column($this->columns, 'field'); // Show all columns by default
    }

    public function render()
    {
        $authors = Author::search($this->search)
            ->orderBy($this->sortField, $this->sortDirection)
            ->withCount('books')
            ->paginate(12);

        return $this->renderView('components.table.common-table', [
            'data' => $authors,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }
    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }
        if($column['field'] == 'books_count'){
            return '<a href="' . route('books.index', ['author' => $item->id]) . '">' . $item->books_count . '</a>';
        }

        return '';
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL').'/admin';
        $model = 'author';

        $item['deleteMessage'] = 'هل أنت متأكد من حذف المؤلف '.$item->name.'؟';
        $item['delete'] = route('authors.destroy', ['author' => $item->id]);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/{$model}s/{$item->id}/edit",
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';

            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}
