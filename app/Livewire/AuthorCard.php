<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Author;

class AuthorCard extends Component
{
    public $author;

    public function mount(Author $author)
    {
        $this->author = $author;
    }

    public function render()
    {
        return <<<'HTML'
        <div class="group relative transform transition-all duration-300 hover:scale-[1.02] h-full">
            <a href="{{ url('/admin/v2/authors/' . $author->id) }}" class="block h-full">
                <div class="card bg-base-100 shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-base-200 h-full">
                    <div class="card-body p-6 flex flex-col">
                        <!-- Author Image -->
                        <div class="relative mb-4 flex-shrink-0">
                            <div class="w-24 h-24 mx-auto rounded-full ring-2 ring-primary/20 p-1">
                                <div class="w-full h-full rounded-full bg-gradient-to-br from-primary/5 to-primary/10 flex items-center justify-center overflow-hidden">
                                    @if(isset($author->image) && $author->image)
                                        <img src="{{ asset($author->image) }}" 
                                             alt="{{ $author->name }}" 
                                             class="w-full h-full object-cover rounded-full transition-transform duration-300 group-hover:scale-110"
                                        >
                                    @else
                                        <i class="fas fa-user text-2xl text-primary/40"></i>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Author Info -->
                        <div class="text-center space-y-2 flex-grow">
                            <h3 class="font-bold text-lg group-hover:text-primary transition-colors duration-300">
                                {{ $author->name }}
                            </h3>

                            @if(isset($author->death_date) && $author->death_date )
                                <div class="text-sm text-base-content/60 flex items-center justify-center gap-1">
                                    <i class="fas fa-calendar-alt text-xs"></i>
                                    @if($author->death_date == 'معاصر')
                                        <span>معاصر</span>
                                    @else
                                        <span>وفاة المؤلف: {{ $author->death_date }}</span>
                                    @endif
                                </div>
                            @endif

                            @if(isset($author->nickname) && $author->nickname)
                                <div class="text-sm italic text-base-content/60">
                                    {{ $author->nickname }}
                                </div>
                            @endif

                            <div class="pt-2 mt-auto">
                                <div class="badge badge-primary badge-outline gap-1">
                                    <i class="fas fa-book text-xs"></i>
                                    <span>{{ $author->books_count }} كتاب</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        HTML;
    }
} 