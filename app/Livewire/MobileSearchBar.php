<?php

namespace App\Livewire;

use App\Helpers\TextHelper;
use App\Models\Author;
use App\Models\Book;
use App\Models\PopularSearch;
use App\Models\SearchHistory;
use Illuminate\Support\Facades\Request;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Models\BookSearch;

class MobileSearchBar extends Component
{
    public $query = '';
    public $searchType = 'all';
    public $showOptions = false;
    public $autoCompleteResults = [];
    public $currentBookId = null;
    public $recentSearches = [];
    public $popularSearches = [];

    public function mount()
    {
        // Initialize component

        // Load recent searches
        if (class_exists('App\Models\SearchHistory')) {
            $this->recentSearches = SearchHistory::getRecentSearches(5);
        }

        // Load popular searches
        if (class_exists('App\Models\PopularSearch')) {
            $this->popularSearches = PopularSearch::getPopularSearches(5);
        }

        // Set the query to the latest search term if empty
        if (empty($this->query) && class_exists('App\Models\SearchHistory')) {
            $this->query = SearchHistory::getLatestSearchTerm();
        }
    }

    public function toggleOptions()
    {
        $this->showOptions = !$this->showOptions;
    }

    public function selectSearchType($type, $currentBookId = null)
    {
        $this->searchType = $type;
        $this->currentBookId = $currentBookId;
        $this->showOptions = false;
        $this->updatedQuery(); // Refresh results when search type changes
    }

    public function updatedQuery()
    {
        $this->autoCompleteResults = [];
        $searchQuery = TextHelper::searchForTextFormatter($this->query);

        if (!empty($searchQuery) && strlen($searchQuery) > 2) {
            switch ($this->searchType) {
                case 'books':
                    $books = Book::where('title', 'like', '%' . $searchQuery . '%')->limit(5)->get();
                    foreach ($books as $book) {
                        $this->autoCompleteResults[] = $book->toArray();
                    }
                    break;
                case 'authors':
                    $authors = Author::where('name', 'like', '%' . $searchQuery . '%')->limit(5)->get();
                    foreach ($authors as $author) {
                            $this->autoCompleteResults[] = [
                            'href' => '/author/' . $author->id,
                            'name' => $author->name,
                            'author_bio' => $author->bio,
                        ];
                    }
                    break;
                case 'current_book':
                    if ($this->currentBookId) {
                        $this->autoCompleteResults = BookSearch::search($searchQuery, 'current_book', $this->currentBookId);
                    } else {
                        $this->autoCompleteResults = [];
                    }
                    break;
                case 'all_content':
                    $this->autoCompleteResults = BookSearch::search($searchQuery, 'all_content');
                    break;
                case 'all':
                    $contents = BookSearch::search($searchQuery, 'all');
                    $books = Book::where('title', 'like', '%' . $searchQuery . '%')->limit(3)->get();
                    $authors = Author::where('name', 'like', '%' . $searchQuery . '%')->limit(3)->get();
                    //array_merge(): Argument #2 must be of type array, Illuminate\Database\Eloquent\Collection given
                    $books = $books->toArray();
                    $authors = $authors->toArray();
                    $this->autoCompleteResults = array_merge($contents, $books, $authors);
                    break;
                default:
                    $this->autoCompleteResults = [];
            }
        } else {
            $this->autoCompleteResults = [];
        }
    }

    public function selectResult($term)
    {
        $this->query = TextHelper::searchForTextFormatter($term);

        // Record the search in history and update popular searches
        if (!empty($this->query) && class_exists('App\Models\SearchHistory')) {
            SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
        }

        $this->search();
    }

    public function selectRecentSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();
        $this->search(); // Automatically trigger search after selecting a recent search term
    }

    public function selectPopularSearch($term)
    {
        $this->query = $term;
        $this->updatedQuery();

        // Update the click count for this popular search
        if (class_exists('App\Models\PopularSearch')) {
            PopularSearch::updateSearchCount($term);
        }

        $this->search(); // Automatically trigger search after selecting a popular search term
    }

    public function search()
    {
        // Record the search in history and update popular searches
        if (!empty($this->query) && class_exists('App\Models\SearchHistory')) {
            SearchHistory::recordSearch($this->query, $this->searchType, Request::ip());
        }

        // Redirect to search results page
        return redirect()->to('/book_search?' . http_build_query([
            'q' => $this->query,
            'search_type' => $this->searchType
        ]));
    }

    public function openAdvancedSearch()
    {
        return redirect()->to('/advanced_search?' . http_build_query([
            'q' => $this->query,
            'search_type' => $this->searchType
        ]));
    }

    #[On('keyboardShortcut')]
    public function keyboardShortcut($data)
    {
        if (isset($data['key']) && $data['key'] === 'search') {
            $this->dispatch('focus-search');
        }
    }

    public function render()
    {
        // Refresh recent searches if the class exists
        if (class_exists('App\Models\SearchHistory')) {
            $this->recentSearches = SearchHistory::getRecentSearches(5);
        }

        return view('livewire.mobile-search-bar');
    }
}
