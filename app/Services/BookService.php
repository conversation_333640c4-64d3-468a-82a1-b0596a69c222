<?php

namespace App\Services;

use App\Models\Book;
use App\Models\BookReport;
use App\Models\BookReportDetail;
use App\Models\Category;
use App\RolesEnum;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class BookService
{
    protected int $shortCache = 172800; // 2 days in seconds
    protected int $longCache = 604800;  // 1 week in seconds
    protected bool $cachingEnabled;

    /**
     * Create a new BookService instance.
     *
     * @param bool|null $enableCaching Whether to enable caching. If null, will be disabled in debug mode or for admin users.
     */
    public function __construct(?bool $enableCaching = null)
    {
        // If caching preference is not explicitly set, disable caching in debug mode or for admin users
        if ($enableCaching === null) {
            $this->cachingEnabled = !Config::get('app.debug', false) && !$this->isAdminContext();
        } else {
            $this->cachingEnabled = $enableCaching;
        }
    }

    public function getLatestBooks($count = 12)
    {
        return $this->cacheBooks("latest_books_home_{$count}", $this->shortCache, function () use ($count) {
            return Book::with(['author:id,name,death_date', 'categories'])
                ->latest()
                ->take($count)
                ->get();
        });
    }

    public function getMostReadBooksThisMonth($limit = 12)
    {
        $ids = $this->cache("most_read_books_home_ids_{$limit}", $this->longCache, function () use ($limit) {
            return BookReportDetail::select('book_id', DB::raw('count(*) as count'))
                ->where('created_at', '>=', now()->startOfMonth())
                ->where('type', 'read')
                ->groupBy('book_id')
                ->orderByDesc('count')
                ->limit($limit)
                ->pluck('book_id');
        });

        return $this->cacheBooks("most_read_books_home_v2_{$limit}", $this->shortCache, function () use ($ids) {
            return Book::with(['author:id,name,death_date', 'categories'])
                ->whereIn('id', $ids)
                ->get();
        });
    }

    public function getMostDownloadedBooksThisMonth($limit = 12)
    {
        $ids = $this->cache("most_downloaded_books_home_ids_{$limit}", $this->longCache, function () use ($limit) {
            return BookReport::select('book_id')
                ->where('type', 'sqlite')
                ->where('created_at', '>=', now()->startOfMonth())
                ->groupBy('book_id')
                ->orderByDesc(DB::raw('count(*)'))
                ->limit($limit)
                ->pluck('book_id');
        });

        return $this->cacheBooks("most_downloaded_books_home_v2_{$limit}", $this->shortCache, function () use ($ids) {
            return Book::with(['author:id,name,death_date', 'categories'])
                ->whereIn('id', $ids)
                ->get();
        });
    }

    public function getSelectedBooks($limit = 12)
    {
        return $this->cacheBooks("selected_books_home_{$limit}", $this->shortCache, function () use ($limit) {
            return Book::with(['author:id,name,death_date', 'categories'])
                ->where('display_on_home', 1)
                ->latest()
                ->take($limit)
                ->get();
        });
    }

    public function getMainCategoriesWithBookCounts()
    {
        return $this->cache('main_categories_home', $this->shortCache, function () {
            return Category::withCount('availableBooks')->get();
        });
    }

    /**
     * Check if caching is currently enabled for this service instance.
     *
     * @return bool
     */
    public function isCachingEnabled(): bool
    {
        return $this->cachingEnabled;
    }

    /**
     * Check if the current context is admin (either URL contains 'admin' or user is admin).
     *
     * @return bool
     */
    private function isAdminContext(): bool
    {
        // Check if URL contains 'admin'
        $request = request();
        if ($request && str_contains($request->getPathInfo(), 'admin')) {
            return true;
        }

        // Check if current user is admin
        $user = Auth::user();
        if ($user && ($user->hasRole(RolesEnum::SUPER_ADMIN) || $user->hasRole(RolesEnum::ADMIN))) {
            return true;
        }

        return false;
    }

    private function cache(string $key, int $duration, \Closure $callback)
    {
        // If caching is disabled, execute the callback directly
        if (!$this->cachingEnabled) {
            return $callback();
        }

        return Cache::tags(['books', 'home'])->remember($key, now()->addSeconds($duration), $callback);
    }

    private function cacheBooks(string $key, int $duration, \Closure $callback)
    {
        // If caching is disabled, execute the callback directly
        if (!$this->cachingEnabled) {
            return $callback();
        }

        return Cache::tags(['books', 'home'])->remember($key, now()->addSeconds($duration), $callback);
    }
}