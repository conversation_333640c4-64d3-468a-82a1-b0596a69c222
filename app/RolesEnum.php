<?php
namespace App;

enum RolesEnum: string
{
    case SUPER_ADMIN = 'super_admin';
    case ADMIN = 'admin';
    case USER = 'user';

    private function generatePermissions(string $model, string $actions): array
    {
        $permissions = [];
        if (str_contains($actions, 'c')) {
            $permissions[] = "{$model}_create";
        }
        if (str_contains($actions, 'r')) {
            $permissions[] = "{$model}_read";
        }
        if (str_contains($actions, 'u')) {
            $permissions[] = "{$model}_update";
        }
        if (str_contains($actions, 'd')) {
            $permissions[] = "{$model}_delete";
        }
        return $permissions;
    }

    public function allPermissions(string $actions): array
    {
        return array_merge(
            $this->generatePermissions('user', $actions),
            $this->generatePermissions('category', $actions),
            $this->generatePermissions('notification', $actions),
            $this->generatePermissions('report', $actions),
            $this->generatePermissions('settings', $actions),
            $this->generatePermissions('author', $actions),
            $this->generatePermissions('book', $actions),
            $this->generatePermissions('faq', $actions),
            $this->generatePermissions('page', $actions),
            $this->generatePermissions('short_url', $actions),
            $this->generatePermissions('app_notification', $actions),
            $this->generatePermissions('blog_category', $actions),
            $this->generatePermissions('blog', $actions),
            
        );
    }

    public function permissions(): array
    {
        return match($this) {
            self::SUPER_ADMIN => $this->allPermissions('crud'),
            self::ADMIN => $this->allPermissions('crud'),
            self::USER => $this->allPermissions('r'),
        };
    }

    static public function modules(): array
    {
        return [
            ['en' => 'user', 'ar' => 'المستخدم'],
            ['en' => 'category', 'ar' => 'تصنيفات'],
            ['en' => 'notification', 'ar' => 'إشعار'],
            ['en' => 'report', 'ar' => 'تقرير'],
            ['en' => 'settings', 'ar' => 'إعدادات'],
            ['en' => 'author', 'ar' => 'مؤلف'],
            ['en' => 'book', 'ar' => 'كتاب'],
            ['en' => 'faq', 'ar' => 'الأسئلة الشائعة'],
            ['en' => 'page', 'ar' => 'صفحة'],
            ['en' => 'short_url', 'ar' => 'رابط مختصر'],
            ['en' => 'app_notification', 'ar' => 'إشعار التطبيق'],
            ['en' => 'blog_category', 'ar' => 'تصنيف المدونة'],
            ['en' => 'blog', 'ar' => 'مدونة'],
        ];
    }
}

