<?php

namespace App\Helpers;

use SQLite3;
use Exception;
use Illuminate\Support\Facades\Log;

class TextHelper
{
    /**
     * Clean text function using regular expressions.
     *
     * @param string $text
     * @return string
     */
    public static function cleanText($text)
    {
        // Define symbols for cleaning
        $symbols = '@#\$%\^&÷¥®¤|﷽ﷺ¦ø~ªÀ#ﷻ¿îâàëêéèï˶˵';

        // Replace \n with \r
        $text = preg_replace('/\n/u', "\r", $text);

        // Replace §([^§\r\n]*)§ with ˶\1˵
        $text = preg_replace('/§([^§\r\n]*)§/u', '˶$1˵', $text);

        // Replace invisible separator (⁣) with a space
        $text = preg_replace('/⁣/u', ' ', $text);

        // Normalize spaces and strip leading/trailing spaces
        $text = preg_replace('/[ \t]+(?=[^\r\s])/u', ' ', $text);
        $text = preg_replace('/^\s+/u', '', $text);
        $text = preg_replace('/\s+$/u', '', $text);
        $text = preg_replace('/\r\s+/u', "\r", $text);
        $text = preg_replace('/\s+\r/u', "\r", $text);

        // Insert spaces around numbers and Arabic text
        $text = preg_replace('/(?<=[گء-كم-ولىيًٌٍَُِّْ])(\d)/u', ' $1', $text);
        $text = preg_replace('/(\d)(?=[گء-كم-ولىيًٌٍَُِّْ])/u', '$1 ', $text);
        $text = preg_replace('/([گء-كم-ولىيًٌٍَُِّْ]+)([' . preg_quote($symbols, '/') . '])/u', '$1 $2', $text);
        $text = preg_replace('/([' . preg_quote($symbols, '/') . '])([گء-كم-ولىيًٌٍَُِّْ]+)/u', '$1 $2', $text);

        // Adjust spacing around punctuation and symbols
        $text = preg_replace('/ \.\. /', ' ... ', $text);
        $text = preg_replace('/(\S)-/u', '$1 -', $text);
        $text = preg_replace('/-(\S)/u', '- $1', $text);
        $text = preg_replace('/([\(\[\{\«<﴿›]) /u', '$1', $text);
        $text = preg_replace('/ ([\)\]\}\»>﴾‹])/u', '$1', $text);

        // Handle repeated punctuation
        $text = preg_replace('/([؛،؟!:])(\s*\1)+/u', '$1', $text);
        $text = preg_replace('/([؛،؟!:])(\S)/u', '$1 $2', $text);
        $text = preg_replace('/([؛،؟!:]) ([\)\]\}\»>﴾‹])/u', '$1$2', $text);
        $text = preg_replace('/ ([؛،؟!:])/u', '$1', $text);

        // Handle ellipsis
        $text = preg_replace('/(?<!\.) \. \.(?!\.)/u', ' ... ', $text);
        $text = preg_replace('/(\.\.\.+)/u', ' $1 ', $text);
        $text = preg_replace('/ \.(?!\.)(?![\)\]\}\»>﴾‹])/u', '. ', $text);
        $text = preg_replace('/ \.\.\. >/u', '> ... ', $text);
        $text = preg_replace('/([\(\[\{\«<﴿›]) /u', '$1', $text);
        $text = preg_replace('/ ([\)\]\}\»>﴾‹])/u', '$1', $text);

        // Fix special cases
        $text = preg_replace('/\}\s\[¢/u', '}⁣[¢', $text);
        $text = preg_replace('/(\S) \((¬\d+)\)/u', '$1⁣($2)', $text);
        $text = preg_replace('/(\S) \[(¬\d+)\]/u', '$1⁣[$2]', $text);

        // Adjust for special markers ˶˵
        $text = preg_replace('/˶ /u', '˶', $text);
        $text = preg_replace('/ ˵/u', '˵', $text);
        $text = preg_replace('/\. ˵/u', '.˵', $text);
        $text = preg_replace('/˶([^˵\r\n]*)˵/u', '§$1§', $text);

        // Remove redundant newlines
        $text = preg_replace('/(\r\r)\s*(\r)+/u', '$1', $text);

        // Normalize spaces and strip leading/trailing spaces again
        $text = preg_replace('/[ \t]+(?=[^\r\s])/u', ' ', $text);
        $text = preg_replace('/^\s+/u', '', $text);
        $text = preg_replace('/\s+$/u', '', $text);
        $text = preg_replace('/\r\s+/u', "\r", $text);
        $text = preg_replace('/\s+\r/u', "\r", $text);

        return $text;
    }

    /**
     * Process a single SQLite file to clean and update text.
     *
     * @param string $dbPath Path to the SQLite database file.
     */
    public static function processSqliteFile($dbPath)
    {
        try {
            // Open the SQLite database
            $db = new SQLite3($dbPath);

            // Optimize database settings
            $db->exec('PRAGMA journal_mode=Delete;');
            $db->exec('PRAGMA synchronous = NORMAL;');
            $db->exec('PRAGMA cache_size = 10000;');

            // Fetch rows to be cleaned
            $results = $db->query('SELECT id, nass FROM book');

            $updates = [];
            while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
                $cleanedText = self::cleanText($row['nass']);
                $updates[] = ['id' => $row['id'], 'nass' => $cleanedText];
            }

            // Update rows in batches
            $db->exec('BEGIN TRANSACTION;');
            $stmt = $db->prepare('UPDATE book SET nass = :nass WHERE id = :id');

            foreach ($updates as $update) {
                $stmt->bindValue(':nass', $update['nass'], SQLITE3_TEXT);
                $stmt->bindValue(':id', $update['id'], SQLITE3_INTEGER);
                $stmt->execute();
            }
            $db->exec('COMMIT;');
        } catch (Exception $e) {
        } finally {
            if (isset($db)) {
                $db->close();
            }
        }
    }

    public static function cleanText2($text)
    {
        $symbols = ['َ', 'ً', 'ُ', 'ٌ', 'ِ', 'ّ', 'ْ', 'ٍ', '°', '´', 'ٓ', 'ٖ', 'ٗ', 'ٞ', 'ٰ', 'ۖ', 'ۗ', 'ۘ', 'ۙ', 'ۚ', 'ۛ', 'ۜ', '۞', '۠', 'ۡ', 'ۢ', 'ۤ', 'ۥ', 'ۦ', 'ۧ', 'ۨ', '۪', '۬', 'ۭ', '', '±', '¹', '²', '³', '¸', '¨', '£', '¶', '¯', 'ˆ', '©', 'µ', '†', '™'];
        foreach ($symbols as $char) {
            $text = str_replace($char, '', $text);
        }
        $text = preg_replace('/\[¢[^¢\]\r\n]*¢\]/u', ' ', $text);
        $text = preg_replace('/ـ/u', '', $text);
        $text = preg_replace('/[ٱ]/u', 'ا', $text);
        $text = preg_replace('/[^ء-ي __]/u', ' ', $text);
        $text = preg_replace('/\b[ء-ي]\b/u', ' ', $text);
        $text = preg_replace('/\bء([ء-ي]+)\b/u', '$1', $text);
        $text = str_replace('_/u', ' ', $text);
        $text = preg_replace('/\s+/u', ' ', $text);
        return trim($text);
    }

    public static function normalizeArabic($text)
    {
        $text = preg_replace('/[أإآ]/u', 'ا', $text);
        $text = preg_replace('/[ةه]/u', 'ه', $text);
        $text = preg_replace('/[ئ]/u', 'ي', $text);
        $text = preg_replace('/[ؤ]/u', 'و', $text);
        return $text;
    }

    public static function splitNass($nass)
    {
        if (strpos($nass, '__') !== false) {
            [$nass_main, $nass_footnote] = explode('__', $nass, 2);
        } else {
            $nass_main = $nass;
            $nass_footnote = '';
        }
        $nass_main = str_replace('__', '', $nass_main);
        $nass_footnote = str_replace('__', '', $nass_footnote);
        return [trim($nass_main), trim($nass_footnote)];
    }

    public static function loadWordsForPage($db, $text): array
    {
        $words = explode(' ', trim($text));
        //remove empty words and duplicates
        $words = array_unique(array_filter($words));
        $words = array_map(function ($word) {
            $word = trim($word);
            return "'$word'";
        }, $words);
        $words = implode(',', $words);

        $results = $db->query('SELECT word, root FROM words WHERE word in (' . $words . ')');
        $wordRootDict = [];
        while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
            $wordRootDict[$row['word']] = $row['root'];
        }
        return $wordRootDict;
    }

    public static function loadWords($wordsDbPath)
    {
        $db = new SQLite3($wordsDbPath);
        $results = $db->query('SELECT word, root FROM words LIMIT 1000');
        $wordRootDict = [];
        while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
            $wordRootDict[$row['word']] = $row['root'];
        }
        $db->close();
        return $wordRootDict;
    }

    public static function convertToRoots($text, $wordRootDict)
    {
        $words = explode(' ', $text);
        $roots = array_map(function ($word) use ($wordRootDict) {
            $word = trim($word);
            if (empty($word)) {
                return '';
            }
            return trim($wordRootDict[$word] ?? $word);
        }, $words);

        return implode(' ', $roots);
    }

    public static function columnExists($db, $tableName, $columnName)
    {
        $results = $db->query("PRAGMA table_info($tableName)");
        while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
            if ($row['name'] == $columnName) {
                return true;
            }
        }
        return false;
    }

    public static function gatherColumnValues($db, $tableName, $columnName, $idValue)
    {
        $stmt = $db->prepare("SELECT $columnName FROM $tableName WHERE id = :id");
        $stmt->bindValue(':id', $idValue, SQLITE3_INTEGER);
        $results = $stmt->execute();
        $values = [];
        while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
            if (!empty($row[$columnName])) {
                $values[] = $row[$columnName];
            }
        }
        return !empty($values) ? implode(' , ', array_unique($values)) : null;
    }

    public static function createOutputTable($db)
    {
        $db->exec('
            CREATE TABLE IF NOT EXISTS book (
                id INTEGER,
                nass TEXT,
                nass_footnote TEXT,
                part TEXT,
                page TEXT,
                tit TEXT,
                Hno TEXT,
                Mno TEXT,
                nass_root TEXT,
                nass_footnote_root TEXT
            )
        ');
    }

    public static function createNewSqliteFile($sqliteFilePath)
    {
        $tempFilePath = dirname($sqliteFilePath) . '/temp.sqlite';
       
        try {
            Log::info("Starting createNewSqliteFile for: $sqliteFilePath");

            // Rename the original file to temp.sqlite
            if (file_exists($sqliteFilePath)) {
                rename($sqliteFilePath, $tempFilePath);
                Log::info("Renamed oriژginal file to temp.sqlite");
            }

            // Create a new database file with the original name
            $newDb = new SQLite3($sqliteFilePath);
            $newDb->busyTimeout(20000);
            $newDb->exec('PRAGMA journal_mode=Delete;');
            $newDb->exec('PRAGMA synchronous=NORMAL;');
            $newDb->exec('PRAGMA cache_size=10000;');
            Log::info("Created new database file: $sqliteFilePath");

            // Open the renamed temp.sqlite file
            $sourceDb = new SQLite3($tempFilePath);
            Log::info("Opened temp.sqlite file");
            $sourceDb->busyTimeout(20000);
            Log::info("Set busy timeout for temp.sqlite");
            $sourceDb->exec('PRAGMA journal_mode=Delete;');
            Log::info("Set journal mode for temp.sqlite");
            $sourceDb->exec('PRAGMA synchronous=NORMAL;');
            Log::info("Set synchronous mode for temp.sqlite");
            $sourceDb->exec('PRAGMA cache_size=10000;');
            Log::info("Set cache size for temp.sqlite");
            Log::info("Opened temp.sqlite file");

            // Get table structure
            $tableInfo = $sourceDb->query("PRAGMA table_info(book)");
            $columns = [];
            while ($row = $tableInfo->fetchArray(SQLITE3_ASSOC)) {
                $columns[] = $row['name'] . ' ' . $row['type'];
            }
            Log::info("Fetched table structure for 'book'");

            // Create the same table structure in the new database
            $createTableSQL = "CREATE TABLE book ( " . implode(', ', $columns) . " )";
            $newDb->exec($createTableSQL);
            Log::info("Created 'book' table in new database");

            // Copy data with cleaned text
            $batchSize = 100;
            $offset = 0;

            //* book table
            while (true) {
                $query = "SELECT * FROM book LIMIT $batchSize OFFSET $offset";
                $results = $sourceDb->query($query);

                $rowCount = 0;
                $newDb->exec('BEGIN TRANSACTION;');
                Log::info("Started transaction for 'book' table batch at offset $offset");

                while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
                    $rowCount++;

                    // Clean the 'nass' field
                    if (isset($row['nass'])) {
                        $row['nass'] =  self::cleanText($row['nass']);
                    }

                    // Prepare column names and placeholders for INSERT
                    $columnNames = array_keys($row);
                    $placeholders = array_map(function ($col) {
                        return ":$col";
                    }, $columnNames);

                    $insertSQL = "INSERT INTO book (" . implode(', ', $columnNames) . ") 
                                  VALUES (" . implode(', ', $placeholders) . ")";

                    $stmt = $newDb->prepare($insertSQL);

                    // Bind values
                    foreach ($row as $key => $value) {
                        $stmt->bindValue(":$key", $value, is_int($value) ? SQLITE3_INTEGER : SQLITE3_TEXT);
                    }

                    $stmt->execute();
                }

                $newDb->exec('COMMIT;');
                Log::info("Committed transaction for 'book' table batch at offset $offset");

                // If we got fewer rows than the batch size, we're done
                if ($rowCount < $batchSize) {
                    break;
                }

                $offset += $batchSize;
            }

            //* title table
            $titleInfo = $sourceDb->query("PRAGMA table_info(title)");
            $titleColumns = [];
            while ($row = $titleInfo->fetchArray(SQLITE3_ASSOC)) {
                $titleColumns[] = $row['name'] . ' ' . $row['type'];
            }
            $createTitleTableSQL = "CREATE TABLE title ( " . implode(', ', $titleColumns) . " )";
            $newDb->exec($createTitleTableSQL);
            Log::info("Created 'title' table in new database");

            $titleBatchSize = 100;
            $titleOffset = 0;
            while (true) {
                $query = "SELECT * FROM title LIMIT $titleBatchSize OFFSET $titleOffset";
                $results = $sourceDb->query($query);

                $rowCount = 0;
                $newDb->exec('BEGIN TRANSACTION;');
                Log::info("Started transaction for 'title' table batch at offset $titleOffset");

                while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
                    $rowCount++;

                    // Clean the 'title' field
                    if (isset($row['title'])) {
                        $row['title'] = self::cleanText($row['title']);
                    }

                    // Clean the 'tit' field
                    if (isset($row['tit'])) {
                        $row['tit'] = self::cleanText($row['tit']);
                    }

                    // Prepare column names and placeholders for INSERT
                    $columnNames = array_keys($row);
                    $placeholders = array_map(function ($col) {
                        return ":$col";
                    }, $columnNames);

                    $insertSQL = "INSERT INTO title (" . implode(', ', $columnNames) . ") 
                                  VALUES (" . implode(', ', $placeholders) . ")";

                    $stmt = $newDb->prepare($insertSQL);

                    // Bind values
                    foreach ($row as $key => $value) {
                        $stmt->bindValue(":$key", $value, is_int($value) ? SQLITE3_INTEGER : SQLITE3_TEXT);
                    }

                    $stmt->execute();
                }

                $newDb->exec('COMMIT;');
                Log::info("Committed transaction for 'title' table batch at offset $titleOffset");

                // If we got fewer rows than the batch size, we're done
                if ($rowCount < $titleBatchSize) {
                    break;
                }

                $titleOffset += $titleBatchSize;
            }

            // Close database connections
            $sourceDb->close();
            $newDb->close();
            Log::info("Closed database connections");

            // Remove the temp.sqlite file
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
                Log::info("Removed temp.sqlite file");
            }

            Log::info("Successfully completed createNewSqliteFile for: $sqliteFilePath");
            return true;
        } catch (\Exception $e) {
            Log::error("Error in createNewSqliteFile: " . $e->getMessage());

            // Clean up resources
            if (isset($sourceDb)) {
                $sourceDb->close();
            }
            if (isset($newDb)) {
                $newDb->close();
            }

            // Clean up any partially created files
            if (file_exists($sqliteFilePath)) {
                unlink($sqliteFilePath);
            }
            if (file_exists($tempFilePath)) {
                rename($tempFilePath, $sqliteFilePath); // Restore the original file
                Log::info("Restored original file from temp.sqlite");
            }

            throw $e;
        }
    }

    //TODO make sure 
    // .db used for seach 
    public static function createDbForFtsUseAndZipFile($folderPath, $filePath, $bookHashId)
    {
        $wordsDbPath = database_path('words.db');
        $startTime = microtime(true);

        $baseName = basename($filePath);
        $newDbName = pathinfo($baseName, PATHINFO_FILENAME) . '.db';
        $outputDbPath = $folderPath . '/' . $newDbName;

        try {
            // Source database connection
            $conn = new SQLite3($filePath);
            $conn->exec('PRAGMA journal_mode=Delete;');
            $conn->exec('PRAGMA synchronous=NORMAL;');
            $conn->exec('PRAGMA cache_size=10000;');

            // Check column existence
            $hnoExists = self::columnExists($conn, 'title', 'Hno');
            $mnoExists = self::columnExists($conn, 'title', 'Mno');

            // Output database connection
            $outputConn = new SQLite3($outputDbPath);
            $outputConn->exec('PRAGMA journal_mode=Delete;');
            $outputConn->exec('PRAGMA synchronous=NORMAL;');
            $outputConn->exec('PRAGMA cache_size=10000;');
            self::createOutputTable($outputConn);

            // Words database connection
            $dbWords = new SQLite3($wordsDbPath);
            $dbWords->exec('PRAGMA journal_mode=Delete;');

            // Get total count for progress tracking
            $countResult = $conn->query('SELECT COUNT(*) as total FROM book');
            $totalRows = $countResult->fetchArray(SQLITE3_ASSOC)['total'];

            // Process in batches
            $lastTit = '';
            $batchSize = 100;
            $offset = 0;

            $outputConn->exec('BEGIN TRANSACTION;');

            while (true) {
                // Fetch batch of rows
                $query = "SELECT id, nass, part, page FROM book LIMIT $batchSize OFFSET $offset";
                $results = $conn->query($query);

                $rowCount = 0;
                while ($row = $results->fetchArray(SQLITE3_ASSOC)) {
                    $rowCount++;
                    $id = $row['id'];
                    $nass = $row['nass'] ?? '';
                    $part = $row['part'];
                    $page = $row['page'];

                    [$nassMain, $nassFootnote] = self::splitNass($nass);
                    $nassClean = self::cleanText2($nassMain);
                    $nassFootnoteClean = self::cleanText2($nassFootnote);
                    $wordRootDict = self::loadWordsForPage($dbWords, $nassClean);
                    $wordRootDictFooter = self::loadWordsForPage($dbWords, $nassFootnoteClean);

                    $nassRoot = self::convertToRoots($nassClean, $wordRootDict);
                    $nassFootnoteRoot = self::convertToRoots($nassFootnoteClean, $wordRootDictFooter);
                    $nassCleanNormalized = self::normalizeArabic($nassClean);
                    $nassFootnoteCleanNormalized = self::normalizeArabic($nassFootnoteClean);

                    $stmt = $conn->prepare('SELECT tit FROM title WHERE id = :id');
                    $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
                    $titleRow = $stmt->execute()->fetchArray(SQLITE3_ASSOC);
                    $tit = $titleRow ? $titleRow['tit'] : $lastTit;
                    $lastTit = $tit;

                    $hno = $hnoExists ? self::gatherColumnValues($conn, 'title', 'Hno', $id) : null;
                    $mno = $mnoExists ? self::gatherColumnValues($conn, 'title', 'Mno', $id) : null;

                    $replaceEmptyWithNone = function ($value) {
                        return !empty($value) ? $value : null;
                    };

                    $data = [$id, $replaceEmptyWithNone($nassCleanNormalized), $replaceEmptyWithNone($nassFootnoteCleanNormalized), $replaceEmptyWithNone($part), $replaceEmptyWithNone($page), $replaceEmptyWithNone($tit), $replaceEmptyWithNone($hno), $replaceEmptyWithNone($mno), $replaceEmptyWithNone($nassRoot), $replaceEmptyWithNone($nassFootnoteRoot)];

                    $outputStmt = $outputConn->prepare('
                        INSERT INTO book
                        (id, nass, nass_footnote, part, page, tit, Hno, Mno, nass_root, nass_footnote_root)
                        VALUES (:id, :nass, :nass_footnote, :part, :page, :tit, :Hno, :Mno, :nass_root, :nass_footnote_root)
                    ');

                    $outputStmt->bindValue(':id', $data[0], SQLITE3_INTEGER);
                    $outputStmt->bindValue(':nass', $data[1], SQLITE3_TEXT);
                    $outputStmt->bindValue(':nass_footnote', $data[2], SQLITE3_TEXT);
                    $outputStmt->bindValue(':part', $data[3], SQLITE3_TEXT);
                    $outputStmt->bindValue(':page', $data[4], SQLITE3_TEXT);
                    $outputStmt->bindValue(':tit', $data[5], SQLITE3_TEXT);
                    $outputStmt->bindValue(':Hno', $data[6], SQLITE3_TEXT);
                    $outputStmt->bindValue(':Mno', $data[7], SQLITE3_TEXT);
                    $outputStmt->bindValue(':nass_root', $data[8], SQLITE3_TEXT);
                    $outputStmt->bindValue(':nass_footnote_root', $data[9], SQLITE3_TEXT);
                    $outputStmt->execute();

                    // Calculate progress
                    $progress = (($offset + $rowCount) / $totalRows) * 100;
                    if ($rowCount % 100 == 0) {
                        // You could emit progress here if needed
                    }
                }

                // If we got fewer rows than the batch size, we're done
                if ($rowCount < $batchSize) {
                    break;
                }

                // Commit the current batch and start a new transaction
                $outputConn->exec('COMMIT;');
                $outputConn->exec('BEGIN TRANSACTION;');

                // Move to the next batch
                $offset += $batchSize;
            }

            // Commit any remaining changes
            $outputConn->exec('COMMIT;');

            // Close database connections
            $dbWords->close();
            $outputConn->close();
            $conn->close();

            // Create zip file
            $zipFileName = $bookHashId . '.zip';
            $zipFilePath = $folderPath . '/' . $zipFileName;

            $zip = new \ZipArchive();
            if ($zip->open($zipFilePath, \ZipArchive::CREATE) === true) {
                $zip->addFile($filePath, $baseName);
                $zip->addFile($outputDbPath, $newDbName);
                $zip->close();

                // Optional: Remove the intermediary database file
                // unlink($outputDbPath);

                return $zipFilePath;
            } else {
                throw new \Exception("Could not create zip file at $zipFilePath");
            }
        } catch (\Exception $e) {
            // Handle errors and clean up resources
            if (isset($dbWords)) {
                $dbWords->close();
            }
            if (isset($outputConn)) {
                $outputConn->close();
            }
            if (isset($conn)) {
                $conn->close();
            }

            // Clean up any partially created files
            if (file_exists($outputDbPath)) {
                unlink($outputDbPath);
            }
            if (file_exists($zipFilePath)) {
                unlink($zipFilePath);
            }

            // Re-throw or log the exception
            throw $e;
        }
    }

    public static function titleFormat($text)
    {
        // إزالة التشكيلات المحددة من النص
        $tashkeel = ['َ', 'ً', 'ُ', 'ٌ', 'ِ', 'ّ', 'ْ', 'ٍ', '°', '´', 'ٓ', 'ٖ', 'ٗ', 'ٞ', 'ٰ', 'ۖ', 'ۗ', 'ۘ', 'ۙ', 'ۚ', 'ۛ', 'ۜ', '۞', '۠', 'ۡ', 'ۢ', 'ۤ', 'ۥ', 'ۦ', 'ۧ', 'ۨ', '۪', '۬', 'ۭ', '', '±', '¹', '²', '³', '¸', '¨', '£', '¶', '¯', 'ˆ', '©', 'µ', 'î', '†', '™'];
        foreach ($tashkeel as $char) {
            $text = str_replace($char, '', $text);
        }
        $text = preg_replace('/\[¢.*?¢\]/u', ' ', $text);

        // إزالة الحروف غير العربية أو "_" مع الحفاظ على الحروف العثمانية
        $text = preg_replace('/[^ء-ي ]/u', ' ', $text);

        $text = preg_replace('/(?<=\s|^)[ء-ي](?=\s|$)/u', '', $text);

        // إزالة الكاشيدة
        $text = preg_replace('/ـ/u', '', $text);

        // replace أ with ا
        $text = preg_replace('/[ٱ]/u', 'ا', $text);
        $text = preg_replace('/[إأآ]/u', 'ا', $text);
        $text = preg_replace('/[ة]/u', 'ه', $text);
        $text = preg_replace('/[ى]/u', 'ي', $text);
        $text = preg_replace('/[ؤ]/u', 'و', $text);

        // إزالة احرف غير مرئية
        // $text = preg_replace('/[\u200B-\u200F\u2060-\u206F]/u', ' ', $text);

        // استبدال بعض الحروف
        $text = preg_replace('/(?<=\s|^)ء/u', '', $text);

        // إزالة المسافات الزائدة
        $text = preg_replace('/\s+/u', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Format text for search purposes.
     *
     * @param string $text
     * @param bool $disableExchangeLettersWithHamza
     * @param bool $disableStar
     * @return string
     */
    public static function searchForTextFormatter($text, $disableExchangeLettersWithHamza = false, $disableStar = false)
    {
        $tashkeel = ['َ', 'ً', 'ُ', 'ٌ', 'ِ', 'ّ', 'ْ', 'ٍ', '°', '´', 'ٓ', 'ٖ', 'ٗ', 'ٞ', 'ٰ', 'ۖ', 'ۗ', 'ۘ', 'ۙ', 'ۚ', 'ۛ', 'ۜ', '۞', '۠', 'ۡ', 'ۢ', 'ۤ', 'ۥ', 'ۦ', 'ۧ', 'ۨ', '۪', '۬', 'ۭ', '', '±', '¹', '²', '³', '¸', '¨', '£', '¶', '¯', 'ˆ', '©', 'µ', 'î', '†', '™'];

        if ($disableStar) {
            $text = str_replace('*', 'نججم', $text);
        }

        // Remove specific diacritics from the text
        foreach ($tashkeel as $char) {
            $text = str_replace($char, '', $text);
        }
        $text = preg_replace('/ـ/u', '', $text);

        // Replace certain letters
        if (!$disableExchangeLettersWithHamza) {
            $text = preg_replace('/[ؤ]/u', 'و', $text);
            $text = preg_replace('/[ئ]/u', 'ي', $text);
            $text = preg_replace('/[أإآٱ]/u', 'ا', $text);
            $text = str_replace('ة', 'ه', $text);
        }
        $text = preg_replace('/[ٱ]/u', 'ا', $text);

        $text = preg_replace('/\[¢.*?¢\]/u', ' ', $text);

        // Remove non-Arabic characters or "_" while preserving Ottoman characters
        $text = preg_replace('/[^ء-ي _]/u', ' ', $text);

        // Remove single-character words containing "ـ"
        $text = preg_replace('/(?<=\s|^)[ء-ي](?=\s|$)/u', '', $text);

        $text = preg_replace('/(?<=\s|^)ء/u', '', $text);

        // Handle underscores "_"
        $text = preg_replace('/_+/u', '_', $text);

        // Remove extra spaces
        $text = preg_replace('/\s+/u', ' ', $text);
        $text = trim($text);

        if ($disableStar) {
            $text = str_replace('نججم', '*', $text);
        }

        return $text;
    }
}
