<?php

namespace App\Helpers;

use SQLite3;
use Exception;

class BookContentStyleHelper
{
    public static $ayatColor = '#00AA00';
    public static $ayatGaloon = '#00AA00';
    public static $numbersColor = '#be0000';
    public static $ezzoColor = '#000099';
    public static $hashyaColor = '#800000';
    public static $matnColor = '#0000fa';
    public static $titleColor = '#800000';
    public static $symbolsColor = '#800000';
    public static $narratorsColor = '#ff00ff';
    public static $footerColor = '#464646';

    public static $ayatColorDark = '#00AA00';
    public static $ayatGaloonDark = '#00AA00';
    public static $numbersColorDark = '#be0000';
    public static $ezzoColorDark = '#000099';
    public static $hashyaColorDark = '#800000';
    public static $matnColorDark = '#0000fa';
    public static $titleColorDark = '#800000';
    public static $symbolsColorDark = '#800000';
    public static $narratorsColorDark = '#ff00ff';
    public static $footerColorDark = '#464646';

    public static $hiddenSymbole = ' ';
    public static $hs = ' ';

    public static function applyStyle($contentModel, $titles)
    {
        $text = $contentModel->nass ?? '';
        $text = self::convertEnglishNumbersToArabic($text);
        $text = self::handleAyat($text);
        $text = self::handleAyatGaloon($text);
        $text = self::handleSelectedWords($text);
        $text = self::handleEzzo($text);
        $text = self::handleMatn($text);
        $text = self::handleSubNumbers($text);
        $text = self::handleTitles($text, $contentModel, $titles);
        $text = self::handleSpacesAndOther($text);
        $text = self::handleSymbols($text);
        $text = self::convertToLines($text);

        return $text;
    }

    public static function convertToLines($text)
    {
        $theList = [];
        $list = explode("\r", $text);
        foreach ($list as $line) {
            $line = explode("\n", $line);
            foreach ($line as $l) {
                if (empty(trim($l)) || trim($l) == ' ') {
                    continue;
                }
                $theList[] = $l;
            }
        }
        $text = '';
        $isfooter = false;
        $noramlStyle = 'style="margin-top:1px;margin-bottom:1px"';
        $footerStyle = 'style="text-indent: -20px;padding-right: 20px;font-size: 0.8em;margin-top:1px;margin-bottom:1px;color:' . self::$footerColor . ';text-align: justify;"';
        // "; padding-right: 30px; padding-left: 30px;
        foreach ($theList as $line) {
            //check if line container _
            if (strpos($line, '_') !== false) {
                $pattern = '/__+/';
                $replacement = '<hr style="margin-top: 12px;margin-bottom: 12px;border: 0;height: 1px;background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);">';
                $line = preg_replace($pattern, $replacement, $line);
                $isfooter = true;
            }

            $line = self::handleIfLineStartWithNumberAndDash($line);
            $line = self::handleBoldTextAndBesm($line);
            //check if line already has a p tag
            if (strpos($line, '<p') !== false) {
                $text .= $line;
                continue;
            }
            if (empty(trim($line)) || trim($line) == ' ' || trim($line) == ' ') {
                continue;
            }
            $spaceForStartEachP = $isfooter ? '' : '&nbsp&nbsp';
            $text .= '<p ' . ($isfooter ? $footerStyle : $noramlStyle) . '>' . $spaceForStartEachP . $line . '</p>';
        }

        return $text;
    }

    public static function convertEnglishNumbersToArabic($text)
    {
        $text = str_replace(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'], $text);
        return $text;
    }

    //* Ayat RegExp(r'\<([^\>]*)\>')
    public static function handleAyat($text)
    {
        $pattern = '/<(.*?)>/';
        $replacement = '<span style="color: ' . self::$ayatColor . ';font-family: Ziydia-Q;">$1</span>';
        return preg_replace($pattern, $replacement, $text);
    }
    //* Ayat Galoon RegExp(r'(\﴿[^\﴾]*\﴾)')
    public static function handleAyatGaloon($text)
    {
        $pattern = '/﴿(.*?)﴾/';
        $replacement = '<span style="color: ' . self::$ayatColor . ';font-family: Ziydia-QL;">$1</span>';
        return preg_replace($pattern, $replacement, $text);
    }

    //* handleSelectedWords
    public static function handleSelectedWords($text)
    {
        $pattern2 = '/›(.*?)‹/';
        $replacement2 = '<span style="font-family:Ziydia-Bold;">$1</span>';
        $text = preg_replace($pattern2, $replacement2, $text);

        return $text;
    }

    //* Ezzo RegExp(r'\[¢([^\¢]*)\¢]')
    public static function handleEzzo($text)
    {
        $pattern = '/\[¢(.*?)\¢]/';
        $replacement = '<span style="font-size: 0.8em;">[$1]</span>';
        return preg_replace($pattern, $replacement, $text);
    }

    //* handle titles
    public static function handleTitles($text, $contentModel, $titles)
    {
        $nass = $text;
        $titlesInPage = [];
        foreach ($titles as $title) {
            if ($contentModel != null && $title['title_id'] == $contentModel->page_id) {
                $titlesInPage[] = $title['title'];
            }
        }
        //check each line in text if matches any title in titlesList the make it bold and red
        // convert text to array of lines
        $lines = explode("\n", str_replace("\r", "\n", $nass ?? ''));
        // loop through each line
        foreach ($lines as $key => $line) {
            $lineFormatted = TextHelper::titleFormat($line);
            foreach ($titlesInPage as $title) {
                $titleFormatted = TextHelper::titleFormat($title);
                //check if titleformatted is equal to lineformatted
                if ($titleFormatted == $lineFormatted) {
                    $lines[$key] = '<p class="border-r-4 px-2"  style="border-radius: 0px 3px 3px 0px;font-family:Ziydia-Bold;color: ' . self::$titleColor . ';">' . $line . '</p>';
                }
            }
        }
        return implode("\n", $lines);
    }

    //* handle sub numbers الحاشية
    // RegExp(r'\(¬([٠-٩]+)\)')
    // RegExp(r'\[¬([٠-٩]+)\]')
    public static function handleSubNumbers($text)
    {
        $pattern = '/\(¬([٠-٩]+)\)/';
        $pattern2 = '/\[¬([٠-٩]+)\]/';
        $replacement1 = '<a href="javascript: scrollTo(\'hashya1\')"><sup style="color: ' . self::$hashyaColor . ';">(⁣$1)</sup></a>';
        $replacement2 = '<sup style="color: ' . self::$hashyaColor . ';">[⁣$1]</sup>';

        $text = preg_replace($pattern, $replacement1, $text);
        $text = preg_replace($pattern2, $replacement2, $text);

        return $text;
    }

    //*Matn RegExp(r'§([^§]*)§')
    public static function handleMatn($text)
    {
        $pattern = '/§(.*?)§/';
        $replacement = '<span style="color: ' . self::$matnColor . ';">$1</span>';
        return preg_replace($pattern, $replacement, $text);
    }

    //* Symbols RegExp(r'((\.\.)+(\.+))')
    public static function handleSymbols($text)
    {
        //new line in text = \n or \r\n or \r
        //* for book حاشية الدسوقي على مختصر المعاني only
        $pattern02 = '/((• - - -)|(- - - •))/';
        $replacement02 = '-';
        $text = preg_replace($pattern02, $replacement02, $text);

        $pattern01 = '/(^|\n)((\-\s*)+(\-+)\n)/';
        $replacement01 = '<hr style="margin-top: 12px;margin-bottom: 12px;border: 0;height: 1px;background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);">' . "\n";
        $text = preg_replace($pattern01, $replacement01, $text);

        $pattern0 = '/(^|\n)((\.\.)+(\.+)\n)/';
        $replacement0 = '<hr style="margin-top: 12px;margin-bottom: 12px;border: 0;height: 1px;background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);">' . "\n";
        $text = preg_replace($pattern0, $replacement0, $text);

        $pattern = '/((\.\.)+(\.+))/';
        $replacement = '<span  style="color: ' . self::$symbolsColor . ';">$1</span>';
        $text = preg_replace($pattern, $replacement, $text);

        $pattern2 = '/µ|î|â|à|ë|ê|é|è|ï|†|™|÷|&|@|¥|\^|®|¤|¦|\||°|±|¹|²|³|¸|´|¨|£|¶|¯|ˆ|©|·|ø|¿|ª|À|~|\$|ﷻ|ﷺ|﷽/';
        $replacement2 = '<span  style="color: ' . self::$symbolsColor . ';">$0</span>';
        $text = preg_replace($pattern2, $replacement2, $text);

        //style symoble # where after # there is no number 0 to 9 and nor a to f
        $pattern3 = '/#(?![0-9a-fA-F])/';
        $replacement3 = '<span style="color: ' . self::$symbolsColor . ';">$0</span>';
        $text = preg_replace($pattern3, $replacement3, $text);

        return $text;
    }

    //* handle handleNarrators RegExp(r'(§[^§/n]*§)')
    public static function handleNarrators($text)
    {
        $pattern = '/(§[^§\n]*§)/';
        $replacement = '<span style="color: ' . self::$narratorsColor . ';">$1</span>';
        return preg_replace($pattern, $replacement, $text);
    }

    //*handleIfLineStartWithNumberAndDash RegExp(r'(?:^\s*|\n)\s*([٠-٩]+\s-|\([٠-٩]+\)|\[[٠-٩]+\]|\(\*\))')
    public static function handleIfLineStartWithNumberAndDash($text)
    {
        // String text = item.text ?? '';
        // text = text.replaceAllMapped(RegExp(r'(?:^\s*|\n)\s*([٠-٩]+\s-|\([٠-٩]+\)|\[[٠-٩]+\]|\(\*\))'), (match) {
        //   return '✅Red${match.group(0)!}✅';
        // });
        // text = text.replaceAllMapped(RegExp(r'(?:^\s*|\n)\s*-'), (match) {
        //   return '✅Red${match.group(0)!}✅';
        // });
        // text = text.replaceAllMapped(RegExp(r'\[\*\]'), (match) {
        //   return '✅Red${match.group(0)!}✅';
        // });

        // text = text.replaceAllMapped(RegExp(r'(?:^\s*|\r)\s*='), (match) {
        //   return '✅Red${match.group(0)!}✅';
        // });
        // text = text.replaceAllMapped(RegExp(r'=\s*(?:$|\r)'), (match) {
        //   return '✅Red${match.group(0)!}✅';
        // });

        $pattern = '/^\s*([٠-٩]+\s-|\([٠-٩]+\)|\[[٠-٩]+\]|\(\*\))/';
        $replacement = '<span style="color: ' . self::$numbersColor . ';">$0</span>';
        $text = preg_replace($pattern, $replacement, $text);

        $pattern2 = '/(?:^\s*|\r\n)\s*-/';
        $replacement2 = '<span style="color: ' . self::$numbersColor . ';">$0</span>';
        $text = preg_replace($pattern2, $replacement2, $text);

        $pattern3 = '/\[\*\]/';
        $replacement3 = '<span style="color: ' . self::$numbersColor . ';">$0</span>';
        $text = preg_replace($pattern3, $replacement3, $text);

        $pattern4 = '/(?:^\s*|\r\n)\s*=/';
        $replacement4 = '<span style="color: ' . self::$numbersColor . ';">$0</span>';
        $text = preg_replace($pattern4, $replacement4, $text);

        $pattern5 = '/=\s*(?:$|\r\n)/';
        $replacement5 = '<span style="color: ' . self::$numbersColor . ';">$0</span>';
        $text = preg_replace($pattern5, $replacement5, $text);

        return $text;
    }

    public static function handleBoldTextAndBesm($line)
    {
        $line = preg_replace('/(^\s*[گء-كم-ولىيًٌٍَُِّْ ]{0,30}\S[:؛؟!])/u', '<span style="font-family:Ziydia-Bold">$1</span>', $line);
        if (strpos($line, '﷽') !== false && !preg_match('/[ء-ي]/u', $line)) {
            $line = '<div style="text-align: center;">' . $line . '</div>';
        }

        return $line;
    }

    public static function handleSpacesAndOther($text)
    {
        // remove any signs that are not needed
        $text = str_replace(['¢', '¬', '§', '›', '‹'], ' ', $text);

        return $text;
    }
}
