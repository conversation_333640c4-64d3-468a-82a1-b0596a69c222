<?php

namespace App\Helpers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class QueryCacheHelper
{
    /**
     * Cache a query result for a specified duration
     *
     * @param Builder $query The query to execute and cache
     * @param string $key The cache key
     * @param int $seconds Cache duration in seconds
     * @param array $tags Cache tags for better management (requires Redis or Memcached)
     * @return mixed The query result
     */
    public static function remember(Builder $query, string $key, int $seconds = 3600, array $tags = [])
    {
        $cacheStore = Cache::store();
        
        // Use tags if available (Redis or Memcached drivers support tags)
        if (method_exists($cacheStore, 'tags') && count($tags) > 0) {
            $cacheStore = $cacheStore->tags($tags);
        }
        
        return $cacheStore->remember($key, $seconds, function () use ($query) {
            return $query->get();
        });
    }
    
    /**
     * Cache a paginated query result
     *
     * @param Builder $query The query to execute and cache
     * @param string $keyPrefix Cache key prefix
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param int $seconds Cache duration in seconds
     * @param array $tags Cache tags
     * @return mixed Paginated result
     */
    public static function rememberPaginated(
        Builder $query, 
        string $keyPrefix, 
        int $page, 
        int $perPage = 15, 
        int $seconds = 3600, 
        array $tags = []
    ) {
        $key = "{$keyPrefix}_page{$page}_per{$perPage}";
        $cacheStore = Cache::store();
        
        if (method_exists($cacheStore, 'tags') && count($tags) > 0) {
            $cacheStore = $cacheStore->tags($tags);
        }
        
        return $cacheStore->remember($key, $seconds, function () use ($query, $perPage, $page) {
            return $query->paginate($perPage, ['*'], 'page', $page);
        });
    }
    
    /**
     * Cache a model find by ID operation
     *
     * @param string $model The model class name
     * @param mixed $id The ID to find
     * @param array $columns Columns to select
     * @param int $seconds Cache duration in seconds
     * @return Model|null The model instance or null
     */
    public static function rememberFind(string $model, $id, array $columns = ['*'], int $seconds = 3600)
    {
        $key = strtolower(class_basename($model)) . "_find_{$id}";
        
        return Cache::remember($key, $seconds, function () use ($model, $id, $columns) {
            return $model::find($id, $columns);
        });
    }
    
    /**
     * Clear cache by tags
     *
     * @param array $tags Tags to clear
     * @return bool Success indicator
     */
    public static function clearByTags(array $tags)
    {
        if (method_exists(Cache::store(), 'tags')) {
            Cache::store()->tags($tags)->flush();
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate a query hash based on the query to use as cache key
     *
     * @param Builder $query The query builder instance
     * @param string $prefix Optional prefix for the key
     * @return string The generated cache key
     */
    public static function generateQueryCacheKey(Builder $query, string $prefix = 'query')
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();
        
        return $prefix . '_' . md5($sql . serialize($bindings));
    }
}