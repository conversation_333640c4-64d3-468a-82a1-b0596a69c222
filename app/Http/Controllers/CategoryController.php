<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StoreCategoryRequest;
use App\Http\Requests\UpdateCategoryRequest;
use App\Http\Resources\CategoryResource;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $parentCategories = Category::whereNull('parent_id')->get();
        $analyticsData = [];
        return view('modules.category.index', compact('parentCategories', 'analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('categories.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCategoryRequest $request)
    {
        $data = $request->all();
        $data['created_by'] = auth()->id();
        $category = Category::create($data);
        $category = CategoryResource::make($category);

        cache()->put('categories', Category::all());
        return back()->with('success', 'تم إنشاء القسم بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        return redirect()->route('categories.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        return redirect()->route('categories.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCategoryRequest $request, Category $category)
    {
        if (count($category->children) > 0 && $request->parent_id != null) {
            return back()->with('error', 'القسم لديه أقسام فرعية لا يمكن تحويلة لقسم فرعي');
        }
        $data = $request->validated();
        $data['updated_by'] = auth()->id();
        $category->update($data);
        $category = CategoryResource::make($category);

        cache()->put('categories', Category::all());
        return back()->with('success', 'تم تحديث القسم بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        if ($category->children->count() > 0) {
            return back()->with('error', 'القسم لديه أقسام فرعية لا يمكن حذفة');
        }
        $category->deleted_by = auth()->id();
        $category->save();
        $category->delete();
        Helpers::notifyAdmins([
            'title' => 'تم حذف قسم',
            'message' => 'القسم ' . $category->title . ' تم حذف بواسطة ' . auth()->user()->name,
            'href' => route('categories.index'),
        ]);
        cache()->put('categories', Category::all());
        return back()->with('success', 'تم حذف القسم بنجاح');
    }
}
