<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Front\BookPageController;
use App\Models\Book;
use App\Models\Author;
use Illuminate\Http\Request;
use App\Http\Requests\StoreBookRequest;
use App\Http\Requests\UpdateBookRequest;
use App\Jobs\ProcessBook;
use App\Models\BookCategories;
use App\Models\BookContent;
use App\Models\BookFiles;
use App\Models\BookSearch;
use App\Models\BookTitle;
use App\Models\Category;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class BookController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->has('itemsPerPage') ? $request->itemsPerPage : 12;
        $allBooks = Book::get(['title','id','display_on_home']);

        // Define cache key for book statistics
        $cacheKey = 'book_statistics';

        // Check if we have cached statistics
        if (Cache::has($cacheKey)) {
            $cachedData = Cache::get($cacheKey);
            $analyticsData = $cachedData['analytics_data'];
            $cacheInfo = $cachedData['cache_info'];
        } else {
            // Calculate book statistics
            $booksCount = Book::count();
            $booksWithSqlite = Book::whereNotNull('sqlite')->count();
            $booksWithoutSqlite = $booksCount - $booksWithSqlite;

            // Get books with PDF files
            $booksWithPdf = Book::whereHas('files')->count();
            $booksWithoutPdf = $booksCount - $booksWithPdf;

            // Get books available on website, application, and private books
            $booksOnWebsite = Book::where('website', 1)->count();
            $booksOnApplication = Book::where('application', 1)->count();
            $privateBooks = Book::whereNotNull('private_users')->where('private_users', '!=', '')->count();


            $analyticsData = [
                [
                    'title' => 'عدد الكتب',
                    'description' => 'عدد الكتب الكلي',
                    'value' => $booksCount,
                    'percent' => 0,
                    'icon' => 'book',
                    'color' => 'primary',
                ],
                [
                    'title' => 'كتب متاحة علي الموقع',
                    'description' => 'عدد الكتب المتاحة على الموقع',
                    'value' => $booksOnWebsite,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksOnWebsite) / $booksCount, 2) : 0,
                    'icon' => 'world',
                    'color' => 'success',
                ],
                [
                    'title' => 'كتب متاحة علي التطبيق',
                    'description' => 'عدد الكتب المتاحة على التطبيق',
                    'value' => $booksOnApplication,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksOnApplication) / $booksCount, 2) : 0,
                    'icon' => 'device-mobile',
                    'color' => 'info',
                ],
                [
                    'title' => 'كتب خاصة',
                    'description' => 'عدد الكتب المقيدة بمستخدمين محددين',
                    'value' => $privateBooks,
                    'percent' => $booksCount > 0 ? number_format((100 * $privateBooks) / $booksCount, 2) : 0,
                    'icon' => 'lock',
                    'color' => 'warning',
                ],
                [
                    'title' => 'كتب مع SQLite',
                    'description' => 'عدد الكتب التي تحتوي على ملف SQLite',
                    'value' => $booksWithSqlite,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksWithSqlite) / $booksCount, 2) : 0,
                    'icon' => 'database',
                    'color' => 'success',
                ],
                [
                    'title' => 'كتب بدون SQLite',
                    'description' => 'عدد الكتب التي لا تحتوي على ملف SQLite',
                    'value' => $booksWithoutSqlite,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksWithoutSqlite) / $booksCount, 2) : 0,
                    'icon' => 'database-off',
                    'color' => 'warning',
                ],
                [
                    'title' => 'كتب مع PDF',
                    'description' => 'عدد الكتب التي تحتوي على ملف PDF',
                    'value' => $booksWithPdf,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksWithPdf) / $booksCount, 2) : 0,
                    'icon' => 'file-text',
                    'color' => 'info',
                ],
                [
                    'title' => 'كتب بدون PDF',
                    'description' => 'عدد الكتب التي لا تحتوي على ملف PDF',
                    'value' => $booksWithoutPdf,
                    'percent' => $booksCount > 0 ? number_format((100 * $booksWithoutPdf) / $booksCount, 2) : 0,
                    'icon' => 'file-off',
                    'color' => 'danger',
                ],
            ];

            // Create cache information
            $now = Carbon::now();
            $expiryDate = $now->copy()->addDays(7);

            $cacheInfo = [
                'created_at' => $now,
                'expires_at' => $expiryDate,
            ];

            // Store data in cache for 7 days
            Cache::put($cacheKey, [
                'analytics_data' => $analyticsData,
                'cache_info' => $cacheInfo
            ], $expiryDate);
        }

        return view('modules.book.index', compact('allBooks', 'cacheInfo'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = $this->getCreateFormData();
        return view('modules.book.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBookRequest $request)
    {
        $book = $this->createBook($request);
        $this->handleBookImage($book, $request);
        $this->handleBookCategories($book, $request);
        if ($request->has('file_path') && $request->file_path != null) {
            ProcessBook::dispatch($book, $request->enable_update_date == 'on' ? true : false);
        }

        return redirect()->route('books.index')->with('success', 'تم إضافة الكتاب بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Book $book)
    {
        return view('modules.book.show', compact('book'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Book $book)
    {
        $data = $this->getEditFormData($book);
        return view('modules.book.update', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBookRequest $request, Book $book)
    {
        $data = $this->prepareUpdateData($request, $book);
        $book->update($data);
        $this->handleBookCategories($book, $request);

        return redirect()->route('books.index')->with('success', 'تم تعديل الكتاب بنجاح');
    }

    /**
     * Display PDF files for the specified book.
     */
    public function pdfs(Book $book)
    {
        $pdfs = $book->files;
        return view('modules.book.pdfs', compact('book', 'pdfs'));
    }

    public function pdfByPart(Book $book, $part)
    {

        $pdf = $book->files;
        foreach ($pdf as $index => $pdf) {
            if ($index + 1 == $part) {

                $response = Http::withHeaders([
                    'Accept' => 'application/pdf',
                ])->get($pdf->file_path);

                return response($response->body(), 200)
                    ->header('Content-Type', 'application/pdf');
            }
        }
    }

    /**
     * Store a new PDF file for the book.
     */
    public function storePdf(Request $request)
    {
        $request->validate([
            'book_id' => 'required|exists:books,id',
            'file' => 'required|mimes:pdf',
            'part' => 'required|integer',
        ]);

        $book = Book::find($request->book_id);
        $this->storePdfFile($book, $request);

        return redirect()->route('book.pdfs', $book->id)->with('success', 'تم إضافة الكتاب بنجاح');
    }

    /**
     * Download a PDF file.
     */
    public function downloadPdf($id)
    {
        $pdf = BookFiles::find($id);
        return response()->download(storage_path('app/public/books/' . $pdf->file_name));
    }

    /**
     * Update a PDF file.
     */
    public function updatePdf(Request $request, BookFiles $bookFile)
    {
        $request->validate([
            'book_id' => 'required|exists:books,id',
            'file' => 'sometimes|mimes:pdf',
            'part' => 'required|integer',
        ]);

        $this->updatePdfFile($bookFile, $request);

        return redirect()->back()->with('success', 'تم تعديل الكتاب بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Book $book)
    {
        $this->deleteBookData($book);
        return back()->with('success', 'تم حذف الكتاب بنجاح');
    }

    public function deletePdf(BookFiles $bookFile)
    {
        //*delete file from spaces
        Storage::disk('spaces')->delete('books/' . $bookFile->book_id . '/' . $bookFile->file_name);
        $bookFile->delete();
        return back()->with('success', 'تم حذف الكتاب بنجاح');
    }

    /**
     * Update the book's updated_at timestamp
     */
    public function updateDate(Request $request, Book $book)
    {
        $request->validate([
            'updated_at' => 'required|date',
        ]);

        $book->timestamps = false; // Prevent auto-updating the timestamps
        $book->updated_at = $request->updated_at;
        $book->save();

        return redirect()->route('books.index')->with('success', 'تم تحديث تاريخ الكتاب بنجاح');
    }

    /**
     * Update the book's created_at timestamp
     */
    public function updateCreatedDate(Request $request, Book $book)
    {
        $request->validate([
            'created_at' => 'required|date',
        ]);

        $book->timestamps = false; // Prevent auto-updating the timestamps
        $book->created_at = $request->created_at;
        $book->save();

        return redirect()->route('books.index')->with('success', 'تم تحديث تاريخ إضافة الكتاب بنجاح');
    }

    /**
     * Get filtered books based on request parameters
     */
    public static function getFilteredBooks(Request $request, $perPage)
    {
        return Book::where(function ($query) use ($request) {
            if ($request->id != null) {
                $query->where('id', $request->id);
            }
            if ($request->search != null) {
                $query->where('title', 'LIKE', '%' . $request->search . '%')->orWhere('author', 'LIKE', '%' . $request->search . '%');
            }
            if ($request->datefilter != null) {
                $this->applyDateFilter($query, $request->datefilter);
            }
        })
            ->with('author')
            ->orderBy('created_at', 'DESC')
            ->paginate($perPage);
    }

    /**
     * Apply date filter to query
     */
    private function applyDateFilter($query, $dateFilter)
    {
        try {
            $dates = explode(' - ', $dateFilter);
            $startDate = Carbon::createFromFormat('d/m/Y', $dates[0])->format('Y-m-d');
            $endDate = Carbon::createFromFormat('d/m/Y', $dates[1])->format('Y-m-d');
            $query->whereDate('created_at', '>=', $startDate)->whereDate('created_at', '<=', $endDate);
        } catch (\Exception $e) {
            // Handle invalid date format
        }
    }

    /**
     * Get data for create form
     */
    private function getCreateFormData()
    {
        return [
            'authors' => Author::all(),
            'categories' => Category::all(),
            'users' => User::all('id', 'name', 'email'),
        ];
    }

    /**
     * Get data for edit form
     */
    private function getEditFormData(Book $book)
    {
        return [
            'book' => $book,
            'authors' => Author::all(),
            'categories' => Category::all(),
            'users' => User::all('id', 'name', 'email'),
        ];
    }

    /**
     * Create a new book
     */
    private function createBook(StoreBookRequest $request)
    {
        $privateUsers = $this->getPrivateUsers($request);
        $data = [
            'title' => $request->title,
            'image' => $this->getImagePath($request->image_path),
            'author_id' => $request->author_id,
            'added_by' => auth()->id(),
            'summary' => $request->summary,
            'private_users' => $privateUsers,
            'application' => $request->application == 'on' ? 1 : 0,
            'website' => $request->website == 'on' ? 1 : 0,
            'display_on_home' => $request->display_on_home == 'on' ? 1 : 0,
            'updated_at' => Carbon::create(1999, 1, 1, 0, 0, 0),
            'created_at' => now(),
        ];

        if ($request->has('file_path') && $request->file_path != null) {
            $fileSize = $this->getFileSize($request->file_path);
            $readableSize = $this->formatFileSize($fileSize);

            $bookFileFullPath = $this->getBookFilePath($request->file_path);
            $data['sqlite'] = $bookFileFullPath;
            $data['sqlite_size'] = $readableSize;
        }

        return Book::create($data);
    }

    /**
     * Handle book image upload and storage
     */
    private function handleBookImage(Book $book, $request)
    {
        if ($request->has('image_path') && $request->image_path != null) {
            $currentImage = $this->getImageFileName($book->image);
            $file = Storage::disk('public')->get($currentImage);

            Storage::disk('spaces')->put('books/' . $book->id . '/' . $book->hashId . '.jpg', $file, 'public');

            $path = Storage::disk('spaces')->url('books/' . $book->id . '/' . $book->hashId . '.jpg');

            $book->timestamps = false;
            $book->image = $path;
            $book->save();
            if ($currentImage != null) {
                Storage::disk('public')->delete($currentImage);
            }
        }
    }

    /**
     * Handle book categories
     */
    private function handleBookCategories(Book $book, $request)
    {
        BookCategories::where('book_id', $book->id)->delete();
        foreach ($request->categories as $category) {
            BookCategories::create([
                'book_id' => $book->id,
                'category_id' => $category,
            ]);
        }
    }

    /**
     * Prepare data for book update
     */
    private function prepareUpdateData(UpdateBookRequest $request, Book $book)
    {
        $data = [
            'title' => $request->title,
            'author_id' => $request->author_id,
            'summary' => $request->summary,
            'private_users' => $this->getPrivateUsers($request),
            'application' => $request->application == 'on' ? 1 : 0,
            'website' => $request->website == 'on' ? 1 : 0,
            'display_on_home' => $request->display_on_home == 'on' ? 1 : 0,
            'enable_update_date' => $request->enable_update_date == 'on' ? 1 : 0,
        ];

        $processBook = false;
        if ($request->has('file_path') && $request->file_path != null) {
            $this->handleFileUpdate($book, $request, $data);
            $processBook = true;
        }

        if ($request->has('image_path') && $request->image_path != null) {
            $this->handleImageUpdate($book, $request, $data);
        }

        if ($processBook) {
            ProcessBook::dispatch($book, $request->enable_update_date == 'on' ? true : false);
        } else {
            if ($request->enable_update_date == 'on') {
                $data['updated_at'] = now();
            }
        }

        return $data;
    }

    /**
     * Delete book and related data
     */
    private function deleteBookData(Book $book)
    {
        BookContent::where('book_id', $book->id)->delete();
        BookTitle::where('book_id', $book->id)->delete();
        BookSearch::where('book_id', $book->id)->chunkById(100, function ($records) {
            $ids = $records->pluck('id');
            BookSearch::whereIn('id', $ids)->delete();
            usleep(20);
        });
        //* delete book files
        Storage::disk('spaces')->deleteDirectory('books/' . $book->id);
        //* clear cache for book reads page
        BookPageController::clearSpecificBookCache($book->hashId);
        //* clear/purge cache from CDN
        $cachePurgeController = new CachePurgeController();
        $cachePurgeController->purgeBookCache($book->id);

        $book->update(['updated_at' => now()]);
        $book->delete();
    }

    /**
     * Get private users from request
     */
    private function getPrivateUsers($request)
    {
        if ($request->has('private_users') && $request->private_users != null && ($request->private_users_switch == 'on' || $request->private_users_switch == true)) {
            return implode(',', $request->private_users);
        }
        return null;
    }

    /**
     * Format file size to human readable format
     */
    private function formatFileSize($fileSize)
    {
        $fileSizeInKb = $fileSize / 1024;
        $fileSizeInMb = $fileSizeInKb / 1024;
        return $fileSizeInMb > 1 ? round($fileSizeInMb, 2) . ' MB' : round($fileSizeInKb, 2) . ' KB';
    }

    /**
     * Get file size in bytes
     */
    private function getFileSize($filePath)
    {
        return round(Storage::disk('public')->size(str_replace("\"", '', $filePath)));
    }

    /**
     * Get image file name from path
     */
    private function getImageFileName($imagePath)
    {
        $currentImage = explode('/', $imagePath);
        //* remove "
        $currentImage = str_replace("\"", '', $currentImage);
        return end($currentImage);
    }

    /**
     * Get book file path
     */
    private function getBookFilePath($filePath)
    {
        return env('APP_URL') . '/storage/books/' . str_replace("\"", '', $filePath);
    }

    /**
     * Get image path
     */
    private function getImagePath($imagePath)
    {
        return env('APP_URL') . '/storage/books/' . str_replace("\"", '', $imagePath);
    }

    /**
     * Handle file update
     */
    private function handleFileUpdate(Book $book, $request, &$data)
    {
        //*check if exist
        if ($book->sqlite != null) {
            Storage::disk('books')->delete($book->sqlite);
        }
        $bookFileFullPath = $this->getBookFilePath($request->file_path);
        $fileSize = $this->getFileSize($request->file_path);
        $readableSize = $this->formatFileSize($fileSize);

        $data['sqlite'] = $bookFileFullPath;
        $data['sqlite_size'] = $readableSize;
    }

    /**
     * Handle image update
     */
    private function handleImageUpdate(Book $book, $request, &$data)
    {
        $imageNameWithExtension = $this->getImageFileName($book->image);
        $status = Storage::disk('spaces')->delete('books/' . $book->id . '/' . $imageNameWithExtension);

        $currentImage = $this->getImageFileName($request->image_path);
        $file = Storage::disk('public')->get($currentImage);

        Storage::disk('spaces')->put('books/' . $book->id . '/' . $book->hashId . '.jpg', $file, 'public');

        $path = Storage::disk('spaces')->url('books/' . $book->id . '/' . $book->hashId . '.jpg');

        $data['image'] = $path;
    }

    /**
     * Store PDF file
     */
    private function storePdfFile(Book $book, $request)
    {
        $fileNameFormatted = $this->formatFileName($request->file('file')->getClientOriginalName());

        Storage::disk('spaces')->putFileAs('books/' . $book->id . '/', $request->file('file'), $fileNameFormatted, 'public');

        $fileSize = $this->getPdfFileSize($book, $fileNameFormatted);
        $readableSize = $this->formatFileSize($fileSize);
        $bookFileFullPath = Storage::disk('spaces')->url('books/' . $book->id . '/' . $fileNameFormatted);

        BookFiles::create([
            'book_id' => $request->book_id,
            'file_name' => $fileNameFormatted,
            'file_path' => $bookFileFullPath,
            'file_size' => $readableSize,
            'file_type' => 'pdf',
            'part' => $request->part,
            'file_number' => 1,
            'updated_at' => Carbon::create(1999, 1, 1, 0, 0, 0),
        ]);
    }

    /**
     * Update PDF file
     */
    private function updatePdfFile(BookFiles $bookFile, $request)
    {
        if ($request->hasFile('file') && $request->file('file') != null) {
            $this->handlePdfFileUpdate($bookFile, $request);
        } else {
            $bookFile->update([
                'part' => $request->part,
                'file_number' => $request->part,
            ]);
        }
    }

    /**
     * Handle PDF file update
     */
    private function handlePdfFileUpdate(BookFiles $bookFile, $request)
    {
        if (isset($bookFile->file_name)) {
            Storage::disk('spaces')->delete('books/' . $bookFile->book_id . '/' . $bookFile->file_name);
        }

        $book = Book::find($request->book_id);
        $fileNameFormatted = $this->formatFileName($request->file('file')->getClientOriginalName());

        Storage::disk('spaces')->putFileAs('books/' . $book->id . '/', $request->file('file'), $fileNameFormatted, 'public');

        $fileSize = $this->getPdfFileSize($book, $fileNameFormatted);
        $readableSize = $this->formatFileSize($fileSize);
        $bookFileFullPath = Storage::disk('spaces')->url('books/' . $book->id . '/' . $fileNameFormatted);

        $bookFile->update([
            'book_id' => $request->book_id,
            'file_name' => $fileNameFormatted,
            'file_path' => $bookFileFullPath,
            'file_size' => $readableSize,
            'file_type' => 'pdf',
            'part' => $request->part,
            'file_number' => $request->part,
        ]);
    }

    /**
     * Format file name
     */
    private function formatFileName($fileName)
    {
        $fileName = str_replace(' ', '_', $fileName);
        $fileName = preg_replace('/[^A-Za-z0-9_\p{Arabic}]/u', '', $fileName);
        $fileName = preg_replace('/_+/', '_', $fileName);
        return preg_replace('/pdf/', '.pdf', $fileName);
    }

    /**
     * Get PDF file size
     */
    private function getPdfFileSize(Book $book, $fileName)
    {
        return round(Storage::disk('spaces')->size('books/' . $book->id . '/' . $fileName));
    }

    /**
     * Display and manage books that appear on the home page
     *
     * This method is protected by the 'auth' middleware in the routes file
     * and should only be accessible to administrators
     */

    /**
     * Update books displayed on the home page
     *
     * This method is protected by the 'auth' middleware in the routes file
     * and should only be accessible to administrators
     */
    public function updateHomeBooks(Request $request)
    {

        // Validate the request
        $request->validate([
            'book_ids' => 'required|array',
            'book_ids.*' => 'exists:books,id',
        ]);


        // First, reset all books to not display on home
        Book::where('display_on_home', 1)->update(['display_on_home' => 0]);

        // Then set the selected books to display on home
        Book::whereIn('id', $request->book_ids)->update(['display_on_home' => 1]);

        // Clear cache to reflect changes immediately
        if (class_exists('\Illuminate\Support\Facades\Cache')) {
            \Illuminate\Support\Facades\Cache::tags(['books', 'home'])->flush();
        }

        return redirect()->route('books.index')->with('success', 'تم تحديث الكتب المعروضة في الصفحة الرئيسية بنجاح');
    }

    /**
     * Toggle a book's display status on the home page via AJAX
     */
    public function toggleHomeBook(Request $request)
    {
        // Check if user has admin permissions
        if (!$this->userHasAdminPermission()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بهذا الإجراء'
            ], 403);
        }

        // Validate the request
        $request->validate([
            'book_id' => 'required|exists:books,id',
        ]);

        $book = Book::findOrFail($request->book_id);
        $newStatus = !$book->display_on_home;

        // Update the book's display status
        $book->display_on_home = $newStatus;
        $book->save();

        // Clear cache to reflect changes immediately
        if (class_exists('\Illuminate\Support\Facades\Cache')) {
            \Illuminate\Support\Facades\Cache::tags(['books', 'home'])->flush();
        }

        return response()->json([
            'success' => true,
            'display_on_home' => $newStatus,
            'message' => $newStatus
                ? 'تم إضافة "' . $book->title . '" للصفحة الرئيسية بنجاح'
                : 'تم إزالة "' . $book->title . '" من الصفحة الرئيسية بنجاح'
        ]);
    }

    /**
     * Toggle a book's website visibility status via AJAX
     */
    public function toggleWebsite(Request $request, Book $book)
    {
        // Validate the request
        $request->validate([
            'website' => 'required|boolean',
        ]);

        // Update the book's website status
        $book->timestamps = false; // Prevent auto-updating the timestamps
        $book->website = $request->website;
        $book->save();

        // Clear cache to reflect changes immediately
        if (class_exists('\Illuminate\Support\Facades\Cache')) {
            \Illuminate\Support\Facades\Cache::tags(['books', 'website'])->flush();
        }

        return response()->json([
            'success' => true,
            'message' => $request->website
                ? 'تم تفعيل ظهور "' . $book->title . '" في الموقع بنجاح'
                : 'تم إلغاء ظهور "' . $book->title . '" من الموقع بنجاح'
        ]);
    }

    /**
     * Toggle a book's application visibility status via AJAX
     */
    public function toggleApplication(Request $request, Book $book)
    {
        // Validate the request
        $request->validate([
            'application' => 'required|boolean',
        ]);

        // Update the book's application status
        $book->timestamps = false; // Prevent auto-updating the timestamps
        $book->application = $request->application;
        $book->save();

        // Clear cache to reflect changes immediately
        if (class_exists('\Illuminate\Support\Facades\Cache')) {
            \Illuminate\Support\Facades\Cache::tags(['books', 'application'])->flush();
        }

        return response()->json([
            'success' => true,
            'message' => $request->application
                ? 'تم تفعيل ظهور "' . $book->title . '" في التطبيق بنجاح'
                : 'تم إلغاء ظهور "' . $book->title . '" من التطبيق بنجاح'
        ]);
    }

    /**
     * Update a book's private users access
     */
    public function updatePrivateUsers(Request $request, Book $book)
    {
        // Validate the request
        $validated = $request->validate([
            'private_users_switch' => 'sometimes',
            'private_users' => 'array',
            'private_users.*' => 'exists:users,id'
        ]);

        // Process private users
        $privateUsers = null;
        if (isset($validated['private_users_switch']) && $validated['private_users_switch'] == 'on') {
            if (isset($validated['private_users']) && !empty($validated['private_users'])) {
                $privateUsers = implode(',', $validated['private_users']);
            }
        }

        // Update the book
        $book->timestamps = false; // Prevent auto-updating the timestamps
        $book->private_users = $privateUsers;
        $book->save();

        return redirect()->back()->with('success', 'تم تحديث إعدادات الوصول للكتاب "' . $book->title . '" بنجاح');
    }

    /**
     * Helper method to check if the current user has admin permissions
     */
    protected function userHasAdminPermission()
    {
        // Implement your permission check here
        // For now, we'll assume that if the user is authenticated, they have admin permissions
        return auth()->check();
    }

    /**
     * Clear the cache for book statistics
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearStatisticsCache()
    {
        Cache::forget('book_statistics');
        return redirect()->route('books.index')->with('success', 'تم مسح ذاكرة التخزين المؤقت للإحصائيات وتحديث البيانات بنجاح');
    }
}
