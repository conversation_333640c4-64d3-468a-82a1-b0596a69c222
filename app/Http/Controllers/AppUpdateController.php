<?php

namespace App\Http\Controllers;

use App\Models\AppUpdate;
use Illuminate\Http\Request;

class AppUpdateController extends Controller
{
    public function index()
    {
        $appUpdates = AppUpdate::all();
        return view('modules.app-updates.index', compact('appUpdates'));
    }

    public function create()
    {
        return redirect()->route('app-updates.index');
    }

    public function store(Request $request)
    {
        $request->validate([
            'platform' => 'required|string|max:255',
            'version' => 'required|string|max:255',
            'message' => 'nullable|string|max:255',
            'update_message' => 'nullable|string',
            'banner_message' => 'nullable|string|max:255',
            'release_date' => 'nullable|date',
            'force_update' => 'sometimes',
            'show_banner_message' => 'sometimes',
        ]);
        $data = $request->all();
        if ($request->has('force_update')) {
            $data['force_update'] = $request->force_update == 'on' ? true : false;
        }
        if ($request->has('show_banner_message')) {
            $data['show_banner_message'] = $request->show_banner_message == 'on' ? true : false;
        }

        AppUpdate::create($data);

        return redirect()->route('app-updates.index')->with('success','تم إنشاء تحديث التطبيق بنجاح.');
    }

    public function show(AppUpdate $appUpdate)
    {
        //
    }

    public function edit(AppUpdate $appUpdate)
    {
        return view('modules.app-updates.edit', compact('appUpdate'));
    }

    public function update(Request $request, AppUpdate $appUpdate)
    {
        $request->validate([
            'platform' => 'required|string|max:255',
            'version' => 'required|string|max:255',
            'message' => 'nullable|string|max:255',
            'update_message' => 'nullable|string',
            'banner_message' => 'nullable|string|max:255',
            'release_date' => 'nullable|date',
            'force_update' => 'sometimes',
            'show_banner_message' => 'sometimes',
        ]);

        $data = $request->all();
        if ($request->has('force_update')) {
            $data['force_update'] = $request->force_update == 'on' ? true : false;
        }else{
            $data['force_update'] = false;
        }
        if ($request->has('show_banner_message')) {
            $data['show_banner_message'] = $request->show_banner_message == 'on' ? true : false;
        }else{
            $data['show_banner_message'] = false;
        }
        $appUpdate->update($request->all());

        return redirect()->route('app-updates.index')->with('success',  'تم تحديث تحديث التطبيق بنجاح.');
    }

    public function destroy(AppUpdate $appUpdate)
    {
        $appUpdate->delete();

        return redirect()->route('app-updates.index')->with('success', 'تم حذف تحديث التطبيق بنجاح.');
    }
}
