<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SocialMediaStatsController extends Controller
{
    public function getTelegramStats()
    {
        $cacheKey = 'telegram_stats';
        $cacheTime = 3600; // 1 hour cache

        return Cache::remember($cacheKey, $cacheTime, function () {
            $botToken = config('telegram.bots.mybot.token');
            $channelUsername = 'ziydia';

            try {
                $response = Http::get("https://api.telegram.org/bot{$botToken}/getChatMembersCount", [
                    'chat_id' => "@{$channelUsername}"
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return [
                        'subscribers' => $data['result'] ?? 0,
                        'success' => true
                    ];
                }
            } catch (\Exception $e) {
                Log::error('Error fetching Telegram stats: ' . $e->getMessage());
            }

            return [
                'subscribers' => 0,
                'success' => false
            ];
        });
    }

    public function getYouTubeStats()
    {
        $cacheKey = 'youtube_stats';
        $cacheTime = 3600; // 1 hour cache

        return Cache::remember($cacheKey, $cacheTime, function () {
            // $apiKey = config('services.youtube.api_key');
            $apiKey = config('services.youtube.api_key');
            $channelId = 'UC0HK4Hp_XnAP4bZUyT1DdVA';
            
            try {
                $response = Http::get("https://www.googleapis.com/youtube/v3/channels", [
                    'part' => 'statistics',
                    'id' => $channelId,
                    'key' => $apiKey
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return [
                        'subscribers' => $data['items'][0]['statistics']['subscriberCount'] ?? 0,
                        'success' => true
                    ];
                }
            } catch (\Exception $e) {
                Log::error('Error fetching YouTube stats: ' . $e->getMessage());
            }

            return [
                'subscribers' => 0,
                'success' => false
            ];
        });
    }
}