<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSettingsRequest;
use App\Http\Requests\UpdateSettingsRequest;
use App\Models\Settings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = Settings::all();
        $settings = [];
        foreach ($list as $item) {
            $settings[$item->key] = $item->value;
        }

        return view('modules.settings.index', compact('settings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('settings.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSettingsRequest $request)
    {
        $keys = $request->validated()['keys'];
        $keys = array_filter($keys, function ($key) {
            return $key['type'] !== 'file';
        });

        //*check if the file is present
        if (count($request->files) > 0) {
            $files = $request->files;
            foreach ($files as $key => $files2) {
                foreach ($files2 as $key2 => $file) {
                    $extension = pathinfo($file['value']->getClientOriginalName(), PATHINFO_EXTENSION);
                    $path = Storage::disk('public')->putFileAs('settings', $file['value'], $key2.'.'.$extension);
                    $keys[$key2] = [
                        'value' => $path,
                        'type' => 'file',
                    ];
                }
            }
        }

        foreach ($keys as $key => $value) {
            Settings::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value['value'],
                    'type' => $value['type'],
                ],
            );
        }

        return redirect()->route('settings.index')->with('success', 'تم حفظ الإعدادات بنجاح.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Settings $settings)
    {
        return redirect()->route('settings.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Settings $settings)
    {
        return redirect()->route('settings.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSettingsRequest $request, Settings $settings)
    {
        return redirect()->route('settings.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Settings $settings)
    {
        return redirect()->route('settings.index');
    }
}
