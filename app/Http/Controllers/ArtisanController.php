<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ArtisanController extends Controller
{
    /**
     * Execute an artisan command
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function executeCommand(Request $request)
    {
        // Validate the request
        $request->validate([
            'command' => 'required|string|max:255',
        ]);

        // List of allowed commands for security
        $allowedCommands = [
            'cache:clear',
            'config:clear',
            'route:clear',
            'view:clear',
            'optimize',
            'db:optimize',
            'down',
            'up',
            'schedule:run',
        ];

        $command = $request->input('command');

        // Check if the command is allowed
        if (!in_array($command, $allowedCommands)) {
            return redirect()->back()->with('error', 'هذا الأمر غير مسموح به.');
        }

        try {
            // Execute the command
            Artisan::call($command);
            
            // Get the output
            $output = Artisan::output();
            
            // Log the command execution
            Log::info("Artisan command executed: {$command}", ['output' => $output]);
            
            // Return with success message
            return redirect()->back()->with('success', "تم تنفيذ الأمر '{$command}' بنجاح.");
        } catch (\Exception $e) {
            // Log the error
            Log::error("Error executing artisan command: {$command}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return with error message
            return redirect()->back()->with('error', "حدث خطأ أثناء تنفيذ الأمر: {$e->getMessage()}");
        }
    }
} 