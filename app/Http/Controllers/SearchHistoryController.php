<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Models\SearchHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SearchHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Prepare analytics data for the dashboard
        $analyticsData = [
            [
                'title' => 'إجمالي عمليات البحث',
                'value' => SearchHistory::count(),
                'percent' => 0,
                'color' => 'primary',
                'icon' => 'search',
                'description' => 'إجمالي عدد عمليات البحث'
            ],
            [
                'title' => 'عمليات البحث اليوم',
                'value' => SearchHistory::whereDate('created_at', today())->count(),
                'percent' => 0,
                'color' => 'success',
                'icon' => 'calendar',
                'description' => 'عمليات البحث في اليوم الحالي'
            ],
            [
                'title' => 'المستخدمين النشطين',
                'value' => SearchHistory::distinct('user_id')->whereNotNull('user_id')->count('user_id'),
                'percent' => 0,
                'color' => 'info',
                'icon' => 'users',
                'description' => 'عدد المستخدمين الذين قاموا بالبحث'
            ],
            [
                'title' => 'متوسط البحث اليومي',
                'value' => round(SearchHistory::count() / max(1, now()->diffInDays(SearchHistory::min('created_at') ?: now()))),
                'percent' => 0,
                'color' => 'warning',
                'icon' => 'chart-bar',
                'description' => 'متوسط عمليات البحث اليومية'
            ]
        ];

        // Get top search types
        $searchTypes = SearchHistory::select('search_type', DB::raw('count(*) as count'))
            ->groupBy('search_type')
            ->orderBy('count', 'desc')
            ->get();

        return view('modules.search-history.index', compact('analyticsData', 'searchTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('search-histories.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return redirect()->route('search-histories.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(SearchHistory $searchHistory)
    {
        return redirect()->route('search-histories.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SearchHistory $searchHistory)
    {
        return redirect()->route('search-histories.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SearchHistory $searchHistory)
    {
        return redirect()->route('search-histories.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SearchHistory $searchHistory)
    {
        $searchHistory->delete();
        
        Helpers::notifyAdmins([
            'title' => 'تم حذف سجل بحث',
            'message' => 'تم حذف سجل بحث بواسطة ' . auth()->user()->name,
            'href' => route('search-histories.index'),
        ]);
        
        return back()->with('success', 'تم حذف سجل البحث بنجاح');
    }
}
