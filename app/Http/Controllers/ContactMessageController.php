<?php

namespace App\Http\Controllers;

use App\Models\ContactMessage;
use Illuminate\Http\Request;

class ContactMessageController extends Controller
{
    /**
     * Display a listing of the contact messages.
     */
    public function index()
    {
        $items = ContactMessage::parents()->latest()->get();
        return view('modules.contact-messages.index', compact('items'));
    }

    /**
     * Store a newly created contact message.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'message' => 'required|string',
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'status' => 'nullable|in:pending,replied,closed',
            'file' => 'nullable|file|max:5120', // 5MB max
            'extra' => 'nullable|json',
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            
            if (!file_exists(public_path('storage/contact-messages'))) {
                mkdir(public_path('storage/contact-messages'), 0777, true);
            }
            
            $file->move(public_path('storage/contact-messages'), $fileName);
            $validated['file'] = 'contact-messages/' . $fileName;
        }

        if ($request->user()) {
            $validated['user_id'] = $request->user()->id;
        }

        ContactMessage::create($validated);
        return redirect()->route('contact-messages.index')->with('success', 'تم إنشاء الرسالة بنجاح.');
    }

    /**
     * Display the specified contact message.
     */
    public function show(ContactMessage $contactMessage)
    {
        // Mark the message as read when viewing it
        if (!$contactMessage->is_read) {
            $contactMessage->markAsRead();
        }
        
        return view('modules.contact-messages.show', compact('contactMessage'));
    }

    /**
     * Update the specified contact message.
     */
    public function update(Request $request, ContactMessage $contactMessage)
    {
        $validated = $request->validate([
            'message' => 'required|string',
            'status' => 'required|in:pending,replied,closed',
        ]);

        $contactMessage->update($validated);
        return redirect()->route('contact-messages.index')->with('success', 'تم تحديث الرسالة بنجاح.');
    }

    /**
     * Remove the specified contact message.
     */
    public function destroy(ContactMessage $contactMessage)
    {
        $contactMessage->delete();
        return redirect()->route('contact-messages.index')->with('success', 'تم حذف الرسالة بنجاح.');
    }

    /**
     * Reply to a contact message.
     */
    public function reply(Request $request, ContactMessage $contactMessage)
    {
        $validated = $request->validate([
            'message' => 'required|string',
        ]);

        // Create a reply as a new message with parent_id
        $reply = new ContactMessage([
            'message' => $validated['message'],
            'parent_id' => $contactMessage->id,
            'user_id' => $request->user()->id,
            'status' => 'replied',
        ]);
        
        $reply->save();
        
        // Update parent message status to replied
        $contactMessage->update(['status' => 'replied']);

        return redirect()->route('contact-messages.show', $contactMessage)->with('success', 'تم إرسال الرد بنجاح.');
    }
}