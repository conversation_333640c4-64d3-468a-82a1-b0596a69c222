<?php

namespace App\Http\Controllers;

use App\Models\ShortUrl;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ShortUrlController extends Controller
{
    public function index()
    {
        $columns = [['title' => '#', 'type' => 'number', 'field' => 'id'], ['title' => 'الرابط المختصر', 'type' => 'string', 'field' => 'short_url'], ['title' => 'الرابط الأصلي', 'type' => 'string', 'field' => 'original_url'], ['title' => 'تاريخ الإنشاء', 'type' => 'date', 'field' => 'created_at'], ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false]];
        $shortUrls = ShortUrl::orderByDesc('id')->paginate(10);
        return view('modules.short-url.index', compact('shortUrls', 'columns'));
    }

    public function create(Request $request)
    {
        $request->validate([
            'original_url' => 'required|url',
        ]);

        $shortCode = Str::random(6);
        ShortUrl::create([
            'original_url' => $request->original_url,
            'short_code' => $shortCode,
        ]);

        return back()->with('success', 'تم إنشاء الرابط المختصر بنجاح');
    }

    public function update(Request $request, ShortUrl $shortUrl)
    {
        $request->validate([
            'original_url' => 'required|url',
        ]);

        $shortUrl->update([
            'original_url' => $request->original_url,
        ]);

        return back()->with('success', 'تم تحديث الرابط المختصر بنجاح');
    }

    public function redirect($shortCode)
    {
        $shortUrl = ShortUrl::where('short_code', $shortCode)->firstOrFail();
        $shortUrl->increment('clicks');

        return redirect($shortUrl->original_url);
    }

    public function destroy(ShortUrl $shortUrl)
    {
        $shortUrl->delete();

        return back()->with('success', 'تم حذف الرابط المختصر بنجاح');
    }
}
