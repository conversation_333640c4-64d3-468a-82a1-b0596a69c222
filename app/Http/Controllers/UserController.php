<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\RolesEnum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->has('itemsPerPage') ? $request->itemsPerPage : 12;
        $users = User::where(function ($query) use ($request) {
            if ($request->id != null) {
                $query->where('id', $request->id);
            }
            if ($request->search != null) {
                $query
                    ->where('name', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('phone', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('email', 'LIKE', '%' . $request->search . '%');
            }
            if ($request->datefilter != null) {
                try {
                    $startDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[0])->format('Y-m-d');
                    $endDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[1])->format('Y-m-d');
                    $query->whereDate('created_at', '>=', $startDate)->whereDate('created_at', '<=', $endDate);
                } catch (\Exception $e) {
                }
            }
        })
            ->with(['roles'])
            ->with(['userInfo'])
            ->orderBy('last_activity', 'DESC')
            ->orderBy('id', 'DESC')
            ->paginate($perPage);

        $roles = Role::all();
        $usersCount = User::count();
        $usersCountLastMonth = User::where('created_at', '>=', Carbon::now()->subMonth())->count();
        $usersCountLastMonthPercentage = $usersCountLastMonth > 0 ? number_format((100 * $usersCountLastMonth) / $usersCount) : 0;

        $activeUsersLastWeek = User::where('last_activity', '>=', Carbon::now()->subWeek())->count();
        $activeUsersLastWeekPercentage = $activeUsersLastWeek > 0 ? number_format((100 * $activeUsersLastWeek) / $usersCount, 2) : 0;

        $activeUsersLastMonth = User::where('last_activity', '>=', Carbon::now()->subMonth())->count();
        $activeUsersLastMonthPercentage = $activeUsersLastMonth > 0 ? number_format((100 * $activeUsersLastMonth) / $usersCount, 2) : 0;

        $analyticsData = [
            [
                'title' => 'عدد المستخدمين',
                'description' => 'عدد المستخدمين الكلي',
                'value' => $usersCount,
                'percent' => 0,
                'icon' => 'users',
                'color' => 'primary',
            ],
            [
                'title' => 'عدد المستخدمين الجدد',
                'description' => 'عدد المستخدمين الجدد خلال الشهر الماضي',
                'value' => $usersCountLastMonth,
                'percent' => $usersCountLastMonthPercentage,
                'icon' => 'users',
                'color' => 'success',
            ],
            [
                'title' => 'عدد المستخدمين النشطين',
                'description' => 'عدد المستخدمين النشطين خلال الأسبوع الماضي',
                'value' => $activeUsersLastWeek,
                'percent' => $activeUsersLastWeekPercentage,
                'icon' => 'users',
                'color' => 'warning',
            ],
            [
                'title' => 'عدد المستخدمين النشطين',
                'description' => 'عدد المستخدمين النشطين خلال الشهر الماضي',
                'value' => $activeUsersLastMonth,
                'percent' => $activeUsersLastMonthPercentage,
                'icon' => 'users',
                'color' => 'danger',
            ],
        ];

        return view('modules.user.index', compact('users', 'roles', 'usersCount', 'analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        abort(403, 'Unauthorized action.');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        $data = $request->except('role');
        $user = User::create($data);
        $user->assignRole(Role::find($request->role)->name);
        $user = UserResource::make($user);

        return back()->with('success', 'تم إنشاء المستخدم بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        return view('modules.user.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        return view('modules.user.update', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        if ($user->hasRole(RolesEnum::SUPER_ADMIN)) {
            return back()->with('error', 'لا يمكن تعديل بيانات المستخدم لانه مدير النظام');
        }

        $data = $request->except('role');
        if ($request->password != null && $request->password != '') {
            $data['password'] = bcrypt($request->password);
        } else {
            unset($data['password']);
        }
        $user->update($data);
        $user = UserResource::make($user);

        //syncRoles() method is used to assign a role to a user and remove all other roles from the user.
        $user->syncRoles(Role::find($request->role)->name);
        return back()->with('success', 'تم تحديث المستخدم بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {

        if ($user->hasRole(RolesEnum::SUPER_ADMIN)) {
            return back()->with('error', 'لا يمكن تعديل بيانات المستخدم لانه مدير النظام');
        }
        $user->delete();
        Helpers::notifyAdmins([
            'title' => 'تم حذف مستخدم',
            'message' => 'المستخدم ' . $user->name . ' تم حذف بواسطة ' . auth()->user()->name,
            'href' => route('users.index'),
        ]);
        return back()->with('success', 'تم حذف المستخدم بنجاح');
    }

    //* Send Notification to User
    public function sendNotificationToUser(Request $request, User $user)
    {
        // $user->notify(new UserNotification($request->message));
        // return back()->with('success', 'Notification sent successfully');
    }
}
