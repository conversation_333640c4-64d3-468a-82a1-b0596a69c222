<?php

use App\Models\BookSearch;
use App\Models\Fatawy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Telegram\Bot\Laravel\Facades\Telegram;

class TelegramController extends Controller
{

    public function handleUpdate(Request $request)
    {

        $update = Telegram::getWebhookUpdates();

        $message = $update->getMessage();

        if (!$message || !$message->getText()) {
            return;
        }

        $chatId = $message->getChat()->getId();
        $username = $message->getChat()->getUsername();
        $text = $message->getText();

        //*check if message is admin password if yes return un answered questions
        if($text === '/255#@La') {
            $fatawies = Fatawy::where('answered', false)->get();
            $response = '';
            foreach ($fatawies as $fatawy) {
                $response .= "User: " . $fatawy->username . "\nQuestion: " . $fatawy->question . "\n\n";
            }
            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => $response,
            ]);
            return;
        }else if ($text === '/start') {
            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => "Welcome! Send me your question, and an admin will reply within 24 hours."
            ]);
        } else {
            // Save question in the database
            Fatawy::create([
                'user_id' => $chatId,
                'username' => $username,
                'question' => $text,
            ]);

            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => "Your question has been received. An admin will reply within 24 hours."
            ]);
        }
    }

  
    public function receive(Request $request)
    {
        $data = $request->all();
        $chat_id = $data['message']['chat']['id'];
        $text = $data['message']['text'];

        $response = [
            'chat_id' => $chat_id,
            'text' => $text,
            'parse_mode' => 'HTML',
        ];
        Log::info('Telegram response: ' . json_encode($response));
        if (strpos($text, '/start') !== false) {
            $response['text'] = 'مرحبا بك في بوت المكتبة الزيدية';
        } elseif (strpos($text, '/help') !== false) {
            $response['text'] = 'اكتب كلمة البحث للبحث عن الكتب';
        } else {
            //check if text contains only english letters
            if (preg_match('/[A-Za-z]/', $text)) {
                $response['text'] = 'البحث يدعم اللغة العربية فقط';
                Http::post('https://api.telegram.org/bot' . env('TELEGRAM_BOT_TOKEN') . '/sendMessage', $response);
                return response()->json($response);
                $text = preg_replace('/\s+/', ' ', $text);
            }
            //remove characters that are not letters ar or numbers
            $text = preg_replace('/[^A-Za-z0-9ء-ي\s]/u', '', $text);

            //*remove spaces
            $text = preg_replace('/\s+/', ' ', $text);

            try {
                $results = BookSearch::search($text, 'default');
            } catch (\Exception $e) {
                $response['text'] = 'حدث خطأ أثناء البحث';
                Http::post('https://api.telegram.org/bot' . env('TELEGRAM_BOT_TOKEN') . '/sendMessage', $response);
                return response()->json($response);
            }
            $resultsAsText = '';
            if (count($results) == 0) {
                $response['text'] = 'لم يتم العثور على نتائج';
            } else {
                for ($i = 0; $i < count($results); $i++) {
                    if ($i == 3) {
                        break;
                    } else {
                        $resultsAsText .= $results[$i]->snip . "\n";
                    }
                }
            }
            $response['text'] = $resultsAsText;
        }
        Http::post('https://api.telegram.org/bot' . env('TELEGRAM_BOT_TOKEN') . '/sendMessage', $response);

        return response()->json($response);
    }
}
