<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StoreAuthorRequest;
use App\Http\Requests\UpdateAuthorRequest;
use App\Models\Author;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AuthorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return view('modules.author.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('authors.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAuthorRequest $request)
    {
        $data = $request->all();

        $author = Author::create($data);


        cache()->put('authors', Author::all());
        return back()->with('success', 'تم إنشاء المؤلف بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Author $author)
    {
        return redirect()->route('authors.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Author $author)
    {
        return view('modules.author.update', compact('author'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAuthorRequest $request, Author $author)
    {
        dd('test');
        $data = $request->all();
        
        $author->update($data);

        cache()->put('authors', Author::all());
        return back()->with('success', 'تم تحديث المؤلف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Author $author)
    {
        $author->delete();
        Helpers::notifyAdmins([
            'title' => 'Author Deleted',
            'message' => 'Author ' . $author->name . ' has been deleted by ' . auth()->user()->name,
            'href' => route('authors.index'),
        ]);
        cache()->put('authors', Author::all());
        return back()->with('success', 'تم حذف المؤلف بنجاح');
    }
}
