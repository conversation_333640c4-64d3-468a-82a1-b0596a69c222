<?php

namespace App\Http\Controllers;

use App\Models\AppNotification;
use App\Http\Requests\StoreAppNotificationRequest;
use App\Http\Requests\UpdateAppNotificationRequest;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class AppNotificationController extends Controller
{
    public function index()
    {
        $items = AppNotification::all();
        return view('modules.app-notifications.index', compact('items'));
    }

    public function create()
    {
        return redirect()->route('app-notifications.index');
    }

    public function store(StoreAppNotificationRequest $request)
    {
        $data = $request->except('image');
        if ($request->hasFile('image')) {
            //if image bigger than 10MB resize it
            $manager = new ImageManager(Driver::class);
            $image = $manager->read($request->file('image'));
            $image->resize(512, 512);
            $generateImageName = time() . '.' . $request->file('image')->getClientOriginalExtension();
            //create directory if not exists
            if (!file_exists(public_path('storage/notifications'))) {
                mkdir(public_path('storage/notifications'), 0777, true);
            }
            $data['image'] = $image->save('storage/notifications/' . $generateImageName);
        }
        $notification = AppNotification::create($request->validated());
        return redirect()->route('app-notifications.index')->with('success', 'تم إنشاء الإشعار بنجاح.');
    }

    public function show(AppNotification $appNotification)
    {
        return view('modules.app-notifications.show', compact('appNotification'));
    }

    public function edit(AppNotification $appNotification)
    {
        return view('modules.app-notifications.edit', compact('appNotification'));
    }

    public function update(UpdateAppNotificationRequest $request, AppNotification $appNotification)
    {
        $appNotification->update($request->validated());
        return redirect()->route('app-notifications.index')->with('success', 'تم تحديث الإشعار بنجاح.');
    }

    public function destroy(AppNotification $appNotification)
    {
        $appNotification->delete();
        return redirect()->route('app-notifications.index')->with('success','تم حذف الإشعار بنجاح.');
    }
}
