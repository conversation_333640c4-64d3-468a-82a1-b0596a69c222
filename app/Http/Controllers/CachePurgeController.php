<?php

namespace App\Http\Controllers;

use App\Services\CachePurgeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CachePurgeController extends Controller
{
    /**
     * Purge cache for a book's files
     *
     * @param string $bookId
     * @return JsonResponse
     */
    public function purgeBookCache(string $bookId): JsonResponse
    {
        $directory = "books/{$bookId}";
        $token = config('filesystems.disks.spaces.digital_ocean_token');
        try {
            $cdnIdResponse = Http::withToken($token . 'a6')->get('https://api.digitalocean.com/v2/cdn/endpoints');
            if ($cdnIdResponse->status() !== 200) {
                $result = [
                    'success' => false,
                    'message' => 'Failed to retrieve CDN endpoints',
                ];
            }
            $cdnEndpoints = $cdnIdResponse->json();
            $cdnId = $cdnEndpoints['endpoints'][0]['id'] ?? null;

            $response = Http::withToken($token . 'a6')->delete("https://api.digitalocean.com/v2/cdn/endpoints/{$cdnId}/cache", [
                'files' => ["{$directory}*"],
            ]);

            if ($response->status() === 204) {
                Log::info("Cache purged for directory: {$directory}");
                $result = [
                    'success' => true,
                    'message' => "Cache purged for directory: {$directory}",
                ];
            }

            $result = [
                'success' => false,
                'message' => "Failed to purge cache for directory: {$directory}",
            ];
        } catch (\Exception $e) {
            Log::error("Exception while purging cache for directory {$directory}: " . $e->getMessage());
            $result = [
                'success' => false,
                'message' => "Exception while purging cache for directory: {$directory}",
            ];
        }

        return response()->json($result, $result['success'] ? 200 : 404);
    }
}
