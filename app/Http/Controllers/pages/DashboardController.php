<?php

namespace App\Http\Controllers\pages;

use App\Http\Controllers\Controller;
use App\Http\Controllers\SocialMediaStatsController;
use App\Jobs\ProcessBook;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookCategories;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Modules\TrafficLogs\Models\TrafficLogs;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    public function index(Request $request)
    {
        $data = Cache::remember('dashboard_data', 300, function () {
            return $this->dashboardService->getDashboardData();
        });

        $socialMediaStatsController = new SocialMediaStatsController();

        $telegramStats = $socialMediaStatsController->getTelegramStats();
        $youtubeStats = $socialMediaStatsController->getYouTubeStats();

        $data['telegram'] = $telegramStats['subscribers'];
        $data['youtube'] = $youtubeStats['subscribers'];


        return view('content.pages.dashboard', compact('data'));
    }


    public function refreshDashboard()
    {
        Cache::forget('dashboard_data');
        return redirect()->route('dashboard');
    }

    public function deleteAllFiles()
    {
        //Delete all folders and files in a directory
        Storage::disk('public')->deleteDirectory('books');

        return back();
    }

    public function testCreateBook()
    {
        $bookFileFullPath = env('APP_URL') . '/storage/books/475.zip';

        $data = [
            'title' => 'test',
            'author_id' => 1,
            'summary' => 'test',
            'application' => 1,
            'website' => 1,
            'categories' => [1, 2],
            'added_by' => 1,
            'sqlite' => $bookFileFullPath,
            'image_path' => 'test_200_200_purble 2.png',
        ];
        $book = Book::create($data);
        $book->categories()->attach($data['categories']);
        ProcessBook::dispatch($book);
        return back();
    }
    public function test()
    {
        return view('content.pages.test');
    }
    public function homeTestButton()
    {
        $bookFile = Storage::disk('books')->get('1/1.zip');
        $bookSize = Storage::disk('books')->size('1/1.zip');
        //move a copy file to 2/2.zip
        $result = Storage::disk('spaces')->put('3/3.zip', $bookFile);
        dd($result);

        return back();
    }

    public function debugMerge()
    {
        $url = env('APP_URL');
        if ($url == 'http://localhost' || $url == 'http://127.0.0.1:8000') {
            $filePath = public_path('assets/books.json'); // Use local file path
            if (!file_exists($filePath)) {
                return back()->with('error', 'File not found');
            }
            $data = file_get_contents($filePath);
            $booksJson = json_decode($data, true);
            $books = [];
            foreach ($booksJson as $item) {
            //     array:15 [▼ // app/Http/Controllers/pages/HomeController.php:93
            //     "id" => 512
            //     "title" => "الجواهر المضيئة في تراجم بعض رجال الزيدية"
            //     "about" => """
            //       الكتاب: الجواهر المضيئة في تراجم بعض رجال الزيدية
            //       المؤلف: السيد العلامة عبد الله بن الإمام الهادي الحسن بن يحيى القاسمي (المتوفى: 1375 هـ)
            //       الطبعة: لم يطبع الكتاب بعد
            //       عدد الصفحات: 703
            //       [هو ضمن خدمة التراجم]
            //       """
            //     "size" => "324.27 KB"
            //     "sqlite" => "https://ziydia.com/uploads/books_db/1727293745.zip"
            //     "created_at" => array:3 [▶]
            //     "last_update_time" => array:3 [▶]
            //     "status" => "active"
            //     "image" => "https://s3.ziydia.com/books/512/8bWXXgWy.jpg"
            //     "pdf_files" => []
            //     "pdf_files_2" => []
            //     "pdf_files_3" => []
            //     "pdf_size" => null
            //     "author" => array:5 [▼
            //       "id" => 110
            //       "name" => "عبدالله بن الحسن القاسمي"
            //       "image" => "https://ziydia.com/assets/img/avatar.jpg"
            //       "death_date" => 1375
            //       "description" => """
            //         عبدالله بن الحسن القاسمي (1307 - 1375 هـ)
            //         عبدالله بن الحسن بن يحيى القاسمي الضحياني: عالم، فقيه، مجتهد، مطلق، مولده ببلاد صعدة سنة 1307 هـ، وبها نشأ، فأخذ عن علمائها، ومنهم والده العلامة الإمام الهادي،  ▶
            //         ومن مؤلفاته:
            //         · التقريب في شرح التهذيب.
            //         · الجداول الصغرى المختصرة من الطبقات الكبرى (طبقات الزيدية).
            //         · جلاء الأبصار بشرح تذكرة العقول في علم الأصول.
            //         · الجواهر المضيئة في تراجم رجال الحديث عند الزيدية.
            //         · سك السمع في حسن التوقيت وجواز الجمع.
            //         · كرامات الأولياء في مناقب خير الأوصياء.
            //         · مطلب الساغب شرح منية الراغب في النحو.
            //         · مواهب الغفار بتخريج أحاديث نجوم الأنظار.
            //         · تعيين الفرقة الناجية.
            //         · فوائد في تعداد المنازل وما يحدث منها ويصلح باعتبار التجارب .
            //         · نجوم الأنظار المنتزع من البحر الزخار.
            //         · الهدايات إلى حل شبه إشكال الآيات.
            //         · الجواب الأسد (جواب على محمد إبراهيم الوزير) في شفاعة قارئ سورة الصمد.
            //         · الخطب (مجموع في الخطب والمواعظ) .
            //         · رسائل وأجوبة في المحكم والمتشابه في القرآن.
            //         · الرد على رد سك السمع.
            //         · سيرة الإمام الهادي الحسن بن يحيى القاسمي.
            //         أعلام المؤلفين الزيدية (1/ 551)
            //         """
            //     ]
            //     "category" => array:3 [▼
            //       "id" => 12
            //       "name" => "السير والتراجم والتاريخ"
            //       "image" => "https://ziydia.com/assets/img/avatar.jpg"
            //     ]
            //   ]
            $author = [
                'id' => $item['author']['id'],
                'name' => $item['author']['name'],
                'death_date' => $item['author']['death_date'],
                'description' => $item['author']['description'],
            ];
            Author::updateOrCreate(
                ['id' => $author['id']],
                [
                    'name' => $author['name'],
                    'death_date' => $author['death_date'],
                    'description' => $author['description'],
                ]
            );
            $category = [
                'id' => $item['category']['id'],
                'name' => $item['category']['name'],
                'image' => $item['category']['image'],
            ];
            $category = \App\Models\Category::updateOrCreate(
                ['id' => $category['id']],
                [
                    'title' => $category['name'],
                    'image' => $category['image'],
                ]
            );
            $book = Book::updateOrCreate(
                ['id' => $item['id']],
                [
                    'title' => $item['title'],
                    'summary' => isset($item['about']) ? $item['about'] : null,
                    'application' => 1,
                    'website' => 1,
                    'added_by' => 1,
                    'sqlite' => $item['sqlite'],
                    'sqlite_size' => $item['size'],
                    'image' => $item['image'],
                    'author_id' => $item['author']['id'],
                ]
            );
            BookCategories::updateOrCreate(
                ['book_id' => $book->id, 'category_id' => $category->id],
                [
                    'book_id' => $book->id,
                    'category_id' => $category->id,
                ]
            );
            }
            return back()->with('success', 'done');
        } else {
            return back()->with('error', 'Not in local environment');
        }
    }
}
