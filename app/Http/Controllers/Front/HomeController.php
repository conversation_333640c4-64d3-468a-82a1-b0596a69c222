<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Models\Seo;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        $books = Book::with(['author:id,name,death_date','categories'])
        ->where('website', 1)  
        ->orderBy('created_at', 'desc')
        ->take(16)
        ->get();
        $seo = Seo::where('page', '=', 'home')->where('num', '=', 0)->first();
        $active_page = 'home';
        return view('front.home',compact('books','seo', 'active_page'));
    }
}
