<?php

namespace App\Http\Controllers\Front;

use App\Helpers\BookContentStyleHelper;
use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Models\BookContent;
use App\Models\BookReport;
use App\Models\BookReportDetail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BookPageController extends Controller
{
    // Add a variable to enable or disable caching
    private $enableCaching = true;

    //* get book info
    public function show($id)
    {
        $book = Book::where('id', $id)->first();
        if (!$book) {
            abort(404);
        }
        $firstPage = $book->firstPageId(null) ?? 1;
        $firstPart = $book->firstPart() ?? null;
        redirect()
            ->to("/book/$id/page/$firstPage/$firstPart")
            ->send();
    }
    public function page($id, $page, $part = null){
        // get page id from page and redirect to pageId
        $book = Book::where('id', $id)->first();
        if (!$book) {
            abort(404);
        }
        $pageId = $book->pageIdByPage($page, $part);
        if($pageId == null){
            abort(404);
        }
        redirect()
            ->to("/book/$id/page/$pageId/$part")
            ->send();
    }
    
    //* get book page content
    public function pageId($id, $pageId, $part = null)
    {
        $isAuth = auth()->check();
        if($isAuth) $this->enableCaching = false;

        $cacheKey = "book_page_{$id}_{$pageId}";
        $ip = $_SERVER['REMOTE_ADDR'];

        // Check if caching is enabled
        if ($this->enableCaching && Cache::has($cacheKey)) {
            $viewData = Cache::get($cacheKey);

            BookReportDetail::create([
                'ip_address' => $ip,
                'book_id' => $viewData['book']->id,
                'type' => 'read',
            ]);
            return view('front_v2.book_read.page', $viewData);
        }

        // Get book data (with caching)
        $bookCacheKey = 'book_' . $id;
        if (!Cache::has($bookCacheKey) || !$this->enableCaching) {
            //check if hash only numbers then get book by id
            if (is_numeric($id)) {
                $book = Book::where('id', $id)->first();
                if ($book == null) {
                    abort(404);
                }
                $id = $book->id;
            }

            $book = Book::with(['author:id,nickname,name,description,death_date', 'categories:id,title', 'titles:book_id,id,title_id,title,level', 'files:book_id,id,file_name,file_path,file_size,file_number,part'])
                ->where('id', $id)
                ->first(['id',  'title', 'image', 'summary', 'author_id', 'created_at', 'application', 'website']);
            if (!$book) {
                return response()->json(['message' => 'Book not found'], 404);
            }

            if (($book->application != 1 && $book->website != 1) && !$isAuth) {
                abort(404);
            }

            $ip = $_SERVER['REMOTE_ADDR'];
            BookReportDetail::create([
                'ip_address' => $ip,
                'book_id' => $book->id,
                'type' => 'read',
            ]);
            if ($this->enableCaching) {
                Cache::put($bookCacheKey, $book, now()->addDays(7));
            }
        } else {
            $book = Cache::get($bookCacheKey);
        }

        $part = $part ?? $book->firstPart();

        // Get pagination data (with caching)
        $paginationCacheKey = "pagination_{$id}_" . ($part ?? 'all');
        if (!Cache::has($paginationCacheKey) || !$this->enableCaching) {
            $pagination = [
                'firstPage' => $book->firstPageId($part),
                'lastPage' => $book->lastPageId($part),
                'totalPages' => $book->totalPages(),
                'firstPart' => $book->firstPart(),
                'lastPart' => $book->lastPart(),
            ];
            Cache::put($paginationCacheKey, $pagination, now()->addDays(1));
        } else {
            $pagination = Cache::get($paginationCacheKey);
        }
        $page = $book->pageByPageId($pageId);
        if($page == null){
            abort(404);
        }
        // $currrentPage = $page < $pagination['firstPage'] ? $pagination['firstPage'] : $page;
        $pagination['previousPage'] = $book->previousPageId($pageId, $part);
        $pagination['nextPage'] = $book->nextPageId($pageId, $part);
        $pagination['currentPage'] = $page;
        $pagination['currentPart'] = $part;
        $pagination['pPage'] = $book->pPage($pageId);

        // Adjust page if needed
        $pageId = $pagination['firstPage'] > $pageId ? $pagination['firstPage'] : $pageId;

        // Fetch content with specific query
        $query = BookContent::where('book_id', $book->id)->where('page_id', $pageId);

        if ($part) {
            $query->where('part', $part);
        }

        $content = $query->first(['nass', 'page_id', 'html']);

        if ($content == null && $book->hasContent() && count($book->contents) > 0) {
            return back()->with('error', 'الصفحة غير موجودة');
        }

        // Process HTML content
        if ($content != null && ($content->html == null || empty($content->html))) {
            $titles = $book->titles->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level,
                ];
            });

            $nass = BookContentStyleHelper::applyStyle($content, $titles);
            $content->html = $nass;
            $content->save();
        } else {
            if ($content != null) {
                $nass = $content->html;
            }
        }

        // Process titles structure
        $titlesResult = $this->processTitles($book, $content->page_id ?? 0);

        // Create view data array for caching
        $viewData = [
            'book' => $book,
            'page' => $page,
            'content' => $nass ?? '',
            'pagination' => $pagination,
            'titles' => $titlesResult['organized'],
            'activeTitles' => $titlesResult['active'],
            'currentTitle' => $titlesResult['currentTitle'],
        ];

        // Cache the view data if caching is enabled
        if ($this->enableCaching) {
            Cache::put($cacheKey, $viewData, now()->addHours(24));
        }

        return view('front_v2.book_read.page', $viewData);
    }

    /**
     * Process titles for the book page
     */
    private function processTitles($book, $pageId)
    {
        // Prepare titles as a flat array sorted by their order (title_id)
        $titles = $book->titles
            ->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level ?? 1, // Ensure level is set, default to 1
                ];
            })
            ->sortBy('title_id')
            ->values();

        $titlesOrganized = [];
        $stack = [];
        $activeTitles = [];

        // Build the tree using a stack
        foreach ($titles as $i => $title) {
            $node = [
                'title' => $title['title'],
                'title_id' => $title['title_id'],
                'children' => [],
                'active' => false, // will be set later
                'level' => $title['level'],
            ];

            // Pop stack until we find a parent with lower level
            while (!empty($stack) && $stack[count($stack) - 1]['level'] >= $node['level']) {
                array_pop($stack);
            }

            if (empty($stack)) {
                $titlesOrganized[] = $node;
                $stack[] = &$titlesOrganized[count($titlesOrganized) - 1];
            } else {
                $parent = &$stack[count($stack) - 1];
                $parent['children'][] = $node;
                $stack[] = &$parent['children'][count($parent['children']) - 1];
            }
        }

        // Helper to set active nodes
        $setActive = function (&$nodes) use ($pageId, &$setActive) {
            $anyActive = false;
            foreach ($nodes as &$node) {
                $isActive = false;
                if ($node['title_id'] == $pageId) {
                    $isActive = true;
                }
                if (!empty($node['children'])) {
                    $childActive = $setActive($node['children']);
                    $isActive = $isActive || $childActive;
                }
                $node['active'] = $isActive;
                if ($isActive) {
                    $anyActive = true;
                }
            }
            unset($node);
            return $anyActive;
        };
        $setActive($titlesOrganized);

        // Gather active titles (top-level ones that are active)
        foreach ($titlesOrganized as $title) {
            if ($title['active']) {
                $activeTitles[] = $title;
            }
        }

        //TODO http://127.0.0.1:8000/book/KdPRzpPa/page/324/1
        //TODO when there is no title on page activate last title with no childeren
        //if there is no activeTitles get previouse first title
        while (empty($activeTitles) && $pageId > 0) {
            $pageId = $pageId -1;
            $setActive($titlesOrganized);
            foreach ($titlesOrganized as $title) {
                if (count($activeTitles) > 0) {
                    break;
                }
                if ($title['active']) {
                    $activeTitles[] = [
                        'title' => $title['title'],
                        'title_id' => $title['title_id'],
                        'level' => $title['level'],
                        'active' => true,
                    ];
                }
            }
            if (count($activeTitles) > 0) {
                break;
            }
        }

        $firstTitleInLatestLevel = count($activeTitles) > 0 ? $activeTitles[count($activeTitles) - 1] : '';
        foreach ($titlesOrganized as $title) {
            if ($title['active']) {
                $firstTitleInLatestLevel = $title;
                if (!empty($title['children'])) {
                    foreach ($title['children'] as $child) {
                        if (!empty($child['active'])) {
                            foreach ($child['children'] as $subChild) {
                                if ($subChild['active']) {
                                    $firstTitleInLatestLevel = $subChild;
                                    break 2;
                                }
                            }
                        } else {
                            if ($child['active']) {
                                $firstTitleInLatestLevel = $child;
                                break;
                            }
                        }
                    }
                }
                break;
            }
        }

        // if($firstTitleInLatestLevel == ''){
        //     //   Helper to get the deepest active title
        // $getDeepest = function ($nodes) use (&$getDeepest) {
        //     $deepestTitle = '';
        //     $maxLevel = -1;
        //     foreach ($nodes as $node) {
        //         if ($node['active']) {
        //             $currentTitle = $node['title'];
        //             $currentLevel = $node['level'];
        //             if (!empty($node['children'])) {
        //                 [$childTitle, $childLevel] = $getDeepest($node['children']);
        //                 if ($childLevel > $currentLevel) {
        //                     $currentTitle = $childTitle;
        //                     $currentLevel = $childLevel;
        //                 }
        //             }
        //             if ($currentLevel > $maxLevel) {
        //                 $maxLevel = $currentLevel;
        //                 $deepestTitle = $currentTitle;
        //             }
        //         }
        //     }
        //     return [$deepestTitle, $maxLevel];
        // };
        // $firstTitleInLatestLevel = $getDeepest($titlesOrganized);

        // }

        return [
            'organized' => $titlesOrganized,
            'active' => $activeTitles,
            'currentTitle' => $firstTitleInLatestLevel['title'] ?? '',
        ];
    }

    public function title($id, $titleId)
    {
        $cacheKey = "book_title_redirect_{$id}_{$titleId}";

        // Check if caching is enabled
        if ($this->enableCaching && Cache::has($cacheKey)) {
            $redirectUrl = Cache::get($cacheKey);
            return redirect()->to($redirectUrl);
        }

        $book = Book::where('id', $id)->first(['id', 'title']);
        if (!$book) {
            return response()->json(['message' => 'Book not found'], 404);
        }

        $title = $book->titles->where('title_id', $titleId)->first();
        if (!$title) {
            return response()->json(['message' => 'Title not found'], 404);
        }

        $bookContent = BookContent::where('book_id', $book->id)
            ->where('page_id', $title->title_id)
            ->first(['page', 'part']);

        $redirectUrl = "/book/$id/page/$bookContent->page/$bookContent->part";

        // Cache the redirect URL if caching is enabled
        if ($this->enableCaching) {
            Cache::put($cacheKey, $redirectUrl, now()->addDays(7));
        }

        return redirect()->to($redirectUrl);
    }

    /**
     * Clear cache data for a specific book
     *
     * @param string $id The id of the book
     */
    public static function clearSpecificBookCache($id)
    {
        // Define cache key patterns for the specific book
        $cacheKeys = ["book_view_{$id}", "book_page_{$id}_", "book_{$id}", "pagination_{$id}_", "book_title_redirect_{$id}_"];

        foreach ($cacheKeys as $prefix) {
            // Use a wildcard search to find matching keys
            $keys = Cache::getPrefix() . $prefix . '*';
            self::clearCacheByPattern($keys);
        }

        return response()->json(['message' => "Cache for book with id {$id} cleared successfully."]);
    }

    /**
     * Helper function to clear cache by pattern
     *
     * @param string $pattern
     */
    public static function clearCacheByPattern($pattern)
    {
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $redis = Cache::getStore()->connection();
            $keys = $redis->keys($pattern);
            foreach ($keys as $key) {
                $redis->del($key);
            }
        } else {
            // For non-Redis stores, Laravel does not support wildcard deletion
            // You may need to manually track and clear keys
            throw new \Exception('Wildcard cache clearing is only supported for Redis.');
        }
    }
}
