<?php

namespace App\Http\Controllers\Front;

use App\Helpers\BookContentStyleHelper;
use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Models\BookContent;
use App\Models\BookReport;
use App\Models\BookReportDetail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BookPageController extends Controller
{
    // Add a variable to enable or disable caching
    private $enableCaching = true;

    //* get book info
    public function show($id)
    {
        $book = Book::where('id', $id)->first();
        if (!$book) {
            abort(404);
        }
        $firstPage = $book->firstPageId(null) ?? 1;
        $firstPart = $book->firstPart() ?? null;
        redirect()
            ->to("/book/$id/page/$firstPage/$firstPart")
            ->send();
    }
    public function page($id, $page, $part = null)
    {
        // get page id from page and redirect to pageId
        $book = Book::where('id', $id)->first();
        if (!$book) {
            abort(404);
        }
        $pageId = $book->pageIdByPage($page, $part);
        if ($pageId == null) {
            abort(404);
        }
        redirect()
            ->to("/book/$id/page/$pageId/$part")
            ->send();
    }

    //* get book page content
    public function pageId($id, $pageId, $part = null)
    {
        $isAuth = auth()->check();
        if ($isAuth) {
            $this->enableCaching = false;
        }

        $cacheKey = "book_page_{$id}_{$pageId}";
        $ip = $_SERVER['REMOTE_ADDR'];

        // Check if caching is enabled
        if ($this->enableCaching && Cache::has($cacheKey)) {
            $viewData = Cache::get($cacheKey);

            BookReportDetail::create([
                'ip_address' => $ip,
                'book_id' => $viewData['book']->id,
                'type' => 'read',
            ]);
            return view('front_v2.book_read.page', $viewData);
        }

        // Get book data (with caching)
        $bookCacheKey = 'book_' . $id;
        if (!Cache::has($bookCacheKey) || !$this->enableCaching) {
            //check if hash only numbers then get book by id
            if (is_numeric($id)) {
                $book = Book::where('id', $id)->first();
                if ($book == null) {
                    abort(404);
                }
                $id = $book->id;
            }

            $book = Book::with(['author:id,nickname,name,description,death_date', 'categories:id,title', 'titles:book_id,id,title_id,title,level', 'files:book_id,id,file_name,file_path,file_size,file_number,part'])
                ->where('id', $id)
                ->first(['id', 'title', 'image', 'summary', 'author_id', 'created_at', 'application', 'website']);
            if (!$book) {
                return response()->json(['message' => 'Book not found'], 404);
            }

            if ($book->application != 1 && $book->website != 1 && !$isAuth) {
                abort(404);
            }

            $ip = $_SERVER['REMOTE_ADDR'];
            BookReportDetail::create([
                'ip_address' => $ip,
                'book_id' => $book->id,
                'type' => 'read',
            ]);
            if ($this->enableCaching) {
                Cache::put($bookCacheKey, $book, now()->addDays(7));
            }
        } else {
            $book = Cache::get($bookCacheKey);
        }

        $part = $book->partByPageId($pageId);

        // Get pagination data (with caching)
        $paginationCacheKey = "pagination_{$id}_" . ($part ?? 'all');
        if (!Cache::has($paginationCacheKey) || !$this->enableCaching) {
            $pagination = [
                'firstPage' => $book->firstPageId($part),
                'lastPage' => $book->lastPageId($part),
                'totalPages' => $book->totalPages(),
                'firstPart' => $book->firstPart(),
                'lastPart' => $book->lastPart(),
            ];
            Cache::put($paginationCacheKey, $pagination, now()->addDays(1));
        } else {
            $pagination = Cache::get($paginationCacheKey);
        }
        $page = $book->pageByPageId($pageId);
        if ($page == null) {
            abort(404);
        }
        // $currrentPage = $page < $pagination['firstPage'] ? $pagination['firstPage'] : $page;
        $pagination['previousPage'] = $book->previousPageId($pageId);
        $pagination['nextPage'] = $book->nextPageId($pageId);
        $pagination['currentPage'] = $page;
        $pagination['currentPart'] = $part;
        $pagination['pPage'] = $book->pPage($pageId);
        // Adjust page if needed
        $pageId = $pagination['firstPage'] > $pageId ? $pagination['firstPage'] : $pageId;

        // Fetch content with specific query
        $query = BookContent::where('book_id', $book->id)->where('page_id', $pageId);

        $content = $query->first(['nass', 'page_id', 'html']);

        if ($content == null && $book->hasContent() && count($book->contents) > 0) {
            return back()->with('error', 'الصفحة غير موجودة');
        }

        // Process HTML content
        if ($content != null && ($content->html == null || empty($content->html))) {
            $titles = $book->titles->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level,
                ];
            });

            $nass = BookContentStyleHelper::applyStyle($content, $titles);
            $content->html = $nass;
            $content->save();
        } else {
            if ($content != null) {
                $nass = $content->html;
            }
        }

        // Process titles structure
        $titlesResult = $this->processTitles($book, $content->page_id ?? 0);

        // Create view data array for caching
        $viewData = [
            'book' => $book,
            'page' => $page,
            'content' => $nass ?? '',
            'pagination' => $pagination,
            'titles' => $titlesResult['organized'],
            'activeTitles' => $titlesResult['active'],
            'currentTitle' => $titlesResult['currentTitle'],
        ];

        // Cache the view data if caching is enabled
        if ($this->enableCaching) {
            Cache::put($cacheKey, $viewData, now()->addHours(24));
        }

        return view('front_v2.book_read.page', $viewData);
    }

    /**
     * Process titles for the book page
     *
     * @param object $book The book object containing titles
     * @param int $pageId The current page ID
     * @return array Organized titles, active titles, and current title
     */
    private function processTitles($book, $pageId)
    {
        // Step 1: Prepare and normalize titles
        $titles = $this->prepareTitles($book);

        // Step 2: Build hierarchical structure
        $titlesOrganized = $this->buildTitleHierarchy($titles);

        // Step 3: Mark active titles based on pageId
        $this->markActiveTitles($titlesOrganized, $pageId);

        // Step 4: Extract active titles
        $activeTitles = $this->extractActiveTitles($titlesOrganized);

        // Step 5: Find fallback titles if needed
        if (empty($activeTitles) && $pageId > 0) {
            $activeTitles = $this->findFallbackTitles($titlesOrganized, $pageId);
        }

        // Step 6: Determine the current title (most specific active title)
        $currentTitle = $this->determineCurrentTitle($titlesOrganized, $activeTitles);
        // Return the processed results
        return [
            'organized' => $titlesOrganized,
            'active' => $activeTitles,
            'currentTitle' => $currentTitle,
        ];
    }

    /**
     * Prepare and normalize titles from the book object
     *
     * @param object $book The book object
     * @return array Normalized and sorted titles
     */
    private function prepareTitles($book)
    {
        return $book->titles
            ->map(function ($title) {
                return [
                    'id' => $title->id,
                    'title_id' => $title->title_id,
                    'title' => $title->title,
                    'level' => $title->level ?? 1, // Ensure level is set, default to 1
                ];
            })
            ->sortBy('title_id')
            ->values();
    }

    /**
     * Build a hierarchical structure of titles based on their levels
     *
     * @param array $titles Flat array of normalized titles
     * @return array Hierarchical structure of titles
     */
    private function buildTitleHierarchy($titles)
    {
        $titlesOrganized = [];
        $stack = [];

        foreach ($titles as $title) {
            $node = [
                'title' => $title['title'],
                'title_id' => $title['title_id'],
                'children' => [],
                'active' => false, // will be set later
                'level' => $title['level'],
            ];

            // Pop stack until we find a parent with lower level
            while (!empty($stack) && $stack[count($stack) - 1]['level'] >= $node['level']) {
                array_pop($stack);
            }

            if (empty($stack)) {
                $titlesOrganized[] = $node;
                $stack[] = &$titlesOrganized[count($titlesOrganized) - 1];
            } else {
                $parent = &$stack[count($stack) - 1];
                $parent['children'][] = $node;
                $stack[] = &$parent['children'][count($parent['children']) - 1];
            }
        }

        return $titlesOrganized;
    }

    /**
     * Mark active titles based on the current page ID
     *
     * @param array &$titlesOrganized Reference to the organized titles
     * @param int $pageId Current page ID
     * @return void
     */
    private function markActiveTitles(&$titlesOrganized, $pageId)
    {
        $setActive = function (&$nodes) use ($pageId, &$setActive) {
            $anyActive = false;
            foreach ($nodes as &$node) {
                $isActive = false;
                if ($node['title_id'] == $pageId) {
                    $isActive = true;
                }
                if (!empty($node['children'])) {
                    $childActive = $setActive($node['children']);
                    $isActive = $isActive || $childActive;
                }
                $node['active'] = $isActive;
                if ($isActive) {
                    $anyActive = true;
                }
            }
            unset($node);
            return $anyActive;
        };

        $setActive($titlesOrganized);
    }

    /**
     * Extract active titles from the organized titles
     *
     * @param array $titlesOrganized Organized titles with active flags set
     * @return array List of active titles
     */
    private function extractActiveTitles($titlesOrganized)
    {
        $activeTitles = [];

        foreach ($titlesOrganized as $title) {
            if ($title['active']) {
                $activeTitles[] = $title;
            }
        }

        return $activeTitles;
    }

    /**
     * Find fallback titles when no active titles are found
     *
     * @param array &$titlesOrganized Reference to the organized titles
     * @param int &$pageId Reference to the page ID (will be decremented)
     * @return array List of fallback active titles
     */
    private function findFallbackTitles(&$titlesOrganized, &$pageId)
    {
        $activeTitles = [];

        while (empty($activeTitles) && $pageId > 0) {
            $pageId = $pageId - 1;
            $this->markActiveTitles($titlesOrganized, $pageId);

            for ($i = count($titlesOrganized) - 1; $i >= 0; $i--) {
                if ($titlesOrganized[$i]['title_id'] > $pageId) {
                    continue;
                }

                if (count($activeTitles) > 0) {
                    break;
                }

                if (empty($titlesOrganized[$i]['children'])) {
                    $activeTitles[] = [
                        'title' => $titlesOrganized[$i]['title'],
                        'title_id' => $titlesOrganized[$i]['title_id'],
                        'level' => $titlesOrganized[$i]['level'],
                        'active' => true,
                    ];
                    break;
                } else {
                    for ($j = count($titlesOrganized[$i]['children']) - 1; $j >= 0; $j--) {
                        if ($titlesOrganized[$i]['children'][$j]['title_id'] > $pageId) {
                            continue;
                        }
                        $activeTitles[] = $titlesOrganized[$i]['children'][$j];
                        break;
                    }
                    break;
                }
            }

            if (count($activeTitles) > 0) {
                break;
            }
        }

        return $activeTitles;
    }

    /**
     * Determine the current title (most specific active title)
     *
     * @param array $titlesOrganized Organized titles with active flags set
     * @param array $activeTitles List of active titles
     * @return string The current title text
     */
    private function determineCurrentTitle($titlesOrganized, $activeTitles)
    {
        $title = count($activeTitles) > 0 ? $activeTitles[count($activeTitles) - 1] : '';
        $firstTitleInLatestLevel = $title['title'];
        $returnTitle = '';
        foreach ($titlesOrganized as $title) {
            if ($title['active']) {
                $firstTitleInLatestLevel = $title['title'];
                if (!empty($title['children'])) {
                    foreach ($title['children'] as $child) {
                        if ($child['active']) {
                            if (!empty($child['children'])) {
                                foreach ($child['children'] as $subChild) {
                                    if ($subChild['active']) {
                                        if (empty($returnTitle)) {
                                            $returnTitle = $subChild['title'];
                                        }
                                        $firstTitleInLatestLevel = $subChild['title'];
                                        break;
                                    }
                                }
                            }
                            if (empty($returnTitle)) {
                                $returnTitle = $child['title'];
                            }
                            $firstTitleInLatestLevel = $child['title'];
                            break;
                        }
                    }
                } else {
                    if ($title['active']) {
                        if (empty($returnTitle)) {
                            $returnTitle = $title['title'];
                        }
                        $firstTitleInLatestLevel = $title['title'];
                        break;
                    }
                }
            }
        }
        return empty($returnTitle) ? $firstTitleInLatestLevel : $returnTitle;
    }

    public function title($id, $titleId)
    {
        $cacheKey = "book_title_redirect_{$id}_{$titleId}";

        // Check if caching is enabled
        if ($this->enableCaching && Cache::has($cacheKey)) {
            $redirectUrl = Cache::get($cacheKey);
            return redirect()->to($redirectUrl);
        }

        $book = Book::where('id', $id)->first(['id', 'title']);
        if (!$book) {
            return response()->json(['message' => 'Book not found'], 404);
        }

        $title = $book->titles->where('title_id', $titleId)->first();
        if (!$title) {
            return response()->json(['message' => 'Title not found'], 404);
        }

        $redirectUrl = "/book/$id/page/$title->title_id";

        // Cache the redirect URL if caching is enabled
        if ($this->enableCaching) {
            Cache::put($cacheKey, $redirectUrl, now()->addDays(7));
        }

        return redirect()->to($redirectUrl);
    }

    /**
     * Clear cache data for a specific book
     *
     * @param string $id The id of the book
     */
    public static function clearSpecificBookCache($id)
    {
        // Define cache key patterns for the specific book
        $cacheKeys = ["book_view_{$id}", "book_page_{$id}_", "book_{$id}", "pagination_{$id}_", "book_title_redirect_{$id}_"];

        foreach ($cacheKeys as $prefix) {
            // Use a wildcard search to find matching keys
            $keys = Cache::getPrefix() . $prefix . '*';
            self::clearCacheByPattern($keys);
        }

        return response()->json(['message' => "Cache for book with id {$id} cleared successfully."]);
    }

    /**
     * Helper function to clear cache by pattern
     *
     * @param string $pattern
     */
    public static function clearCacheByPattern($pattern)
    {
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $redis = Cache::getStore()->connection();
            $keys = $redis->keys($pattern);
            foreach ($keys as $key) {
                $redis->del($key);
            }
        } else {
            // For non-Redis stores, Laravel does not support wildcard deletion
            // You may need to manually track and clear keys
            throw new \Exception('Wildcard cache clearing is only supported for Redis.');
        }
    }
}
