<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

use App\ComprehensiveLibrary;
use App\Models\ContactMessage;
use App\Models\Seo as ModelsSeo;
use App\Seo;

class ContactController extends Controller
{
    public function index()
    {
        $about = "• بريد إدارة المكتبة الزيدية: لمراسلة الإدارة، وأيضا لما يتعلق بالبرنامج أو الكتب أو الموقع على البريد: <EMAIL>.
• وننبه على أنه يتم العناية بجميع الرسائل ودراستها، وإن تعذر الرد على بعضها، فعدم الرد على رسالة ما لا يعني إهمالها.
• يلتزم موقع المكتبة الزيدية بحفظ حقوق الملكية الفكرية للجهات والأفراد، وفق نظام حماية حقوق المؤلف. ونأمل ممن لديه ملاحظة على أي مادة تخالف نظام حقوق الملكية الفكرية أن يراسلنا.";
        $seo = ModelsSeo::where('page', '=', 'contact')->where('num', '=', 0)->first();
        return view('front.contact', compact('about', 'seo'));
    }

    public function contact_us()
    {
        return view('front_v2.common.contact-us');
    }

    public function submit_contact(Request $request)
    {
        $request->validate(
            [
                'name' => 'required',
                'email' => 'required|email',
                'subject' => 'required',
                'message' => 'required',
                'phone' => 'nullable',
                'g-recaptcha-response' => 'required',
            ],
            [
                'name.required' => 'من فضلك أدخل الاسم',
                'email.required' => 'من فضلك أدخل البريد الالكتروني',
                'email.email' => 'من فضلك أدخل بريد الالكتروني صحيح',
                'subject.required' => 'من فضلك اختر موضوع الرسالة',
                'message.required' => 'من فضلك أدخل الرسالة',
                'g-recaptcha-response.required' => 'من فضلك قم بالتأكيد',
            ],
        );

        // Verify reCAPTCHA
        if (config('app.env') != 'local') {
            $secret = env('RECAPTCHA_SECRET_KEY', '');
            $response = file_get_contents('https://www.google.com/recaptcha/api/siteverify?secret=' . $secret . '&response=' . $request['g-recaptcha-response']);
            $responseData = json_decode($response);

            if (!$responseData->success) {
                return redirect()->back()->with('error', 'فشل التحقق من reCAPTCHA. يرجى المحاولة مرة أخرى.');
            }
        }

        // Save contact message
        $message = new ContactMessage();
        $message->name = $request->name;
        $message->email = $request->email;
        $message->message =$request->subject . ' : ' . $request->message;

        if ($request->has('phone')) {
            $message->phone = $request->phone;
        }

        $message->save();

        return redirect()->back()->with('success', 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.');
    }

    public function send_message(Request $request)
    {
        if (!$request->has('g-recaptcha-response') || $request['g-recaptcha-response'] == '') {
            return response()->json(['success' => false, 'errors' => 'من فضلك قم بالتأكيد ', 'code' => 201], 201);
        } else {
            $secret = '6LcGHzYkAAAAAEoNJG4b3Y_aMkQ92vtb7ygIgXZS';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://www.google.com/recaptcha/api/siteverify?secret=' . $secret . '&response=' . $request['g-recaptcha-response']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            $response = json_decode($response);

            if ($response->success) {
                $validator = Validator::make(
                    $request->all(),
                    [
                        'name' => 'required',
                        'email' => 'nullable|email',
                        'message' => 'required',
                    ],
                    [
                        'name.required' => 'من فضلك أدخل الاسم',
                        // 'email.required'=> ('من فضلك أدخل البريد الالكتروني'),
                        'email.email' => 'من فضلك أدخل بريد الالكتروني صحيح',
                        'message.required' => 'من فضلك أدخل الرسالة',
                    ],
                );
                if ($validator->fails()) {
                    return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
                }
                $message = new ContactMessage();
                if ($request->has('name')) {
                    $message->name = $request->name;
                }
                if ($request->has('email')) {
                    $message->email = $request->email;
                }
                $message->message = $request->message;
                if ($request->hasFile('image')) {
                    $image = $request->file('image');
                    $imageName = time() . '.' . $image->getClientOriginalExtension();
                    $destinationPath = public_path('/uploads/messages');
                    $image->move($destinationPath, $imageName);
                    $message->image = '/uploads/messages/' . $imageName;
                }
                $message->save();

                return response()->json(['success' => true, 'message' => 'تم الارسال بنجاح', 'code' => 200]);
            } else {
                return response()->json(['success' => false, 'errors' => 'من فضلك قم بالتأكيد ', 'code' => 201], 201);
            }
        }
    }
}
