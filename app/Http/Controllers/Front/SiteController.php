<?php

namespace App\Http\Controllers\Front;

use App\ComprehensiveLibrary;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\IpAddress;
use App\Models\Author;
use App\Models\Book;
use App\Models\Category;
use App\Models\Seo as ModelsSeo;
use App\Seo;
use Session;

use Illuminate\Validation\Rule;

class SiteController extends Controller
{
    public function sort_books(Request $request)
    {
        $books = Book::select('books.id', 'books.title',  'books.image','books.author', 'authors.name', 'authors.death_date')
        ->join('authors', 'authors.id', '=', 'books.author')->where('books.website', 1);
        if($request->name == 'sort_by')
        {
            $sort_by = htmlentities($request->sort_by);
            if($sort_by == '' || $sort_by == 'alphabetical')
            {
                $books = $books->orderBy('books.title');
            }
            else if($sort_by == 'death_date')
            {
                $books = $books->orderBy('authors.death_date')->orderBy('title','ASC');
            }
        }
        else if($request->name == 'sort_by_cat')
        {
            $sort_by = htmlentities($request->sort_by);
            if($sort_by == '' || $sort_by == 'alphabetical')
            {
                $category = Category::with('books')->where('id',$request->cat)->first();
                $books = $category->books()->orderBy('title','ASC');
            }
            else if($sort_by == 'death_date')
            {
                $category = Category::with('books')->where('id',$request->cat)->first();
                $books = $category->books()->orderBy('authors.death_date')->orderBy('title','ASC');
            }
        }
        else
        {
            $books = $books->orderBy('books.title');

        }
        $books = $books->get();
        return view('partials.sorted_books', compact('books'));
         foreach($books as $book):
            ?>
            <div class="col-lg-3 col-md-6 col-6">
                <div class="product-item book_item">
                    <div class="product-thumb pro">
                        <a href="<?= url('Book/'.$book->id) ?>"><img src="<?=asset($book->image)?>" alt="<?= $book->title ?>"></a>                        
                    </div>
                    <div class="product-content pro_title">
                        <h5><a href="<?= url('Book/'.$book->id) ?>"> <?= $book->title ?></a></h5>
                        <h6><a href="<?= url('Book/'.$book->id) ?>"><?php if($book->author != Null):?> <?= $book->author->name ?> <?php endif; ?></a></h6>                 
                    </div>
                </div>                    
            </div>
           <?php
         endforeach;

    }

    public function sort_others(Request  $request){
        $authors = Author::orderby('name','ASC');
        if($request->has('sort_by_auth') )
        {
            $sort_by = htmlentities($request->sort_by_auth);
            if($sort_by == '' || $sort_by == 'alphabetical')
            {
                $authors = $authors->orderBy('name','ASC');
            }
            else if($sort_by == 'death_date')
            {
                $authors = $authors->orderBy('death_date','ASC')->orderby('name','ASC');
            }
        }else{
            $authors = $authors->orderBy('name','ASC');
            
        }
        $authors = $authors->get();
        foreach($authors as $author):
            ?>
             <div class="col-xl-3 col-lg-4 col-sm-6 col-12">
                <div class="card p-2 mb-4 text-center border-none border_auth">
                <img <?php if($author->image == Null):?> src="<?=asset('site/images/team/01.png')?>" <?php else: ?>src="<?=asset($author->image)?>" <?php endif; ?> class="card-img-top auth_img" alt="product">
                    <div class="card-body">
                        <a href="<?=url('Author/'.$author->id)?>"><h6 class="card-title mb-0">  <?= $author->name?></h6></a>
                        
                   
                    </div>
                    <div class="card-footer">
                    <a href="<?=url('Author/'.$author->id)?>">	<p class="card-text mb-2 card_auth"> <i class="fa fa-book"></i> <?= count(books_author($author->id)) ?>  كتاب</p> </a>
                    </div>
                </div>
            </div>
            <?php

        endforeach;
    }
    public function index(){
        $books = Book::where('website', 1)->orderby('title','ASC')->take(16)->get();
        $seo = ModelsSeo::where('page', '=', 'home')->where('num', '=', 0)->first();

        return view('welcome',compact('books','seo'));
    }
   

   public function Comprehensiv_Library(){
       $book = ComprehensiveLibrary::first();
       $ip = $_SERVER['REMOTE_ADDR'];
       $ip_found = IpAddress::where('ip_address',$ip)->where('book',$book->id)->get();
       if(count($ip_found) == 0){
           $ips = new IpAddress;
           $ips->ip_address = $ip;
          $ips->book = $book->id;
           $ips->save();
       }
       $seo = Seo::where('page', '=', 'comprehensive_library')->where('num', '=', $book->id)->first();
       if($seo === NULL){
           $seo = new Seo;
              $seo->page = 'comprehensive_library';
              $seo->num = $book->id;
              $seo->title = $book->title;
              $seo->ar_title = $book->title;
              $seo->text = $book->summary;
              $seo->ar_text = $book->summary;
              $seo->keywords = $book->title;
              $seo->ar_keywords = $book->title;
              $seo->name = $book->title;
              $seo->save();
      }
       return view('comprehensive_library',compact('book','seo'));

   }
}
