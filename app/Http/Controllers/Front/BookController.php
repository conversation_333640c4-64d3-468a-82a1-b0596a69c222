<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Models\Book;
use App\Models\Seo;
use App\Models\Category;
use App\Models\IpAddress;
use App\Models\Author;
use App\Models\BookCategories;
use App\Models\BookContent;
use App\Models\BookFile;
use App\Models\BookFiles;
use App\Models\BookReport;
use App\Models\BookReportDetail;
use Illuminate\Support\Facades\Storage;

class BookController extends Controller
{
    public function all_books(Request $request)
    {
        $user = auth()->user();
        $sort_by = 'alphabetical';
        if ($request->has('sort_by')) {
            if ($request->sort_by == 'death_date') {
                $sort_by = 'death_date';
            } elseif ($request->sort_by == 'alphabetical') {
                $sort_by = 'alphabetical';
            } elseif ($request->sort_by == 'latest') {
                $sort_by = 'latest';
            } elseif ($request->sort_by == 'oldest') {
                $sort_by = 'oldest';
            }
        }
        if ($sort_by == 'alphabetical') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->orderby('title', 'ASC')
                ->with(['author'])
                ->paginate(16);
        } elseif ($sort_by == 'latest') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->orderby('created_at', 'DESC')
                ->with(['author'])
                ->paginate(16);
        } elseif ($sort_by == 'oldest') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->orderby('created_at', 'ASC')
                ->with(['author'])
                ->paginate(16);
        } elseif ($sort_by == 'death_date') {
            $books = Book::select('books.id', 'books.title', 'books.image', 'books.author_id', 'authors.name', 'authors.death_date')->join('authors', 'authors.id', '=', 'books.author_id')->where('books.website', 1)->orderBy('authors.death_date')->orderBy('title', 'ASC')->paginate(16);
        }

        $seo = Seo::where('page', '=', 'all_books')->where('num', '=', 0)->first();
        $active_page = 'books';
        return view('front.all_books', compact('books', 'seo', 'active_page', 'sort_by'));
    }

    public function Books_Cat($id, Request $request)
    {
        $cat = Category::where('id', $id)->firstOrFail();
        if($cat === null){
            abort('404');
        }

        $user = auth()->user();

        $sort_by = 'alphabetical';
        if ($request->has('sort_by')) {
            if ($request->sort_by == 'death_date') {
                $sort_by = 'death_date';
            } elseif ($request->sort_by == 'alphabetical') {
                $sort_by = 'alphabetical';
            } elseif ($request->sort_by == 'latest') {
                $sort_by = 'latest';
            } elseif ($request->sort_by == 'oldest') {
                $sort_by = 'oldest';
            }
        }
        $books_n_cat = BookCategories::where('category_id', $id)->pluck('book_id')->toArray();
        if ($sort_by == 'alphabetical') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->whereIn('id', $books_n_cat)
                ->orderby('title', 'ASC')
                ->with(['author'])
                ->paginate(16);
        } elseif ($sort_by == 'latest') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->whereIn('id', $books_n_cat)
                ->orderby('created_at', 'DESC')
                ->with(['author'])
                ->paginate(16);
        } elseif ($sort_by == 'oldest') {
            $books = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })
                ->whereIn('id', $books_n_cat)
                ->orderby('created_at', 'ASC')
                ->with(['author'])
                ->paginate(16);
        } else {
            $books = Book::select('books.id', 'books.title', 'books.image', 'books.author_id', 'authors.name', 'authors.death_date')
                ->join('authors', 'authors.id', '=', 'books.author_id')
                ->whereIn('books.id', $books_n_cat)
                ->where('books.website', 1)
                ->orderBy('authors.death_date')
                ->orderBy('title', 'ASC')
                ->with(['author'])
                ->paginate(16);
        }

        $seo = Seo::where('page', '=', 'books_cat')->where('num', '=', $cat->id)->first();
        if ($seo === null) {
            $seo = new Seo();
            $seo->page = 'books_cat';
            $seo->num = $cat->id;
            $seo->title = $cat->title;
            $seo->ar_title = $cat->title;
            $seo->text = $cat->title;
            $seo->ar_text = $cat->title;
            $seo->keywords = $cat->title;
            $seo->ar_keywords = $cat->title;
            $seo->name = $cat->title;
            $seo->save();
        }
        $active_page = 'books';
        return view('front.cats', compact('books', 'cat', 'seo', 'active_page', 'sort_by'));
    }

    public function book($id)
    {
        $user = auth()->user();

        $book = Book::where('id', $id)->first();
        if($book === null){
            abort('404');
        }
        if ($book->website == 0) {
            if ($user === null || $user->isSuperAdmin() == false) {
                abort('404');
            }
        }
        $category = $book->category();
        $similerIds = $category->books->pluck('id')->toArray();
        $simaler = Book::whereIn('id', $similerIds)
            ->where('id', '!=', $book->id)
            ->when($user === null, function ($query) {
                return $query->where('website', 1);
            })
            ->take(4)
            ->get();
        $bookReport = BookReport::where('type', 'web_view')->where('book_id', $id)->first();

        // $simaler = Book::where('cat',$book->cat)->where('id','!=',$book->id)->take(4)->get();
        $ip = $_SERVER['REMOTE_ADDR'];

        $data = [
            'ip_address' => $ip,
            'book_id' => $book->id,
            'type' => 'web_view',
        ];
        BookReportDetail::create($data);

        if ($bookReport === null) {
            $bookReport = new BookReport();
            $bookReport->book_id = $book->id;
            $bookReport->type = 'web_view';
            $bookReport->count = 1;
            $bookReport->save();
        } else {
            $bookReport->count = $bookReport->count + 1;
            $bookReport->timestamps = false;
            $bookReport->save();
        }

        $seo = Seo::where('page', '=', 'books')->where('num', '=', $book->id)->first();

        if ($seo === null) {
            $seo = new Seo();
            $seo->page = 'books';
            $seo->num = $book->id;
            $seo->title = $book->title;
            $seo->ar_title = $book->title;
            $seo->text = $book->title;
            $seo->ar_text = $book->title;
            $seo->keywords = $book->title;
            $seo->ar_keywords = $book->title;
            $seo->name = $book->title;
            $seo->save();
        } else {
            $seo->title = $book->title;
            $seo->ar_title = $book->title;
            $seo->text = strip_tags(nl2br(html_entity_decode($book->summary)));
            $seo->ar_text = strip_tags(nl2br(html_entity_decode($book->summary)));
            $seo->keywords = $book->title;
            $seo->ar_keywords = $book->title;
            $seo->name = $book->title;
            $seo->save();
        }

        $active_page = 'books';
        $book_page = true;
        $book_image = $book->image;

        $isReadable = count($book->contents) > 0;
        $isAuth = auth()->check();
        return view('front.book', compact('book', 'simaler', 'seo', 'active_page', 'book_page', 'book_image', 'isReadable', 'isAuth'));
    }

    public function book_search(Request $request)
    {
        $user = auth()->user();
        $seo = Seo::where('page', '=', 'books_search')->where('num', '=', 0)->first();
        $authors_arr = [];
        $cats_arr = [];
        $book_arr = [];
        $search_term = null;

        // Support both old and new query parameter names
        if ($request->get('search_for')) {
            $search_term = htmlentities($request->search_for);
        } elseif ($request->get('q')) {
            $search_term = htmlentities($request->q);

            // Record search in history if using new parameter
            if ($search_term && class_exists('App\Models\SearchHistory')) {
                \App\Models\SearchHistory::recordSearch(
                    $search_term,
                    $request->get('search_type', 'all'),
                    $request->ip()
                );
            }
        }

        $search_terms = [];
        $search_terms = replace_letters($search_term);

        // search by authors
        $books = Book::when($user === null, function ($query) {
            return $query->where('website', 1);
        })->orderby('id', 'Desc');

        // Apply category filter if provided
        if ($request->filled('category')) {
            $books = $books->whereHas('categories', function($query) use ($request) {
                $query->where('category_id', $request->category);
            });
        }

        // Apply author filter if provided
        if ($request->filled('author')) {
            $books = $books->where('author_id', $request->author);
        }

        // Apply sorting if provided
        if ($request->filled('sort')) {
            switch ($request->sort) {
                case 'title_asc':
                    $books = $books->orderBy('title', 'asc');
                    break;
                case 'title_desc':
                    $books = $books->orderBy('title', 'desc');
                    break;
                case 'newest':
                    $books = $books->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $books = $books->orderBy('created_at', 'asc');
                    break;
                default:
                    // Default relevance sorting handled below
                    break;
            }
        }

        // Handle search type
        $search_type = $request->get('search_type', 'all');

        if ($search_type === 'authors' || $search_type === 'all') {
            $authors = Author::where('name', 'like', '%' . $search_terms[0] . '%');
            for ($i = 1; $i < count($search_terms); $i++) {
                $authors = $authors->orWhere('name', 'like', '%' . $search_terms[$i] . '%');
            }
            $authors = $authors->pluck('id');
            $active_page = 'books';
            if (count($authors) > 0) {
                $books = $books
                    ->when($user === null, function ($query) {
                        return $query->where('website', 1);
                    })
                    ->whereIn('author_id', $authors)
                    ->paginate(16);
                return view('front.books_search', compact('books', 'seo', 'active_page'));
            }
        }

        if ($search_type === 'all') {
            // search by cats
            $cats = Category::where('title', 'like', '%' . $search_terms[0] . '%');
            for ($i = 1; $i < count($search_terms); $i++) {
                $cats = $cats->orWhere('title', 'like', '%' . $search_terms[$i] . '%');
            }
            $cats = $cats->pluck('id');
            if (count($cats) > 0) {
                $books = $books
                    ->when($user === null, function ($query) {
                        return $query->where('website', 1);
                    })
                    ->whereHas('categories', function ($query) use ($cats) {
                        $query->whereIn('category_id', $cats);
                    })
                    ->paginate(16);
                return view('front.books_search', compact('books', 'seo', 'active_page'));
            }
        }

        if ($search_type === 'books' || $search_type === 'all') {
            // Book by title
            $books_search = Book::when($user === null, function ($query) {
                return $query->where('website', 1);
            })->where('title', 'like', '%' . $search_terms[0] . '%');
            for ($i = 1; $i < count($search_terms); $i++) {
                $books_search = $books_search->orWhere('title', 'like', '%' . $search_terms[$i] . '%');
            }
            $books_search = $books_search->pluck('id');
            if (count($books_search) > 0) {
                $books = $books->whereIn('id', $books_search)->paginate(16);
                return view('front.books_search', compact('books', 'seo'));
            }
        }

        if ($search_type === 'all_content' && class_exists('App\Models\BookSearch')) {
            // This would need to be implemented with the BookSearch model
            // For now, we'll just return the regular search results
        }

        // No Results
        $books = $books->where('id', 0)->paginate(16);
        return view('front.books_search', compact('books', 'seo', 'active_page'));
    }

    public function download_book($id)
    {
        $user = auth()->user();
        $book_file = BookFiles::findorfail($id);
        $book = Book::when($user === null, function ($query) {
            return $query->where('website', 1);
        })
            ->where('id', $book_file->book_id)
            ->first();
        if ($book === null) {
            abort('404');
        }
        $download = $book->download_count + 1;
        $book->download_count = $download;
        $book->timestamps = false;
        $book->save();
        $cdnUrl = $book_file->file_path;
        return redirect($cdnUrl);
    }

    /**
     * Autocomplete search functionality
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function autocomplete(Request $request)
    {
        $query = $request->input('query');
        $user = auth()->user();

        if (strlen($query) < 3) {
            return response()->json([]);
        }

        // Create array of search terms with Arabic letter variants
        $searchTerms = replace_letters($query);

        // Search in book content
        $results = BookContent::select('book_content.id', 'book_content.book_id', 'book_content.content as nass', 'books.title as book_title')
            ->join('books', 'book_content.book_id', '=', 'books.id')
            ->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->orWhere('book_content.content', 'like', '%' . $term . '%');
                }
            })
            ->when($user === null, function ($query) {
                return $query->whereHas('book', function ($q) {
                    $q->where('website', 1);
                });
            })
            ->with('book:id,title,author_id')
            ->limit(5)
            ->get();

        // Process results to show relevant content snippet
        $processedResults = $results->map(function ($item) use ($query) {
            // Find the position of the search term in the content
            $pos = mb_stripos($item->nass, $query);

            // If found, extract a snippet around the search term
            if ($pos !== false) {
                $start = max(0, $pos - 50);
                $length = 150;
                $content = mb_substr($item->nass, $start, $length);

                // Add ellipsis if we're not at the beginning/end
                if ($start > 0) {
                    $content = '...' . $content;
                }
                if ($start + $length < mb_strlen($item->nass)) {
                    $content .= '...';
                }

                $item->nass = $content;
            }

            return $item;
        });

        return response()->json($processedResults);
    }
}

function replace_letters($search)
{
    $results = [];
    $results[] = $search;
    $original = str_replace(['أ', 'إ'], 'ا', $search);
    $results[] = $original;
    $original_one = str_replace('ا', 'أ', $original);
    $results[] = $original_one;
    $original_two = str_replace('ا', 'إ', $original);
    $results[] = $original_two;
    for ($i = 0; $i < mb_strlen($original, 'utf-8'); $i++) {
        $pos = mb_strpos($original, 'ا', $i, 'utf-8');
        if ($pos !== false) {
            $wa = $original;
            $wa = mb_substr_replace($wa, 'أ', $pos, 1);
            $results[] = $wa;
        }
    }
    for ($i = 0; $i < mb_strlen($original, 'utf-8'); $i++) {
        $pos = mb_strpos($original, 'ا', $i, 'utf-8');
        if ($pos !== false) {
            $wa = $original;
            $wa = mb_substr_replace($wa, 'إ', $pos, 1);
            $results[] = $wa;
        }
    }
    $results = array_unique($results);
    $results = array_values($results);
    return $results;
}

function mb_substr_replace($str, $repl, $start, $length = null)
{
    preg_match_all('/./us', $str, $ar);
    preg_match_all('/./us', $repl, $rar);
    $length = is_int($length) ? $length : utf8_strlen($str);
    array_splice($ar[0], $start, $length, $rar[0]);
    return implode($ar[0]);
}
