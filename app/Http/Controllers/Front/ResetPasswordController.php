<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Validation\Rules;

class ResetPasswordController extends Controller
{
    public function index($token)
    {
        return view('front.reset_password', ['token' => $token]);
    }

    public function change_password(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ], [
            'token.required' => 'خطأ! من فضلك قم بالمحاولة في وقت لاحق',
            'email.required' => 'من فضلك أدخل البريد الإلكتروني',
            'email.email' => 'من فضلك أدخل بريد إلكتروني صحيح',
            'password.required' => 'من فضلك أدخل كلمة المرور',
            'password.confirmed' => 'كلمة المرور غير مؤكدة',
            'password.min' => 'كلمة المرور يجب ان لا تقل عن 8 احرف'
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($request->password),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        return $status == Password::PASSWORD_RESET
                    ? view('front.password_changed')
                    : back()->withInput($request->only('email'))
                        ->withErrors(['email' => __($status)]);
    }

    public function password_changed()
    {
        return view('front.password_changed');
    }
}
