<?php

namespace App\Http\Controllers\Front;

use App\Helpers\TextHelper;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Models\Seo;
use App\Models\Category;
use App\Models\Author;
use App\Models\Settings;
use App\RolesEnum;
use Illuminate\Support\Facades\Auth;
use App\Services\BookService;

class FronV2Controller extends Controller
{
    protected $bookService;

    public function __construct(BookService $bookService)
    {
        $this->bookService = $bookService;
    }

    public function index(Request $request)
    {
        $latestBooks = $this->bookService->getLatestBooks(12);
        $mostReadedBooksThisMonth = $this->bookService->getMostReadBooksThisMonth(12);
        $mostDownloadedBooksThisMonth = $this->bookService->getMostDownloadedBooksThisMonth(12);
        $selectedBooks = $this->bookService->getSelectedBooks(12);
        $mainCategories = $this->bookService->getMainCategoriesWithBookCounts();
        $seo = Seo::where('page', '=', 'home')->where('num', '=', 0)->first();
        $active_page = 'home';
        return view('front_v2.home', compact('latestBooks', 'mainCategories', 'seo', 'active_page', 'mostReadedBooksThisMonth', 'mostDownloadedBooksThisMonth', 'selectedBooks'));
    }

    public function books(Request $request)
    {
        $booksCount = Book::count();

        return view('front_v2.books.books', compact('booksCount'));
    }

    public function book($id)
    {
        $book = Book::with([
            'author' => function ($query) {
                $query->withCount('books');
            },
        ])->find($id);
        if (!$book) {
            abort(404);
        }

        $similarBooks = Book::where('id', '!=', $book->id)
            ->whereHas('categories', function ($query) use ($book) {
                $query->whereIn('category_id', $book->categories->pluck('id'));
            })
            ->take(12)
            ->get();

        return view('front_v2.book.book', compact('book', 'similarBooks'));
    }

    public function authors(Request $request)
    {
        $authorsQuery = Author::query();

        $authorsCount = $authorsQuery->count();

        return view('front_v2.authors.authors', compact('authorsCount'));
    }

    public function categories(Request $request)
    {
        $categories = Category::withCount(['books'])
            ->whereHas('books')
            ->get();

        return view('front_v2.categories.categories', compact('categories'));
    }

    public function category($id)
    {
        $category = Category::withCount(['books'])->findOrFail($id);

        return view('front_v2.category.category', compact('category'));
    }

    public function author($id)
    {
        $author = Author::withCount(['books'])
            ->with([
                'books' => function ($query) {
                    $query->with('categories');
                },
            ])
            ->findOrFail($id);

        return view('front_v2.author.author', compact('author'));
    }

    public function aboutUs()
    {
        $about = Settings::where('key', 'site_about')->first();
        if (!$about) {
            $about = Settings::create([
                'key' => 'site_about',
                'value' => '',
                'type' => 'text',
            ]);
        }
        return view('front_v2.common.about-us', compact('about'));
    }

    public function termsOfService()
    {
        return view('front_v2.common.terms-of-service');
    }

    public function contactUs()
    {
        return view('front_v2.common.contact-us');
    }

    public function authorsLivewire()
    {
        return view('front_v2.authors.index');
    }

    public function advancedSearch(Request $request)
    {
        $seo = Seo::where('page', '=', 'books_search')->where('num', '=', 0)->first();
        $active_page = 'search';

        // If there's a query, perform the search
        if ($request->filled('q')) {
            // Record the search in history
            if (class_exists('App\Models\SearchHistory')) {
                $searchQuery = TextHelper::searchForTextFormatter($request->q);
                \App\Models\SearchHistory::recordSearch($searchQuery, $request->get('search_type', 'all'), $request->ip());
            }

            // Redirect to the regular search with the advanced parameters
            return redirect()->to('/book_search?' . $request->getQueryString());
        }

        return view('front_v2.advanced_search', compact('seo', 'active_page'));
    }
}
