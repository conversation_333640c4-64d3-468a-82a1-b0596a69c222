<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Author;
use App\Models\Seo;

class AuthorController extends Controller
{
    public function index(Request $request)
    {
        $sort_by = 'alphabetical';
        if($request->has('sort_by'))
        {
            if($request->sort_by == 'death_date') {$sort_by = 'death_date';}
            if($request->sort_by == 'alphabetical') {$sort_by = 'alphabetical';}
        }
        
        if($sort_by == 'alphabetical')
        {
            $authors = Author::whereHas('books', function ($query) {
                $query->where('website', 1);
             })->orderby('name','ASC')->paginate(16);
        }
        else
        {
            $authors = Author::whereHas('books', function ($query) {
                $query->where('website', 1);
            })->orderby('death_date','ASC')->paginate(16);
        }

        $seo = Seo::where('page', '=', 'authors')->where('num', '=', 0)->first();
        $active_page = 'authors';
        return view('front.authors',compact('authors','seo', 'active_page', 'sort_by'));
    }
    public function show($id)
    {
        $author= Author::where('id',$id)->whereHas('books')->first();
        if ($author === NULL) {
            abort(404);
        }
        $seo = Seo::where('page', '=', 'authors')->where('num', '=', $author->id)->first();
        if($seo === NULL)
        {
            $seo = new Seo;
            $seo->page = 'authors';
            $seo->num = $author->id;
            $seo->title = $author->name;
            $seo->ar_title = $author->name;
            $seo->text = $author->description;
            $seo->ar_text = $author->name;
            $seo->keywords = $author->name;
            $seo->ar_keywords = $author->name;
            $seo->name = $author->name;
            $seo->save();
        }
        $active_page = 'authors';
        return view('front.author',compact('author','seo', 'active_page'));
    }
}
