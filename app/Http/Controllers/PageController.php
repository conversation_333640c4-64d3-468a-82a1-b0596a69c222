<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PageController extends Controller
{
    public function index()
    {
        $pages = Page::all();
        return view('modules.pages.index', compact('pages'));
    }

    public function create()
    {
        return view('modules.pages.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'status' => 'required|boolean',
        ]);

        $data = $request->except('status');
        $data['status'] = $request->status == '1' ? true : false;

        Page::create($data);

        return redirect()->route('pages.index')->with('success', 'تم إنشاء الصفحة بنجاح.');
    }

    public function show(Page $page)
    {
        dd($page);
        return view('modules.pages.show', compact('page'));
    }
    public function open($slug)
    {
        $page = Page::where('slug', $slug)->first();
        return view('modules.pages.page', compact('page'));
    }

    public function edit(Page $page)
    {
        return view('modules.pages.update', compact('page'));
    }

    public function update(Request $request, Page $page)
    {
        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'status' => 'required|boolean',
        ]);

        $data = $request->except('status');
        $data['status'] = $request->status == '1' ? true : false;

        $page->update($data);

        return redirect()->route('pages.index')->with('success', 'تم تحديث الصفحة بنجاح.');
    }

    public function destroy(Page $page)
    {
        $page->delete();

        return redirect()->route('pages.index')->with('success', 'تم حذف الصفحة بنجاح.');
    }
}
