<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Models\DataSync;

class DataSyncController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $type = ($request->type) ? $request->type : '';
        $excludeIds = ($request->excludeIds) ? explode(',', $request->excludeIds) : [];

        $query = Auth::user()->dataSyncs()->whereNotIn('id', $excludeIds);
        if ($type)
            $query->where('type', $type);
        $list = $query->get();

        $excludedDeletedList = Auth::user()->dataSyncs()->whereIn('id', $excludeIds)->onlyTrashed()->get();

        //*merge lists
        $list = $list->merge($excludedDeletedList);

        return response()->json(['success' => false, 'data' => $list, 'code' => 200], 200);
    }


    public function store(Request $request)
    {
        $list = $request->data ? $request->data : [];
        $returnList = [];
        foreach ($list as $item) {
            $result = DataSync::create([
                'type' => $item['type'],
                'data' => $item['data'],
                'user_id' => Auth::user()->id,
                'created_at_app' => $item['created_at'],
            ]);
            array_push($returnList, $result);
        }


        return response()->json(['success' => true, 'data' => $returnList, 'code' => 200], 200);
    }


    public function destroy(Request $request)
    {
        $list = $request->data ? $request->data : [];
        foreach ($list as $item) {
            $dataSyncList = Auth::user()->dataSyncs()->where('id', $item);
            if ($dataSyncList->count() > 0) {
                $dataSync = $dataSyncList->first();
                $dataSync->delete();
            }
        }

        return response()->json(['success' => true, 'message' => 'تم حذف البيانات', 'code' => 200], 200);
    }


    public function sync(Request $request)
    {
        $validations = [
            'deleted_ids' => 'array',
            'synced_ids' => 'array',
            'unsynced' => 'array',
            'updated' => 'array',
            'type' => 'required|string',
            'returnNew' => 'integer',

        ];

        $validator = validator::make($request->all(), $validations);


        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first(), 'code' => 400], 400);
        }
        //check if exist user
        if (!Auth::user()) {
            return response()->json(['success' => false, 'message' => 'المستخدم غير موجود', 'code' => 400], 400);
        }




        $deletedIds = $request->deleted_ids ? $request->deleted_ids : [];
        $syncedIds = $request->synced_ids ? $request->synced_ids : [];
        $unsynced = $request->unsynced ? $request->unsynced : [];
        $updated = $request->updated ? $request->updated : [];
        $type = ($request->type) ? $request->type : '';
        $returnNew = ($request->returnNew) ? $request->returnNew : 0;


        //*if type is settings 
        if ($type == 'settings' || $type == 'azzo' || $type == 'books') {
            if (count($unsynced) > 0) {
                $list = Auth::user()->dataSyncs()->where('type', $type)->get();
                foreach ($list as $item) {
                    $item->delete();
                }
                //*save unsynced settings
                $settings = $unsynced[0]['data'];
                $result = DataSync::create([
                    'type' => $type,
                    'data' => $settings,
                    'user_id' => Auth::user()->id,
                    'created_at_app' => 0,
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'تم حفظ البيانات',
                    'data' => [
                        'deleted' => [],
                        'synced' => [],
                        'new' => [$result],
                    ],
                    'code' => 200
                ], 200);
            } else {
                //*return settings
                $list = DataSync::where('type', $type)->where('user_id', Auth::user()->id)->get();
                return response()->json([
                    'success' => false,
                    'message' => 'تم جلب البيانات',
                    'data' => [
                        'deleted' => [],
                        'synced' => [],
                        'new' => count($list) > 0 ? [$list->first()] : [],
                    ],
                    'code' => 200
                ], 200);
            }
        }


        $list = Auth::user()->dataSyncs()->withTrashed()->where('type', $type)->get();

        //* check if all syncedIds are in list and not in deleted
        $deletedSyncedIds = [];
        foreach ($syncedIds as $id) {
            $dataSync = $list->where('id', $id)->first();
            if ($dataSync) {
                if ($dataSync->trashed())
                    array_push($deletedSyncedIds, $id);
            } else {
                array_push($deletedSyncedIds, $id);

            }
        }
        //*delete deletedSyncedIds
        foreach ($deletedIds as $item) {
            $dataSync = DataSync::where('id', $item)->first();
            if ($dataSync)
                $dataSync->delete();
        }
        //add deletedIds to deletedSyncedIds
        if (count($deletedIds) > 0)
            $deletedSyncedIds = array_merge($deletedSyncedIds, $deletedIds);


        //*save unsynced and return ids
        $newSyncedList = [];
        foreach ($unsynced as $item) {
            //*check if already exist by created_at
            $dataSync = DataSync::where('created_at_app', $item['created_at'])->first();
            if ($dataSync) {
                array_push($newSyncedList, $dataSync);
                continue;
            }
            $result = DataSync::create([
                'type' => $item['type'],
                'data' => $item['data'],
                'user_id' => Auth::user()->id,
                'created_at_app' => $item['created_at'],
            ]);
            array_push($newSyncedList, $result);
        }


        //*update list
        foreach ($updated as $item) {
            $decodedData = json_decode($item['data']);
            $sid = $decodedData->sync_id;
            $appCreatedAt = $decodedData->created_at;
            $isUpdated = false;
            if ($sid) {
                $dataSync = DataSync::where('id', $sid)->first();
                if ($dataSync) {
                    $dataSync->data = $item['data'];
                    $dataSync->created_at_app = $item['created_at'];
                    $dataSync->save();
                    $isUpdated = true;
                    array_push($newSyncedList, $dataSync);
                }
            }elseif($appCreatedAt){
                $dataSync = DataSync::where('created_at_app', $appCreatedAt)->first();
                if ($dataSync) {
                    $dataSync->data = $item['data'];
                    $dataSync->created_at_app = $item['created_at'];
                    $dataSync->save();
                    $isUpdated = true;
                    array_push($newSyncedList, $dataSync);
                }
            }
            if (!$isUpdated) {
                $result = DataSync::create([
                    'type' => $item['type'],
                    'data' => $item['data'],
                    'user_id' => Auth::user()->id,
                    'created_at_app' => $item['created_at'],
                ]);
                array_push($newSyncedList, $result);
            }
        }

        //*synced list where not in unsynced and not in deletedSyncedIds
        $list = $list->whereNotIn('id', $deletedSyncedIds)->whereNotIn('id', $syncedIds)->where('deleted_at', null);
        $newList = [];
        if ($returnNew == 1)
            foreach ($list as $item) {
                array_push($newList, $item);
            }




        return response()->json([
            'success' => false,
            'data' => [
                'deleted' => $deletedSyncedIds,
                'synced' => $newSyncedList,
                'new' => $newList,
            ],
            'code' => 200
        ], 200);
    }
}
