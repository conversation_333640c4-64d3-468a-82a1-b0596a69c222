<?php
namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Models\AppUserNotification;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookReport;
use App\Models\Category;
use App\Models\ContactMessage;
use App\Models\CountReport;
use App\Models\DataSync;
use App\Models\User;
use App\RolesEnum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;

class OldApisController extends Controller
{
    public function app_download_counter(Request $request)
    {
        $setting = CountReport::where('type', 'app_download')->first();
        if ($setting === null) {
            $setting = new CountReport();
            $setting->type = 'app_download';
            $setting->count = 0;
            $setting->save();
        }
        $setting->count = $setting->count + 1;
        if ($request->has('platform')) {
            $setting->extra = $request->platform;
        }
        $setting->save();
        return response()->json(['success' => true, 'code' => 200, 'message' => 'التحميل']);
    }

    public function book_download_counter(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'type' => 'required',
                'book' => 'required',
            ],
            [
                'type.required' => 'من فضلك أدخل نوع الملف',
                'book.required' => 'من فضلك أدخل رقم الكتاب',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }

        $book_counter_all_files = BookReport::where('type', $request->type)->where('book_id', $request->book)->first();
        if ($book_counter_all_files === null) {
            $book_counter_all_files = new BookReport();
            $book_counter_all_files->type = $request->type;
            $book_counter_all_files->book_id = $request->book;
            $book_counter_all_files->count = 0;
            $book_counter_all_files->save();
        }
        $book_counter_all_files->count = $book_counter_all_files->count + 1;
        $book_counter_all_files->save();
        return response()->json(['success' => true, 'code' => 200, 'message' => 'التحميل']);
    }

    public function app_helper(Request $request)
    {
        $files = [
            ['id' => 6, 'name' => 'القرآن', 'file' => asset('uploads/app_helpers_db/1709414269.zip'), 'version' => 1],
            ['id' => 7, 'name' => 'محاضرات رمضانية في تقريب معاني الآيات القرآنية', 'file' => asset('uploads/app_helpers_db/1704436145.zip'), 'version' => 2],
            ['id' => 8, 'name' => 'النهج القويم في إعراب القرآن الكريم', 'file' => asset('uploads/app_helpers_db/1710013886.zip'), 'version' => 6],
            ['id' => 9, 'name' => 'تفسير الثمرات اليانعة والأحكام الواضحة القاطعة', 'file' => asset('uploads/app_helpers_db/1704436294.zip'), 'version' => 3],
            ['id' => 10, 'name' => 'الكشاف عن حقائق التنزيل', 'file' => asset('uploads/app_helpers_db/1704436314.zip'), 'version' => 8],
            ['id' => 11, 'name' => 'مركز تفسير للدراسات القرآنية', 'file' => asset('uploads/app_helpers_db/1704436334.zip'), 'version' => 7],
            ['id' => 12, 'name' => 'زبر من الفوائد القرآنية ونوادر من الفرائد والفرائد القرآنية', 'file' => asset('uploads/app_helpers_db/1709125319.zip'), 'version' => 9],
            ['id' => 13, 'name' => 'الحديث', 'file' => asset('uploads/app_helpers_db/1704443201.zip'), 'version' => 10],
            ['id' => 14, 'name' => 'أحكام التجويد في كلمات العزيز الحميد', 'file' => asset('uploads/app_helpers_db/1709912418.zip'), 'version' => 4],
            ['id' => 15, 'name' => 'مركز تفسير للدراسات القرآنية', 'file' => asset('uploads/app_helpers_db/1708797831.zip'), 'version' => 5],
        ];
        foreach ($files as $file) {
            // $ff[] = ['id' => $file->id, 'name' => $file->name, 'file' => asset($file->file), 'version' => $file->version];
            $ff[] = $file;
        }
        return response()->json(['success' => true, 'code' => 200, 'data' => $ff]);
    }

    public function categories(Request $request)
    {
        $cats = Category::whereNULL('deleted_at');
        if ($request->has('search')) {
            $search = htmlentities($request->search);
            if ($search != '') {
                $cats = $cats->where('title', 'LIKE', '%' . $search . '%');
            }
        }

        if ($request->has('last_update_time')) {
            $date = date('Y-m-d H:i:s', strtotime($request->last_update_time));
            $cats = $cats->where('updated_at', '>=', $date);
        }

        $cats = $cats->get();
        $data = [];
        foreach ($cats as $cat) {
            if ($cat->image != '') {
                $image = asset($cat->image);
            } else {
                $image = asset('assets/img/avatar.jpg');
            }

            $data[] = [
                'id' => $cat->id,
                'title' => $cat->title,
                'image' => $image,
                'updated_at' => date('Y-m-d H:i:s', strtotime($cat->updated_at)),
                'books' => count($cat->books),
            ];
        }
        return response()->json(['success' => true, 'code' => 200, 'data' => $data]);
    }

    public function authors(Request $request)
    {
        $authors = Author::query();

        if ($request->has('last_update_time')) {
            $date = date('Y-m-d H:i:s', strtotime($request->last_update_time));
            $authors = $authors->where('updated_at', '>=', $date);
        }

        $authors = $authors->orderBy('name')->get();
        $author = [];
        foreach ($authors as $author_data) {
            if ($author_data->image != '') {
                $author_image = asset($author_data->image);
            } else {
                $author_image = null;
            }
            $authDeathDateAsInt = is_string($author_data->death_date) ? 9999 : $author_data->death_date;
            $author[] = [
                'id' => $author_data->id,
                'name' => $author_data->name,
                'death_date' => $authDeathDateAsInt,
                'image' => $author_image,
                'description' => $author_data->description,
                'updated_at' => date('Y-m-d H:i:s', strtotime($author_data->updated_at)),
            ];
        }
        return response()->json(['success' => true, 'code' => 200, 'author' => $author]);
    }

    public function all_books(Request $request)
    {
        $api_status = [];
        if ($request->has('version')) {
            if ($request->has('perPage')) {
                $perPage = $request->perPage;
            } else {
                $perPage = 0;
            }
            if ($request->has('last_update_time')) {
                $date = date('Y-m-d H:i:s', strtotime($request->last_update_time));
            } else {
                $date = '2020-01-01 00:00:00';
            }
            if ($perPage > 0) {
                $books = Book::where('updated_at', '>=', $date)->with('author', 'categories', 'files')->withTrashed()->orderByDesc('updated_at')->paginate($perPage);
                $count = Book::where('updated_at', '>=', $date)->with('author', 'categories', 'files')->withTrashed()->orderByDesc('updated_at')->count();
                $pagination = ['books_count' => $count, 'pages' => ceil($count / $perPage)];
            } else {
                $books = Book::where('updated_at', '>=', $date)->with('author', 'categories', 'files')->withTrashed()->orderByDesc('updated_at')->get();
                $pagination = ['books_count' => $books->count(), 'pages' => 1];
            }
            $data = [];
            foreach ($books as $book) {
                $book_allow = true;
                if ($book->application == 1) {
                    $book_allow = true;
                } else {
                    $book_allow = false;
                    $user = Auth::user();
                    if ($user != null) {
                        $pivate_users = $book->private_users ?? '';
                        if ($pivate_users != '') {
                            $pivate_users = explode(',', $pivate_users);
                            if (in_array($user->id, $pivate_users)) {
                                $book_allow = true;
                            }
                        }
                    }
                }
                if ($book->author != null && $book->author->image != '') {
                    $author_image = asset($book->author->image);
                } else {
                    $author_image = asset('assets/img/avatar.jpg');
                }

                if (empty($book->deleted_at) && $book_allow) {
                    if ($book->sqlite != '') {
                        $s = $book->sqlite;
                        //check if $s is link
                        if (filter_var($s, FILTER_VALIDATE_URL)) {
                            $s = $s;
                        } else {
                            $s = asset($s);
                        }
                    } else {
                        $s = '';
                    }

                    $pdf_files = [];

                    // https://ziydia.com/uploads/books_files/0-1656968036.pdf
                    // https://ziydia-s3.fra1.digitaloceanspaces.com/books/769/0-1742306116.pdf
                    foreach ($book->files->where('file_number', 1) as $pddf_file) {
                        $fileUrl = $pddf_file->file_path;
                        $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                        $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                        $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                        $pdf_files[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                    }

                    $pdf_files_2 = [];
                    foreach ($book->files->where('file_number', 2) as $pddf_file) {
                        $fileUrl = $pddf_file->file_path;
                        $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                        $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                        $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                        $pdf_files_2[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                    }

                    $pdf_files_3 = [];
                    foreach ($book->files->where('file_number', 3) as $pddf_file) {
                        $fileUrl = $pddf_file->file_path;
                        $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                        $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                        $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                        $pdf_files_3[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                    }

                    $category = $book->categories->first();
                    if ($category->image != '') {
                        $catttimage = asset(optional($book->categories->first())->image);
                    } else {
                        $catttimage = asset('assets/img/avatar.jpg');
                    }
                    // check if $book->author is string convert to 9999
                    $uathDeathDateAsInt = is_string($book->author->death_date) ? 9999 : $book->author->death_date;
                    $data[] = [
                        'id' => $book->id,
                        'title' => $book->title,
                        'about' => $book->summary,
                        'size' => $book->sqlite_size,
                        'sqlite' => $s,
                        'created_at' => ['date' => $book->created_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                        'last_update_time' => ['date' => $book->updated_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                        'status' => 'active',
                        'image' => $book->image,
                        'pdf_files' => $pdf_files,
                        'pdf_files_2' => $pdf_files_2,
                        'pdf_files_3' => $pdf_files_3,
                        'pdf_size' => $book->size_web,
                        'author' => ['id' => $book->author->id, 'name' => $book->author->name, 'image' => $author_image, 'death_date' => $uathDeathDateAsInt, 'description' => $book->author->description],
                        'category' => ['id' => optional($book->categories->first())->id, 'name' => optional($book->categories->first())->title, 'image' => $catttimage],
                    ];
                } else {
                    $data[] = [
                        'id' => $book->id,
                        'title' => null,
                        'about' => null,
                        'size' => null,
                        'sqlite' => null,
                        'created_at' => ['date' => $book->created_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                        'last_update_time' => ['date' => $book->updated_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                        'status' => 'deleted',
                        'image' => $book->image,
                        'pdf_files' => [],
                        'pdf_files_2' => [],
                        'pdf_files_3' => [],
                        'pdf_size' => null,
                        'author' => ['id' => 0, 'name' => null, 'death_date' => null, 'description' => null, 'image' => null],
                        'category' => ['id' => 0, 'name' => null, 'image' => null],
                    ];
                }
            }
        } else {
            return response()->json(['success' => false, 'errors' => 'unauthorized', 'code' => 401]);
        }

        $response = ['success' => true, 'code' => 200, 'pagination' => $pagination, 'books' => $data, 'about' => ''];
        return response()->json($response);
    }

    public function contact_us(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'message' => 'required',
            ],
            [
                'message.required' => 'من فضلك أدخل الرسالة',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $message = new ContactMessage();
        $message->message = $request->message;
        $message->save();

        return response()->json(['success' => true, 'message' => 'تم الارسال بنجاح', 'code' => 200]);
    }

    public function register(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|min:3',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|confirmed|min:8',
            ],
            [
                'name.required' => 'من فضلك أدخل الاسم',
                'name.min' => 'الاسم لا يجب أن يقل عن 3 أحرف',
                'email.required' => 'من فضلك أدخل البريد الإلكتروني',
                'email.email' => 'من فضلك أدخل بريد إلكتروني صحيح',
                'email.unique' => 'هذا البريد الإلكتروني مسجل من قبل',
                'password.required' => 'من فضلك أدخل كلمة المرور',
                'password.confirmed' => 'الرجاء التأكد من تطابق كلمة المرور',
                'password.min' => 'الرجاء كتابة كلمة مرور تحتوي على الأقل 8 أرقام وعلى الأقل حرف واحد',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $checker = User::where('email', $request->email)->first();
        if ($checker !== null) {
            return response()->json(['success' => false, 'errors' => 'هذا البريد الإلكتروني مسجل من قبل', 'code' => 201], 201);
        }
        $user = new User();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = bcrypt($request->password);
        $user->save();

        $user->assignRole(RolesEnum::USER);
        $token = $user->createToken('authToken')->plainTextToken;
        $user['access_token'] = $token;
        $data = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'access_token' => $user['access_token'],
        ];
        return response()->json(['success' => true, 'message' => 'تم التسجيل بنجاح', 'data' => $data, 'code' => 200]);
    }

    public function login(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|email',
                'password' => 'required',
            ],
            [
                'email.required' => 'من فضلك أدخل البريد الإلكتروني',
                'email.email' => 'من فضلك أدخل بريد إلكتروني صحيح',
                'password.required' => 'من فضلك أدخل كلمة المرور',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $user = User::where('email', $request->email)->first();
        if ($user === null) {
            return response()->json(['success' => false, 'errors' => 'هذا البريد الإلكتروني غير مسجل', 'code' => 201], 201);
        } elseif (!Hash::check($request->password, $user->password)) {
            return response()->json(['success' => false, 'errors' => 'خطأ في البريد الإلكتروني أو كلمة المرور', 'code' => 201], 201);
        }
        $token = $user->createToken('authToken')->plainTextToken;
        $user['access_token'] = $token;
        $data = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'access_token' => $user['access_token'],
        ];
        return response()->json(['success' => true, 'message' => 'تم الدخول بنجاح', 'data' => $data, 'code' => 200]);
    }

    public function forget_password(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|email',
            ],
            [
                'email.required' => 'من فضلك أدخل البريد الإلكتروني',
                'email.email' => 'من فضلك أدخل بريد إلكتروني صحيح',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $user = User::where('email', $request->email)->first();
        if ($user === null) {
            return response()->json(['success' => false, 'errors' => 'هذا البريد الإلكتروني غير مسجل', 'code' => 201], 201);
        } else {
            // $reset = new UserPasswordReset();
            // $reset->user = $user->id;
            // $reset->token = md5(strtotime('now') . '_' . str_random(40) . '_' . strtotime('now'));
            // $reset->save();
            // $data = ['token' => $reset->token];

            $token = Password::createToken($user);
            $to = $user->email;

            $message = "<p style='text-align:right;'>مرحبًا، يُرجى اتّباع الرابط التالي لإعادة تعيين كلمة مرور المكتبة الزيدية لحسابك " . $request->email . '</p>';
            $message .= "<p style='text-align:right;'>https://ziydia.com/Reset-Pass/" . $token . '</p>';
            $message .= "<p style='text-align:right;'>ويمكنك تجاهل هذا البريد الإلكتروني إذا لم تطلب إعادة ضبط كلمة المرور.</p>";
            $message .= "<p style='text-align:right;'>شكراً</p>";
            $message .= "<p style='text-align:right;'>فريق المكتبة الزيدية</p>";
            //*call ForgetPasswordMail notification

            $post = [
                'from' => [
                    'email' => '<EMAIL>',
                    'name' => 'المكتبة الزيدية',
                ],
                'to' => [
                    [
                        'email' => $user->email,
                    ],
                ],
                'subject' => 'إعادة ضبط كلمة المرور للتطبيق المكتبة الزيدية',
                'text' => strip_tags($message),
                'html' => $message,
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://send.api.mailtrap.io/api/send');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json', 'Authorization: Bearer 4eed9ab46cb3b2a9e79f2b7c419c6b65']);
            $result = curl_exec($ch);

            // $user->notify(new ForgetPasswordMail($user, $reset->token));

            // $ch = curl_init();
            // curl_setopt($ch, CURLOPT_URL, 'https://api.mailgun.net/v3/ziydia.com/messages');
            // curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            // curl_setopt($ch, CURLOPT_POST, 1);
            // $post = array(
            //     'from' => 'تطبيق المكتبة الزيدية <<EMAIL>>',
            //     'to' => $user->name.' <'.$user->email.'>',
            //     'subject' => 'إعادة ضبط كلمة المرور للتطبيق المكتبة الزيدية',
            //     'text' => strip_tags($message),
            //     'html' => $message
            // );
            // curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
            // curl_setopt($ch, CURLOPT_USERPWD, 'api' . ':' . '**************************************************');

            // $result = curl_exec($ch);
            // curl_close($ch);
            $baseResponse = [
                'success' => true,
                'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
                'code' => 200,
            ];

            return response()->json(['success' => true, 'data' => $baseResponse, 'code' => 200]);
        }
    }

    public function reset_password(Request $request)
    {
        //make action edit password
        $validator = Validator::make(
            $request->all(),
            [
                'user_id' => 'required',
                'email' => 'required|email',
                'current_password' => 'required|min:8',
                'new_password' => 'required|min:8',
            ],
            [
                'user_id.required' => 'حدث خطأ ما، يرجى المحاولة مرة أخرى',
                'email.required' => 'حدث خطأ ما، يرجى المحاولة مرة أخرى',
                'current_password.required' => 'من فضلك أدخل كلمة المرور الحالية',
                'current_password.min' => 'الرجاء كتابة كلمة مرور تحتوي على الأقل 8 أرقام وعلى الأقل حرف واحد',
                'new_password.required' => 'من فضلك أدخل كلمة المرور الجديدة',
                'new_password.min' => 'الرجاء كتابة كلمة مرور تحتوي على الأقل 8 أرقام وعلى الأقل حرف واحد',
            ],
        );

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }

        $user = User::where('id', $request->user_id)->where('email', $request->email)->first();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json(['success' => false, 'errors' => 'كلمة المرور الحالية غير صحيحة', 'code' => 201], 201);
        }

        $user->password = bcrypt($request->new_password);
        $user->save();

        return response()->json(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح', 'code' => 200]);
    }

    public function social_login(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required',
                'email' => 'required|email',
                'social_id' => 'required',
                'social_type' => 'required',
            ],
            [
                'name.required' => 'من فضلك أدخل الاسم',
                'email.required' => 'من فضلك أدخل البريد الإلكتروني',
                'email.email' => 'من فضلك أدخل بريد إلكتروني صحيح',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $user = User::where('email', $request->email)->where('social_id', $request->social_id)->where('social_type', $request->social_type)->first();
        if ($user === null) {
            if (User::where('email', $request->email)->first() !== null) {
                return response()->json(['success' => false, 'errors' => 'هذا البريد الإلكتروني مسجل من قبل', 'code' => 201], 201);
            }
            $user = new User();
            $user->name = empty($request->name) ? $request->email : $request->name;
            $user->email = $request->email;
            $user->social_id = $request->social_id;
            $user->social_type = $request->social_type;
            $user->password = bcrypt(strtotime('now') . '___' . config('variables.default_password'));
            $user->save();
            $token = $user->createToken('authToken')->plainTextToken;
            $user['access_token'] = $token;
            $data = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'access_token' => $user['access_token'],
            ];
            return response()->json(['success' => true, 'message' => 'تم التسجيل بنجاح', 'data' => $data, 'code' => 200]);
        } else {
            $token = $user->createToken('authToken')->plainTextToken;
            $user['access_token'] = $token;
            return response()->json(['success' => true, 'message' => 'تم الدخول بنجاح', 'data' => $user, 'code' => 200]);
        }
    }

    public function profile()
    {
        $user = User::select('id', 'name', 'email')
            ->where('id', Auth::user()->id)
            ->first();
        return response()->json(['success' => true, 'data' => $user, 'code' => 200]);
    }

    public function update_fcm_token(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'token' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => 'من فضلك أدخل التوكن الخاص بالمستخدم', 'code' => 201], 201);
        }
        $user->fcm_token = $request->token;
        $user->last_activity = now();
        $user->save();

        $users = User::where('id', '!=', Auth::user()->id)
            ->where('fcm_token', $request->token)
            ->update(['fcm_token' => null]);
        return response()->json(['success' => true, 'message' => 'تم تغيير بيانات الحساب بنجاح', 'code' => 200]);
    }

    public function logout(Request $request)
    {
        $user = Auth::user();
        $user->fcm_token = null;
        $user->save();
        return response()->json(['success' => true, 'message' => 'تم الخروج بنجاح', 'code' => 200]);
    }

    public function delete_account(Request $request)
    {
        $user = Auth::user();
        $user->fcm_token = null;
        $user->save();

        $user->delete();

        return response()->json(['success' => true, 'message' => 'تم مسح الحساب بنجاح', 'code' => 200]);
    }
    public function update_profile(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|min:3',
                'email' => 'required|email',
            ],
            [
                'name.required' => 'من فضلك أدخل الاسم',
                'name.min' => 'الاسم لا يجب ان يقل عن 3 احرف',
                'email.required' => 'من فضلك أدخل البريد الالكتروني',
                'email.email' => 'من فضلك أدخل بريد الالكتروني صحيح',
            ],
        );
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()->first(), 'code' => 201], 201);
        }
        $checker = User::where('email', $request->email)
            ->where('id', '!=', Auth::user()->id)
            ->first();
        if ($checker !== null) {
            return response()->json(['success' => false, 'errors' => 'هذا البريد الالكتروني مسجل من قبل', 'code' => 201], 201);
        }

        $user = Auth::user();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->save();
        return response()->json(['success' => true, 'message' => 'تم تغيير بيانات الحساب بنجاح', 'code' => 200]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function sync_data(Request $request)
    {
        $type = $request->type ? $request->type : '';
        $excludeIds = $request->excludeIds ? explode(',', $request->excludeIds) : [];

        $query = Auth::user()->dataSyncs()->whereNotIn('id', $excludeIds);
        if ($type) {
            $query->where('type', $type);
        }
        $list = $query->get();

        $excludedDeletedList = Auth::user()->dataSyncs()->whereIn('id', $excludeIds)->onlyTrashed()->get();

        //*merge lists
        $list = $list->merge($excludedDeletedList);

        return response()->json(['success' => false, 'data' => $list, 'code' => 200], 200);
    }

    public function sync_data_store(Request $request)
    {
        $list = $request->data ? $request->data : [];
        $returnList = [];
        foreach ($list as $item) {
            $result = DataSync::create([
                'type' => $item['type'],
                'data' => $item['data'],
                'user_id' => Auth::user()->id,
                'created_at_app' => $item['created_at'],
            ]);
            array_push($returnList, $result);
        }

        return response()->json(['success' => true, 'data' => $returnList, 'code' => 200], 200);
    }

    public function sync_data_destroy(Request $request)
    {
        $list = $request->data ? $request->data : [];
        foreach ($list as $item) {
            $dataSyncList = Auth::user()->dataSyncs()->where('id', $item);
            if ($dataSyncList->count() > 0) {
                $dataSync = $dataSyncList->first();
                $dataSync->delete();
            }
        }

        return response()->json(['success' => true, 'message' => 'تم حذف البيانات', 'code' => 200], 200);
    }

    public function sync(Request $request)
    {
        $validations = [
            'deleted_ids' => 'array',
            'synced_ids' => 'array',
            'unsynced' => 'array',
            'updated' => 'array',
            'type' => 'required|string',
            'returnNew' => 'integer',
        ];

        $validator = validator::make($request->all(), $validations);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first(), 'code' => 400], 400);
        }
        //check if exist user
        if (!Auth::user()) {
            return response()->json(['success' => false, 'message' => 'المستخدم غير موجود', 'code' => 400], 400);
        }

        $deletedIds = $request->deleted_ids ? $request->deleted_ids : [];
        $syncedIds = $request->synced_ids ? $request->synced_ids : [];
        $unsynced = $request->unsynced ? $request->unsynced : [];
        $updated = $request->updated ? $request->updated : [];
        $type = $request->type ? $request->type : '';
        $returnNew = $request->returnNew ? $request->returnNew : 0;

        //*if type is settings
        if ($type == 'settings' || $type == 'azzo' || $type == 'books') {
            if (count($unsynced) > 0) {
                $list = Auth::user()->dataSyncs()->where('type', $type)->get();
                foreach ($list as $item) {
                    $item->delete();
                }
                //*save unsynced settings
                $settings = $unsynced[0]['data'];
                $result = DataSync::create([
                    'type' => $type,
                    'data' => $settings,
                    'user_id' => Auth::user()->id,
                    'created_at_app' => 0,
                ]);
                return response()->json(
                    [
                        'success' => false,
                        'message' => 'تم حفظ البيانات',
                        'data' => [
                            'deleted' => [],
                            'synced' => [],
                            'new' => [$result],
                        ],
                        'code' => 200,
                    ],
                    200,
                );
            } else {
                //*return settings
                $list = DataSync::where('type', $type)
                    ->where('user_id', Auth::user()->id)
                    ->get();
                return response()->json(
                    [
                        'success' => false,
                        'message' => 'تم جلب البيانات',
                        'data' => [
                            'deleted' => [],
                            'synced' => [],
                            'new' => count($list) > 0 ? [$list->first()] : [],
                        ],
                        'code' => 200,
                    ],
                    200,
                );
            }
        }

        $list = Auth::user()->dataSyncs()->withTrashed()->where('type', $type)->get();

        //* check if all syncedIds are in list and not in deleted
        $deletedSyncedIds = [];
        foreach ($syncedIds as $id) {
            $dataSync = $list->where('id', $id)->first();
            if ($dataSync) {
                if ($dataSync->trashed()) {
                    array_push($deletedSyncedIds, $id);
                }
            } else {
                array_push($deletedSyncedIds, $id);
            }
        }
        //*delete deletedSyncedIds
        foreach ($deletedIds as $item) {
            $dataSync = DataSync::where('id', $item)->first();
            if ($dataSync) {
                $dataSync->delete();
            }
        }
        //add deletedIds to deletedSyncedIds
        if (count($deletedIds) > 0) {
            $deletedSyncedIds = array_merge($deletedSyncedIds, $deletedIds);
        }

        //*save unsynced and return ids
        $newSyncedList = [];
        foreach ($unsynced as $item) {
            //*check if already exist by created_at
            $dataSync = DataSync::where('created_at_app', $item['created_at'])->first();
            if ($dataSync) {
                array_push($newSyncedList, $dataSync);
                continue;
            }
            $result = DataSync::create([
                'type' => $item['type'],
                'data' => $item['data'],
                'user_id' => Auth::user()->id,
                'created_at_app' => $item['created_at'],
            ]);
            array_push($newSyncedList, $result);
        }

        //*update list
        foreach ($updated as $item) {
            $decodedData = json_decode($item['data']);
            $sid = $decodedData->sync_id;
            $appCreatedAt = $decodedData->created_at;
            $isUpdated = false;
            if ($sid) {
                $dataSync = DataSync::where('id', $sid)->first();
                if ($dataSync) {
                    $dataSync->data = $item['data'];
                    $dataSync->created_at_app = $item['created_at'];
                    $dataSync->save();
                    $isUpdated = true;
                    array_push($newSyncedList, $dataSync);
                }
            } elseif ($appCreatedAt) {
                $dataSync = DataSync::where('created_at_app', $appCreatedAt)->first();
                if ($dataSync) {
                    $dataSync->data = $item['data'];
                    $dataSync->created_at_app = $item['created_at'];
                    $dataSync->save();
                    $isUpdated = true;
                    array_push($newSyncedList, $dataSync);
                }
            }
            if (!$isUpdated) {
                $result = DataSync::create([
                    'type' => $item['type'],
                    'data' => $item['data'],
                    'user_id' => Auth::user()->id,
                    'created_at_app' => $item['created_at'],
                ]);
                array_push($newSyncedList, $result);
            }
        }

        //*synced list where not in unsynced and not in deletedSyncedIds
        $list = $list->whereNotIn('id', $deletedSyncedIds)->whereNotIn('id', $syncedIds)->where('deleted_at', null);
        $newList = [];
        if ($returnNew == 1) {
            foreach ($list as $item) {
                array_push($newList, $item);
            }
        }

        return response()->json(
            [
                'success' => false,
                'data' => [
                    'deleted' => $deletedSyncedIds,
                    'synced' => $newSyncedList,
                    'new' => $newList,
                ],
                'code' => 200,
            ],
            200,
        );
    }

    public function notifications()
    {
        $user = auth()->user();

        $notifications = $user->userNotifications()->paginate(10);
        $returnList = [];
        foreach ($notifications as $notification) {
            $returnList[] = [
                'id' => $notification->id,
                'title' => $notification->notification->title,
                'body' => $notification->notification->body,
                'image' => $notification->notification->image,
                'related_id' => $notification->notification->related_id,
                'related_type' => $notification->notification->related_type,
                'is_read' => $notification->read_at ? true : false,
                'created_at' => Carbon::parse($notification->created_at)->diffForHumans(),
            ];
        }
        return response()->json(['success' => true, 'data' => $returnList, 'code' => 200], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function notifications_destroy(Request $request): \Illuminate\Http\JsonResponse
    {
        $userNotification = AppUserNotification::where('user_id', auth()->id())
            ->where('id', $request->id)
            ->first();
        if (!$userNotification) {
            return response()->json(['success' => false, 'message' => 'Not found', 'code' => 404], 404);
        }
        $status = $userNotification->delete();
        return response()->json(['success' => $status, 'message' => 'Deleted', 'code' => 200], 200);
    }

    public function notifications_markAsRead(Request $request)
    {
        $userNotifications = AppUserNotification::where('user_id', auth()->id())
            ->where('read_at', null)
            ->get();
        foreach ($userNotifications as $userNotification) {
            $userNotification->markAsRead();
        }
        return response()->json(['success' => true, 'message' => 'Marked as read', 'code' => 200], 200);
    }

    public function notifications_unreadCount()
    {
        $count = AppUserNotification::where('user_id', auth()->id())
            ->where('read_at', null)
            ->count();
        return response()->json(['success' => true, 'message' => $count, 'code' => 200], 200);
    }

    public function authorized_books(Request $request)
    {
        $api_status = [];
        //*get users from private_users from books table for each book

        // $user_books = BookUser::where('user_id', Auth::guard('api')->user()->id)
        // ->pluck('book_id')
        // ->toArray();

        if ($request->has('perPages')) {
            $perPage = $request->perPage;
        } else {
            $perPage = 0;
        }
        if ($request->has('last_update_time')) {
            $date = date('Y-m-d H:i:s', strtotime($request->last_update_time));
        } else {
            $date = '2020-01-01 00:00:00';
        }
        if ($perPage > 0) {
            $books = Book::where('updated_at', '>=', $date)->where('private_users', '!=', null)->where('application', 0)->with('author', 'categories', 'files')->orderByDesc('updated_at')->paginate($perPage);
            $count = Book::where('updated_at', '>=', $date)->where('private_users', '!=', null)->where('application', 0)->with('author', 'categories', 'files')->orderByDesc('updated_at')->count();
            $pagination = ['books_count' => $count, 'pages' => ceil($count / $perPage)];
        } else {
            $books = Book::where('updated_at', '>=', $date)->where('private_users', '!=', null)->where('application', 0)->with('author', 'categories', 'files')->orderByDesc('updated_at')->get();
            $pagination = ['books_count' => $books->count(), 'pages' => 1];
        }
        $data = [];
        foreach ($books as $book) {
            $book_allow = false;
            // $book_users = BookUser::where('book_id', $book->id)->pluck('user_id')->toArray();
            $book_users = explode(',', $book->private_users);
            if (count($book_users) > 0) {
                $user = Auth::user();
                if ($user != null) {
                    if (in_array($user->id, $book_users)) {
                        $book_allow = true;
                    }
                }
            }

            if ($book->author->image != '') {
                $author_image = asset($book->author->image);
            } else {
                $author_image = asset('assets/img/avatar.jpg');
            }
            if (empty($book->deleted_at) && $book_allow) {
                if ($book->sqlite != '') {
                    $s = $book->sqlite;
                    //check if $s is link
                    if (filter_var($s, FILTER_VALIDATE_URL)) {
                        $s = $s;
                    } else {
                        $s = asset($s);
                    }
                } else {
                    $s = '';
                }

                $pdf_files = [];

                // https://ziydia.com/uploads/books_files/0-1656968036.pdf
                // https://ziydia-s3.fra1.digitaloceanspaces.com/books/769/0-1742306116.pdf
                foreach ($book->files->where('file_number', 1) as $pddf_file) {
                    $fileUrl = $pddf_file->file_path;
                    $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                    $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                    $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                    $pdf_files[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                }

                $pdf_files_2 = [];
                foreach ($book->files->where('file_number', 2) as $pddf_file) {
                    $fileUrl = $pddf_file->file_path;
                    $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                    $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                    $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                    $pdf_files_2[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                }

                $pdf_files_3 = [];
                foreach ($book->files->where('file_number', 3) as $pddf_file) {
                    $fileUrl = $pddf_file->file_path;
                    $fileNameWithoutExt = pathinfo($fileUrl, PATHINFO_FILENAME);
                    $fileNameWithExt = pathinfo($fileUrl, PATHINFO_BASENAME);
                    $fileUrl = 'https://ziydia.com/uploads/books_files/' . $fileNameWithoutExt . '.pdf';
                    $pdf_files_3[] = ['file' => $fileUrl, 'size' => $pddf_file->file_size];
                }

                $category = $book->categories->first();
                if ($category->image != '') {
                    $catttimage = asset(optional($book->categories->first())->image);
                } else {
                    $catttimage = asset('assets/img/avatar.jpg');
                }
                // check if $book->author is string convert to 9999
                $uathDeathDateAsInt = is_string($book->author->death_date) ? 9999 : $book->author->death_date;
                $data[] = [
                    'id' => $book->id,
                    'title' => $book->title,
                    'about' => $book->summary,
                    'size' => $book->sqlite_size,
                    'sqlite' => $s,
                    'created_at' => ['date' => $book->created_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                    'last_update_time' => ['date' => $book->updated_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                    'status' => 'active',
                    'image' => $book->image,
                    'pdf_files' => $pdf_files,
                    'pdf_files_2' => $pdf_files_2,
                    'pdf_files_3' => $pdf_files_3,
                    'pdf_size' => $book->size_web,
                    'author' => ['id' => $book->author->id, 'name' => $book->author->name, 'image' => $author_image, 'death_date' => $uathDeathDateAsInt, 'description' => $book->author->description],
                    'category' => ['id' => optional($book->categories->first())->id, 'name' => optional($book->categories->first())->title, 'image' => $catttimage],
                ];
            } else {
                $data[] = [
                    'id' => $book->id,
                    'title' => null,
                    'about' => null,
                    'size' => null,
                    'sqlite' => null,
                    'created_at' => ['date' => $book->created_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                    'last_update_time' => ['date' => $book->updated_at->format('Y-m-d'), 'timezone_type' => 3, 'timezone' => 'UTC'],
                    'status' => 'deleted',
                    'image' => $book->image,
                    'pdf_files' => [],
                    'pdf_files_2' => [],
                    'pdf_files_3' => [],
                    'pdf_size' => null,
                    'author' => ['id' => 0, 'name' => null, 'death_date' => null, 'description' => null, 'image' => null],
                    'category' => ['id' => 0, 'name' => null, 'image' => null],
                ];
            }
        }

        $response = ['success' => true, 'code' => 200, 'pagination' => $pagination, 'books' => $data];
        return response()->json($response);
    }
}
