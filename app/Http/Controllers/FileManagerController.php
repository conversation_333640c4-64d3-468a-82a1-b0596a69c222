<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Models\Book;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use ZipArchive;
use Illuminate\Pagination\LengthAwarePaginator;

class FileManagerController extends Controller
{
    /**
     * Available storage disks
     *
     * @var array
     */
    protected $availableDisks = ['public', 'local', 'spaces'];

    /**
     * Display a listing of the files.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $path = $request->path ?? '';
    
        // Get the current disk (default to 'public')
        $disk = $request->disk ?? 'public';
    
        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }
    
        // Pagination settings
        $perPage = 24;
        $page = $request->page ?? 1;
        
        // Get sort parameters
        $sortBy = $request->sort_by ?? 'name';
        $sortDirection = $request->sort_direction ?? 'asc';
        
        // Validate sort parameters
        $allowedSortFields = ['name', 'size', 'date'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'name';
        }
        
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'asc';
        }
    
        // Get files
        $files = Storage::disk($disk)->files($path);
        $filesDetails = [];
        
        // Get file details
        foreach ($files as $file) {
            $filesDetails[] = [
                'name' => basename($file),
                'url' => Storage::disk($disk)->url($file),
                'size' => Storage::disk($disk)->size($file),
                'human_size' => Helpers::bytesToHuman(Storage::disk($disk)->size($file)),
                'date' => Storage::disk($disk)->lastModified($file),
                'human_date' => date('Y-m-d H:i:s', Storage::disk($disk)->lastModified($file)),
                'fullPath' => $file,
            ];
        }
        
        // Sort files
        switch($sortBy) {
            case 'name':
                usort($filesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? strcmp(strtolower($a['name']), strtolower($b['name']))
                        : strcmp(strtolower($b['name']), strtolower($a['name']));
                });
                break;
            case 'size':
                usort($filesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? $a['size'] - $b['size']
                        : $b['size'] - $a['size'];
                });
                break;
            case 'date':
                usort($filesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? $a['date'] - $b['date']
                        : $b['date'] - $a['date'];
                });
                break;
        }
        
        // Paginate the files
        $paginatedFiles = [];
        for ($i = 0; $i < count($filesDetails); $i++) {
            if ($i >= ($page - 1) * $perPage && $i < $page * $perPage) {
                $paginatedFiles[] = $filesDetails[$i];
            }
        }
        
        // Build the paginator array
        $filesPaginator = [
            'files' => $paginatedFiles,
            'currentPage' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil(count($files) / $perPage),
            'path' => $path,
            'total' => count($files),
        ];
    
        // Get directories and paginate them
        $directories = Storage::disk($disk)->directories($path);
        $directoriesDetails = [];
        
        // Get directory details
        foreach ($directories as $directory) {
            //books/number
            $book = null;
            if(strpos($directory, 'books/') !== false){
                $bookId = explode('/', $directory)[1];
                $book = Book::find($bookId);
            }
            $directoriesDetails[] = [
                'name' => basename($directory),
                'url' => $directory == null ? '' : Storage::disk($disk)->url($directory),
                'book' => $book,
                'date' => '',
                'count' => count(Storage::disk($disk)->files($directory)) + count(Storage::disk($disk)->directories($directory)),
                'fullPath' => $directory,
            ];
        }
        
        // Sort directories
        switch($sortBy) {
            case 'name':
                usort($directoriesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? strcmp(strtolower($a['name']), strtolower($b['name']))
                        : strcmp(strtolower($b['name']), strtolower($a['name']));
                });
                break;
            case 'size':
                usort($directoriesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? $a['count'] - $b['count']
                        : $b['count'] - $a['count'];
                });
                break;
            case 'date':
                // Since directories don't have a date, we'll sort by name as a fallback
                usort($directoriesDetails, function($a, $b) use ($sortDirection) {
                    return $sortDirection === 'asc' 
                        ? strcmp(strtolower($a['name']), strtolower($b['name']))
                        : strcmp(strtolower($b['name']), strtolower($a['name']));
                });
                break;
        }
        
        // Paginate the directories
        $paginatedDirectories = [];
        for ($i = 0; $i < count($directoriesDetails); $i++) {
            if ($i >= ($page - 1) * $perPage && $i < $page * $perPage) {
                $paginatedDirectories[] = $directoriesDetails[$i];
            }
        }
    
        $directoriesPaginator = [
            'directories' => $paginatedDirectories,
            'currentPage' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil(count($directories) / $perPage),
            'path' => $path,
            'total' => count($directories),
        ];
    
        // Calculate disk usage
        $diskTotal = disk_total_space(Storage::disk($disk)->path('/'));
        $diskFree = disk_free_space(Storage::disk($disk)->path('/'));
        $diskUsed = $diskTotal - $diskFree;
        $diskUsage = [
            'total' => round($diskTotal / (1024 * 1024 * 1024), 2) . ' GB',
            'used' => round($diskUsed / (1024 * 1024 * 1024), 2) . ' GB',
            'free' => round($diskFree / (1024 * 1024 * 1024), 2) . ' GB',
        ];
    
        return view('modules.file-manager.file-manager', [
            'directories' => $directoriesPaginator,
            'files' => $filesPaginator,
            'path' => $path,
            'disk' => $disk,
            'availableDisks' => $this->availableDisks,
            'diskUsage' => $diskUsage,
            'sortBy' => $sortBy,
            'sortDirection' => $sortDirection
        ]);
    }

    /**
     * Show the form for creating a new file.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Return a view for creating a new file
    }

    /**
     * Store a newly created file in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'file' => 'required|file',
            'disk' => 'sometimes',
            'path' => 'sometimes',
        ]);

        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if ($request->has('path') && $request->path != '') {
            $path = $request->path;
        } else {
            $path = '';
        }
        $filename = $request->file('file')->getClientOriginalName();

        if ($path == '') {
            $filePath = Storage::disk($disk)->putFileAs('', $request->file('file'), $filename);
        } else {
            $filePath = Storage::disk($disk)->putFileAs($path, $request->file('file'), $filename);
        }
        return response()->json($filePath, 201);
    }

    public function store2(Request $request)
    {
        $request->validate([
            'file' => 'required|file',
            'path' => 'sometimes',
            'disk' => 'sometimes',
        ]);

        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if ($request->has('path') && $request->path != '') {
            $path = $request->path;
        } else {
            $path = '';
        }
        //year 2 digit month day as 2 digit hour minute second as 2 digit milliseconds
        $currentTime = date('dmyHis');
        $filename = $currentTime . '-' . $request->file('file')->getClientOriginalName();

        if ($path == '') {
            Storage::disk($disk)->putFileAs('', $request->file('file'), $filename);
        } else {
            Storage::disk($disk)->putFileAs($path, $request->file('file'), $filename);
        }

        $filePath = Storage::disk($disk)->url($filename);
        return $filePath;
    }

    /**
     * Display the specified file.
     *
     * @param  string  $filename
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $filename)
    {
        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (Storage::disk($disk)->exists($filename)) {
            return response()->file(Storage::disk($disk)->path($filename));
        }

        return response()->json(['message' => 'File not found'], 404);
    }

    /**
     * Show the form for editing the specified file.
     *
     * @param  string  $filename
     * @return \Illuminate\Http\Response
     */
    public function edit($filename)
    {
        redirect()->route('files.index');
    }

    /**
     * Update the specified file in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $filename
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $filename)
    {
        $request->validate([
            'file' => 'required|file',
            'disk' => 'sometimes',
        ]);

        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (Storage::disk($disk)->exists($filename)) {
            Storage::disk($disk)->delete($filename);
            $path = $request->file('file')->storeAs($disk === 'public' ? 'public' : '', $filename, $disk);
            return response()->json(['path' => $path], 200);
        }

        return response()->json(['message' => 'File not found'], 404);
    }

    /**
     * Remove the specified file or directory from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'filename' => 'required|string',
            'disk' => 'sometimes|string',
            'is_directory' => 'sometimes|boolean'
        ]);

        $filename = $request->filename;
        $disk = $request->disk ?? 'public';
        $isDirectory = $request->is_directory ?? false;

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        try {
            if ($isDirectory) {
                if (Storage::disk($disk)->exists($filename)) {
                    Storage::disk($disk)->deleteDirectory($filename);
                    return back()->with('success', 'تم حذف المجلد بنجاح');
                }
            } else {
                if (Storage::disk($disk)->exists($filename)) {
                    Storage::disk($disk)->delete($filename);
                    return back()->with('success', 'تم حذف الملف بنجاح');
                }
            }
            
            return back()->with('error', 'الملف غير موجود');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الملف: ' . $e->getMessage());
        }
    }

    /**
     * Rename the specified file in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $filename
     * @return \Illuminate\Http\Response
     */
    public function rename(Request $request)
    {
        $request->validate([
            'new_name' => 'required|string',
            'current_file_path' => 'required|string',
            'disk' => 'sometimes',
        ]);
        $filePath = $request->input('current_file_path');
        $filename = basename($filePath);
        $filePathWithoutFileName = str_replace($filename, '', $filePath);

        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (Storage::disk($disk)->exists($filePath)) {
            $newName = $request->input('new_name');
            $newName = $filePathWithoutFileName . $newName;
            Storage::disk($disk)->move($filePath, $newName);
            return Back()->with('success', 'تم تغيير اسم الملف بنجاح');
        }

        return Back()->with('error', 'File not found');
    }

    /**
     * Paste a file to a new location.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $filename
     * @return \Illuminate\Http\Response
     */
    public function paste(Request $request, $filename)
    {
        $request->validate([
            'destination' => 'required|string',
            'disk' => 'sometimes',
        ]);

        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (Storage::disk($disk)->exists($filename)) {
            $destination = $request->input('destination');
            $newPath = Str::finish($destination, '/') . basename($filename);
            Storage::disk($disk)->copy($filename, $newPath);
            return response()->json(['message' => 'File pasted'], 200);
        }

        return response()->json(['message' => 'File not found'], 404);
    }

    /**
     * Download a file
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function download(Request $request)
    {
        $request->validate([
            'url' => 'required|string',
            'disk' => 'sometimes',
        ]);

        $url = $request->url;
        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        //* url
        $baseUrl = url('/');
        $baseUrlWithoutSSL = str_replace('https://', 'http://', $baseUrl);

        $filePathRemoveDomain = str_replace($baseUrl, '', $url);
        $filePathRemoveDomainWithoutSSL = str_replace($baseUrlWithoutSSL, '', $url);

        $filePath = str_replace('storage/', '', $filePathRemoveDomain);
        $filePathWithoutSSL = str_replace('storage/', '', $filePathRemoveDomainWithoutSSL);

        // Try direct path first
        if ($request->has('path') && Storage::disk($disk)->exists($request->path)) {
            return response()->download(Storage::disk($disk)->path($request->path));
        }

        if (Storage::disk($disk)->exists($filePath)) {
            return response()->download(Storage::disk($disk)->path($filePath));
        }
        if (Storage::disk($disk)->exists($filePathWithoutSSL)) {
            return response()->download(Storage::disk($disk)->path($filePathWithoutSSL));
        }

        return back()->with('error', 'File not found');
    }

    /**
     * Download a directory as a zip file
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function downloadDirectory(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
            'disk' => 'sometimes',
        ]);

        $path = $request->path;
        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (!Storage::disk($disk)->exists($path)) {
            return back()->with('error', 'Directory not found');
        }

        // Create zip file
        $zipFileName = basename($path) . '.zip';
        $zipFilePath = storage_path('app/temp/' . $zipFileName);

        // Make sure temp directory exists
        if (!File::isDirectory(storage_path('app/temp'))) {
            File::makeDirectory(storage_path('app/temp'), 0755, true);
        }
        //*check file size
        $files = Storage::disk($disk)->files($path);

        if (count($files) > 40) {
            return back()->with('error', 'عدد الملفات كبير ');
        }
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += Storage::disk($disk)->size($file);
        }
        if ($totalSize > 200*1024*1024) {
            return back()->with('error', 'حجم الملفات كبير ');
        }

        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === true) {
            $this->addFilesToZip($zip, $path, $disk);
            $zip->close();

            return response()->download($zipFilePath)->deleteFileAfterSend(true);
        }

        return back()->with('error', 'Could not create zip file');
    }

    /**
     * Recursively add files to zip
     *
     * @param  ZipArchive  $zip
     * @param  string  $path
     * @param  string  $disk
     * @param  string  $zipPath
     * @return void
     */
    private function addFilesToZip(ZipArchive $zip, $path, $disk, $zipPath = '')
    {
        $files = Storage::disk($disk)->files($path);
        $directories = Storage::disk($disk)->directories($path);

        // Add files
        foreach ($files as $file) {
            $fileName = basename($file);
            $fileContent = Storage::disk($disk)->get($file);

            // Set zip path
            $zipFilePath = $zipPath ? $zipPath . '/' . $fileName : $fileName;
            $zip->addFromString($zipFilePath, $fileContent);
        }

        // Add directories recursively
        foreach ($directories as $directory) {
            $dirName = basename($directory);

            // Set zip path for subdirectory
            $zipDirPath = $zipPath ? $zipPath . '/' . $dirName : $dirName;

            // Add empty directory
            $zip->addEmptyDir($zipDirPath);

            // Add directory contents
            $this->addFilesToZip($zip, $directory, $disk, $zipDirPath);
        }
    }

    /**
     * Get file details for the context menu
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getFileDetails(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
            'disk' => 'sometimes',
        ]);

        $path = $request->path;
        $disk = $request->disk ?? 'public';

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        if (!Storage::disk($disk)->exists($path)) {
            return response()->json(['error' => 'File not found'], 404);
        }

        $fileDetails = [
            'name' => basename($path),
            'url' => Storage::disk($disk)->url($path),
            'size' => Helpers::bytesToHuman(Storage::disk($disk)->size($path)),
            'date' => date('Y-m-d H:i:s', Storage::disk($disk)->lastModified($path)),
            'type' => Storage::disk($disk)->mimeType($path),
        ];

        return response()->json($fileDetails);
    }

    /**
     * Delete multiple files or directories.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'disk' => 'sometimes|string'
        ]);

        $disk = $request->disk ?? 'public';
        $items = $request->items;
        $successes = 0;
        $failures = 0;

        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }

        foreach ($items as $item) {
            try {
                if ($item['is_directory']) {
                    if (Storage::disk($disk)->exists($item['path'])) {
                        Storage::disk($disk)->deleteDirectory($item['path']);
                        $successes++;
                    } else {
                        $failures++;
                    }
                } else {
                    if (Storage::disk($disk)->exists($item['path'])) {
                        Storage::disk($disk)->delete($item['path']);
                        $successes++;
                    } else {
                        $failures++;
                    }
                }
            } catch (\Exception $e) {
                $failures++;
            }
        }

        $message = "تمت العملية بنجاح: تم حذف $successes عناصر";
        if ($failures > 0) {
            $message .= " وفشل حذف $failures عناصر";
            return back()->with('warning', $message);
        }
        
        return back()->with('success', $message);
    }

    /**
     * Search for files and directories by name
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function search(Request $request)
    {
        $request->validate([
            'search' => 'required|string',
            'path' => 'sometimes|string',
            'disk' => 'sometimes|string'
        ]);

        $query = $request->search;
        $path = $request->path ?? '';
        $disk = $request->disk ?? 'public';
        // Validate disk
        if (!in_array($disk, $this->availableDisks)) {
            $disk = 'public';
        }
        
        $perPage = 24;
        $page = $request->page ?? 1;
        
        // Search for files
        $allFiles = Storage::disk($disk)->files($path);
        $matchingFiles = [];
        
        foreach($allFiles as $file) {
            if (stripos(basename($file), $query) !== false) {
                $matchingFiles[] = $file;
            }
        }
        
        $filesDetails = [];
        for ($i = 0; $i < count($matchingFiles); $i++) {
            if ($i >= ($page - 1) * $perPage && $i < $page * $perPage) {
                $file = $matchingFiles[$i];
                $filesDetails[] = [
                    'name' => basename($file),
                    'url' => Storage::disk($disk)->url($file),
                    'size' => Helpers::bytesToHuman(Storage::disk($disk)->size($file)),
                    'date' => date('Y-m-d H:i:s', Storage::disk($disk)->lastModified($file)),
                    'fullPath' => $file,
                ];
            }
        }
        
        // Search for directories
        $allDirectories = Storage::disk($disk)->directories($path);
        $matchingDirectories = [];
        
        foreach ($allDirectories as $directory) {
            if (stripos(basename($directory), $query) !== false) {
                $matchingDirectories[] = $directory;
            }
        }
        
        $directoriesDetails = [];
        for ($i = 0; $i < count($matchingDirectories); $i++) {
            if ($i >= ($page - 1) * $perPage && $i < $page * $perPage) {
                $directory = $matchingDirectories[$i];
                //books/number
                $book = null;
                if(strpos($directory, 'books/') !== false){
                    $bookId = explode('/', $directory)[1];
                    $book = Book::find($bookId);
                }
                $directoriesDetails[] = [
                    'name' => basename($directory),
                    'url' => $directory == null ? '' : Storage::disk($disk)->url($directory),
                    'book' => $book,
                    'date' => '',
                    'count' => count(Storage::disk($disk)->files($directory)) + count(Storage::disk($disk)->directories($directory)),
                    'fullPath' => $directory,
                ];
            }
        }
        
        $filesPaginator = [
            'files' => $filesDetails,
            'currentPage' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil(count($matchingFiles) / $perPage),
            'path' => $path,
            'total' => count($matchingFiles),
        ];
        
        $directoriesPaginator = [
            'directories' => $directoriesDetails,
            'currentPage' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil(count($matchingDirectories) / $perPage),
            'path' => $path,
            'total' => count($matchingDirectories),
        ];
        
        // Calculate disk usage
        $diskTotal = disk_total_space(Storage::disk($disk)->path('/'));
        $diskFree = disk_free_space(Storage::disk($disk)->path('/'));
        $diskUsed = $diskTotal - $diskFree;
        $diskUsage = [
            'total' => round($diskTotal / (1024 * 1024 * 1024), 2) . ' GB',
            'used' => round($diskUsed / (1024 * 1024 * 1024), 2) . ' GB',
            'free' => round($diskFree / (1024 * 1024 * 1024), 2) . ' GB',
        ];
        
        return view('modules.file-manager.file-manager', [
            'directories' => $directoriesPaginator,
            'files' => $filesPaginator,
            'path' => $path,
            'disk' => $disk,
            'availableDisks' => $this->availableDisks,
            'diskUsage' => $diskUsage,
            'searchQuery' => $query,
            'isSearchResult' => true
        ]);
    }
}
