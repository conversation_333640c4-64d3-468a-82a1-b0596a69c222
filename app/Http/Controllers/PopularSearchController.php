<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StorePopularSearchRequest;
use App\Http\Requests\UpdatePopularSearchRequest;
use App\Models\PopularSearch;
use App\Models\SearchHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PopularSearchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Prepare analytics data for the dashboard
        $analyticsData = [
            [
                'title' => 'إجمالي عمليات البحث الشائعة',
                'value' => PopularSearch::count(),
                'percent' => 0,
                'color' => 'primary',
                'icon' => 'search',
                'description' => 'إجمالي عدد عمليات البحث الشائعة'
            ],
            [
                'title' => 'عمليات البحث المميزة',
                'value' => PopularSearch::where('is_featured', true)->count(),
                'percent' => 0,
                'color' => 'success',
                'icon' => 'star',
                'description' => 'عدد عمليات البحث المميزة'
            ],
            [
                'title' => 'عمليات البحث النشطة',
                'value' => PopularSearch::where('is_active', true)->count(),
                'percent' => 0,
                'color' => 'info',
                'icon' => 'check',
                'description' => 'عدد عمليات البحث النشطة'
            ],
            [
                'title' => 'إجمالي النقرات',
                'value' => PopularSearch::sum('click_count'),
                'percent' => 0,
                'color' => 'warning',
                'icon' => 'click',
                'description' => 'إجمالي عدد النقرات على عمليات البحث'
            ]
        ];

        return view('modules.popular-search.index', compact('analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('popular-searches.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePopularSearchRequest $request)
    {
        $data = $request->validated();
        
        // Create the popular search
        PopularSearch::create($data);
        
        // Clear the popular searches cache
        Cache::forget('popular_searches');
        
        return back()->with('success', 'تم إنشاء البحث الشائع بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(PopularSearch $popularSearch)
    {
        return redirect()->route('popular-searches.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PopularSearch $popularSearch)
    {
        return redirect()->route('popular-searches.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePopularSearchRequest $request, PopularSearch $popularSearch)
    {
        $data = $request->validated();
        
        // Update the popular search
        $popularSearch->update($data);
        
        // Clear the popular searches cache
        Cache::forget('popular_searches');
        
        return back()->with('success', 'تم تحديث البحث الشائع بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PopularSearch $popularSearch)
    {
        $popularSearch->delete();
        
        // Clear the popular searches cache
        Cache::forget('popular_searches');
        
        Helpers::notifyAdmins([
            'title' => 'تم حذف بحث شائع',
            'message' => 'تم حذف البحث الشائع "' . $popularSearch->search_term . '" بواسطة ' . auth()->user()->name,
            'href' => route('popular-searches.index'),
        ]);
        
        return back()->with('success', 'تم حذف البحث الشائع بنجاح');
    }
}
