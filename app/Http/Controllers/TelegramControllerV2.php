<?php

namespace App\Http\Controllers;

use App\Models\Fatawy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Storage;

class TelegramControllerV2 extends Controller
{
    public function handleUpdate(Request $request)
    {



        $update = Telegram::bot('mybot2')->getWebhookUpdates();
        $message = $update->getMessage();
        try{
            if (!$message || !$message->getText()) {
                return;
            }
        }catch (\Exception $e){
            Log::error('Error in TelegramControllerV2: ' . $e->getMessage());
            return;
        }

        $chatId = $message->getChat()->getId();
        $username = $message->getChat()->getUsername();
        $text = $message->getText();

        $blockedUsers = json_decode(Storage::disk('public')->get('blockedUsers2.json'), true) ?? [];

        // Block the bot from responding to messages from the blocked users
        if (in_array($chatId, $blockedUsers)) {
            return;
        }

        if ($text === '/zZ@s23Asd##') {
            $this->resetAdmins($chatId);
            return;
        }
        
        if ($text === '/start') {
            $this->sendWelcomeMessage($chatId);
            return;
        }

        if ($message->getReplyToMessage()) {
            $this->handleReplyToMessage($message, $chatId, $text);
            return;
        }

        if ($text === '/list') {
            $this->handleAdminCommand($chatId, $username);
            return;
        }

        if ($update->getCallbackQuery()) {
            $this->handleCallbackQuery($update);
            return;
        }

      
        $this->handleNewQuestionSubmission($chatId, $username, $text);
    }

    private function resetAdmins($chatId)
    {
        $admins = [
            'main_admin' => null,
            'admins' => []
        ];
        Storage::disk('public')->put('admins2.json', json_encode($admins, JSON_PRETTY_PRINT));
        try {
            Telegram::bot('mybot2')->sendMessage([
                'chat_id' => $chatId,
                'text' => 'Admins reset successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage());
        }
    }

    private function sendWelcomeMessage($chatId)
    {
        try {
            Telegram::bot('mybot2')->sendMessage([
                'chat_id' => $chatId,
                'text' => "🤝 أهـلًا وسهلًا ومرحبًا بڪم 🤝
في بوت الفتاوى للسيـــد العـلامــة المجتهد: مُحَــــمَّد بن عبدﷲ عـوض المؤيدي
حفظـــه ﷲ وأبقاه ونفعـنا بعلمـــه

✍🏻 نستقبل أسئلتڪم وسنوافيڪم بالإجابة
 بعد الحصول عليها إن شـاء ﷲ.",
            ]);
        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage());
        }
    }

    private function isAdmin($chatId)
    {
        $admins = json_decode(Storage::disk('public')->get('admins2.json'), true);
        $isAdminExist = !is_null($admins['main_admin'] ?? null);
        return $isAdminExist && in_array($chatId, $admins['admins']);
    }

    private function handleReplyToMessage($message, $chatId, $text)
    {
        if (!$this->isAdmin($chatId)) return;

        $originalMessage = $message->getReplyToMessage()->getText();
        $questionId = $this->extractQuestionIdFromMessage($originalMessage);
        $fatawy = Fatawy::find($questionId);
        if(!$fatawy){
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => 'السؤال غير موجود.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
            return;
        }
        $question = $fatawy->question;

        if ($fatawy) {
            $fatawy->update([
                'answered' => true,
                'answer' => $text,
            ]);

            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $fatawy->user_id,
                    'text' => "✳️ سؤال: \n$question\n\n↕️ الجواب: \n$text",
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
    

            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => 'تم إرسال الرد للمستخدم.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        } else {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => 'السؤال غير موجود.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }
    }

    private function handleAdminCommand($chatId, $username)
    {
        $admins = json_decode(Storage::disk('public')->get('admins2.json'), true);
        $isAdminExist = !is_null($admins['main_admin'] ?? null);

        if (!$isAdminExist) {
            $admins['main_admin'] = $chatId;
            $admins['admins'][] = $chatId;
            Storage::disk('public')->put('admins2.json', json_encode($admins, JSON_PRETTY_PRINT));

            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => '✅ تم تسجيلك كمدير رئيسي للوصول إلى قائمة الأسئلة.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
            return;
        }

        if (!$this->isAdmin($chatId)) {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $admins['main_admin'],
                    'text' => "محاولة وصول غير مصرح بها إلى قائمة الأسئلة:\n\n" . "User ID: $chatId\nUsername: $username",
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }

            // Telegram::bot('mybot2')->sendMessage([
            //     'chat_id' => $chatId,
            //     'text' => '❌ ليس لديك صلاحية للوصول إلى هذه القائمة.',
            // ]);
            return;
        }

        $this->sendUnansweredQuestions($chatId);
    }

    private function sendUnansweredQuestions($chatId)
    {
        $fatawies = Fatawy::where('answered', false)->limit(50)->orderBy('created_at', 'asc')->get();
        $count = Fatawy::where('answered', false)->count();

        foreach ($fatawies as $fatawy) {
            $username = $fatawy->username ?: '';
            $questionId = $fatawy->id;
            $question = $fatawy->question;

            $text = $username ? 'المستخدم: ' . $username . "\n" : '';
            $text .= 'السؤال: ' . $question . "\nID: " . $questionId;

            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => $text,
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => 'حذف', 'callback_data' => 'delete_' . $fatawy->id],
                                ['text' => 'حظر', 'callback_data' => 'block_' . $fatawy->user_id],
                            ],
                        ],
                    ]),
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }

        if ($count > count($fatawies)) {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => 'عرض ' . count($fatawies) . ' من ' . $count . ' سؤال غير مجاب',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }

        if ($count == 0) {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => 'لا يوجد أسئلة غير مجابة',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }
    }

    private function handleCallbackQuery($update)
    {
        $callbackQuery = $update->getCallbackQuery();
        $callbackData = $callbackQuery->getData();
        $callbackChatId = $callbackQuery->getMessage()->getChat()->getId();
        $callbackMessageId = $callbackQuery->getMessage()->getMessageId();

        if (str_starts_with($callbackData, 'reply_')) {
            $this->handleReplyCallback($callbackData, $callbackChatId, $callbackMessageId);
        } elseif (str_starts_with($callbackData, 'delete_')) {
            $this->handleDeleteCallback($callbackData, $callbackChatId, $callbackMessageId);
        } elseif (str_starts_with($callbackData, 'block_')) {
            $this->handleBlockCallback($callbackData, $callbackChatId, $callbackMessageId);
        }
    }

    private function handleReplyCallback($callbackData, $callbackChatId, $callbackMessageId)
    {
        $questionId = str_replace('reply_', '', $callbackData);
        $fatawy = Fatawy::find($questionId);

        if ($fatawy) {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $callbackChatId,
                    'text' => $fatawy->question,
                    'reply_to_message_id' => $callbackMessageId,
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        } else {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $callbackChatId,
                    'text' => 'السؤال غير موجود.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }
    }

    private function handleDeleteCallback($callbackData, $callbackChatId, $callbackMessageId)
    {
        $questionId = str_replace('delete_', '', $callbackData);
        $fatawy = Fatawy::find($questionId);

        if ($fatawy) {
            $fatawy->delete();
            try {
                Telegram::bot('mybot2')->editMessageText([
                    'chat_id' => $callbackChatId,
                    'message_id' => $callbackMessageId,
                    'text' => 'تم حذف السؤال.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        } else {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $callbackChatId,
                    'text' => 'السؤال غير موجود.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }
    }

    private function handleBlockCallback($callbackData, $callbackChatId, $callbackMessageId)
    {
        $userIdToBlock = str_replace('block_', '', $callbackData);
        $blockedUsers = json_decode(Storage::disk('public')->get('blockedUsers2.json'), true) ?? [];

        if (!in_array($userIdToBlock, $blockedUsers)) {
            $blockedUsers[] = $userIdToBlock;
            Storage::disk('public')->put('blockedUsers2.json', json_encode($blockedUsers, JSON_PRETTY_PRINT));

            try {
                Telegram::bot('mybot2')->editMessageText([
                    'chat_id' => $callbackChatId,
                    'message_id' => $callbackMessageId,
                    'text' => 'تم حظر المستخدم.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        } else {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $callbackChatId,
                    'text' => 'المستخدم محظور بالفعل.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
        }
    }

    private function handleNewQuestionSubmission($chatId, $username, $text)
    {
        $unansweredQuestions = Fatawy::where('user_id', $chatId)->where('answered', false)->count();
        if ($unansweredQuestions >= 10) {
            try {
                Telegram::bot('mybot2')->sendMessage([
                    'chat_id' => $chatId,
                    'text' => '❌ لا يمكنك إرسال المزيد من الأسئلة، انتظر حتى يتم الرد على الأسئلة السابقة.',
                ]);
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage());
            }
            return;
        }

        Fatawy::create([
            'user_id' => $chatId,
            'username' => $username,
            'question' => $text,
        ]);

        try {
            Telegram::bot('mybot2')->sendMessage([
                'chat_id' => $chatId,
                'text' => '✍🏻 تم استلام سؤالك سنوافيڪم بالإجابة، بعد الحصول عليها إن شـاء ﷲ.',
            ]);
        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage());
        }
    }

    private function extractQuestionIdFromMessage($messageText)
    {
        preg_match('/ID: (\d+)/', $messageText, $matches);
        return $matches[1] ?? null;
    }
}
