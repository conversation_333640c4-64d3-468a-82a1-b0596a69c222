<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class BookSearchController extends Controller
{
    public static function getPageContent($page, $part, $bookId)
    {
        $db = DB::connection('sqlite_search');
        $query = "SELECT * FROM book_searches WHERE page=$page AND  book_id=$bookId";
        try {
            $rows = $db->select($query);
            return $rows;
        } catch (Exception $e) {
            return $e;
        }
    }
}
