<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BookReport;
use App\Models\BookReportDetail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookReportController extends Controller
{
    /**
     * Display a detailed report for the specified book.
     */
    public function show(Book $book)
    {
        // Get basic book information
        $bookInfo = [
            'id' => $book->id,
            'title' => $book->title,
            'author' => $book->author->name ?? 'غير معروف',
            'image' => $book->image,
            'summary' => $book->summary,
            'created_at' => $book->created_at ? $book->created_at->format('Y-m-d') : 'غير معروف',
            'updated_at' => $book->updated_at ? $book->updated_at->format('Y-m-d') : 'غير معروف',
            'website' => $book->website == 1,
            'application' => $book->application == 1,
            'private_users' => $book->private_users ? true : false,
            'has_content' => $book->hasContent(),
            'has_pdfs' => $book->pdfs->count() > 0,
            'has_sqlite' => $book->sqlite ? true : false,
        ];

        // Get categories
        $categories = $book->categories->map(function ($category) {
            return [
                'id' => $category->id,
                'title' => $category->title,
            ];
        });

        // Get download statistics
        $downloadStats = [
            'sqlite_downloads' => $book->sqliteDownloadsCount() ?? 0,
            'pdf_downloads' => $book->pdfDownloadsCount() ?? 0,
            'total_downloads' => ($book->sqliteDownloadsCount() ?? 0) + ($book->pdfDownloadsCount() ?? 0),
        ];

        // Get reading statistics
        $readStats = $this->getReadingStatistics($book);

        // Get monthly reading data for charts
        $monthlyReadData = $this->getMonthlyReadData($book);

        // Get daily reading data for the current month
        $dailyReadData = $this->getDailyReadDataForCurrentMonth($book);

        // Get PDF files
        $pdfFiles = $book->pdfs->map(function ($pdf) {
            return [
                'id' => $pdf->id,
                'title' => $pdf->title ?? 'بدون عنوان',
                'file_path' => $pdf->file_path,
                'file_size' => $pdf->file_size,
                'downloads' => $pdf->downloads ?? 0,
                'created_at' => $pdf->created_at ? $pdf->created_at->format('Y-m-d') : 'غير معروف',
            ];
        });

        // Get book content statistics
        $contentStats = [
            'total_pages' => $book->totalPages() ?? 0,
            'total_parts' => $book->contents()->distinct('part')->count('part') ?? 0,
        ];

        return view('modules.book.report', compact(
            'book',
            'bookInfo',
            'categories',
            'downloadStats',
            'readStats',
            'monthlyReadData',
            'dailyReadData',
            'pdfFiles',
            'contentStats'
        ));
    }

    /**
     * Get reading statistics for the book
     */
    private function getReadingStatistics($book)
    {
        // Total reads
        $totalReads = BookReport::where('book_id', $book->id)
            ->where('type', 'read')
            ->sum('count');

        // Reads in the last 30 days
        $readsLast30Days = BookReportDetail::where('book_id', $book->id)
            ->where('type', 'read')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->count();

        // Reads in the last 7 days
        $readsLast7Days = BookReportDetail::where('book_id', $book->id)
            ->where('type', 'read')
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        // Reads today
        $readsToday = BookReportDetail::where('book_id', $book->id)
            ->where('type', 'read')
            ->whereDate('created_at', Carbon::today())
            ->count();

        

        return [
            'total_reads' => $totalReads,
            'reads_last_30_days' => $readsLast30Days,
            'reads_last_7_days' => $readsLast7Days,
            'reads_today' => $readsToday,
        ];
    }

    /**
     * Get monthly reading data for charts
     */
    private function getMonthlyReadData($book)
    {
        // Get data for the last 12 months
        $monthlyData = BookReportDetail::where('book_id', $book->id)
            ->where('type', 'read')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        $labels = [];
        $data = [];

        // Format data for chart
        foreach ($monthlyData as $item) {
            $monthName = Carbon::createFromDate($item->year, $item->month, 1)->format('M Y');
            $labels[] = $monthName;
            $data[] = $item->count;
        }

        return [
            'labels' => $labels,
            'data' => $data,
        ];
    }

    /**
     * Get daily reading data for the current month
     */
    private function getDailyReadDataForCurrentMonth($book)
    {
        // Get the start and end of the current month
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        // Get data for each day of the current month
        $dailyData = BookReportDetail::where('book_id', $book->id)
            ->where('type', 'read')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Create an array with all days of the month
        $daysInMonth = $endOfMonth->day;
        $labels = [];
        $data = [];

        // Initialize with zeros for all days
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = Carbon::createFromDate($startOfMonth->year, $startOfMonth->month, $day);
            $labels[] = $date->format('d M'); // Format: 01 Jan
            $data[] = 0;
        }

        // Fill in actual data
        foreach ($dailyData as $item) {
            $date = Carbon::parse($item->date);
            $dayIndex = $date->day - 1; // Array is 0-indexed
            $data[$dayIndex] = $item->count;
        }

        return [
            'labels' => $labels,
            'data' => $data,
        ];
    }
}
