<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\View;
use Alkoumi\LaravelHijriDate\Hijri;
use App\Http\Controllers\SocialMediaStatsController;
use App\Models\Category;
use App\Services\DashboardService;
use Modules\Blog\Models\Blog;

class FrontMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        //         // $ip = $_SERVER['REMOTE_ADDR'];

        //         // $ip_found = Visitor::where('ip_address',$ip)->get();
        //         // if(count($ip_found) == 0){
        //         //     $ips = new Visitor;
        //         //     $ips->ip_address = $ip;
        //         //     $ips->save();
        //         // }

        //         $categories = Category::get();
        //         View::share('categories', $categories);

        //         $setting = [
        //             'logo_one' => 'assets/img/logo.png',
        //             'logo_two' => 'assets/img/logo2.png',
        //             'footer_title' => 'المكتبة الزيدية',
        //             'footer_text' => '<p dir="rtl" style="text-align:center"><span style="font-size:20px">تطبيق المكتبة الزيدية هو تطبيق يحتوي على ما توفر من الكتب الزيدية المطبوعة&nbsp;وبعض الكتب الإسلامية الأخرى التي يحتاجها الطالب والباحث&nbsp;ويتميز بسهولة الاستخدام وخدمات مفيدة ويمكن تحميله من مختلف المتاجر والأنظمة</span></p>

        // <p dir="rtl" style="text-align:center"><strong><span style="color:#039be5"><span style="font-size:20px">(متاح للتحميل على جميع المنصات)</span></span></strong></p>',
        //             'library_url' => '',
        //             'ios_download' => 0,
        //             'android_download' => 40445,
        //         ];

        //         View::share('settings', $setting);

        //         $dashboardService = new DashboardService();
        //         View::share('mostReadedBooksThisMonth', $dashboardService->getMostReadedBooksThisMonthHome());

        //         View::share('mostDownloadedBooksThisMonth', $dashboardService->getMostDownloadedBookThisMonthHome());

        //         $latestBlogs = Blog::orderBy('created_at', 'desc')->limit(5)->get();
        //         View::share('latestBlogs', $latestBlogs);

        //         // if ($request->segment(1) != 'dashbaord_admin') {
        //         //     if (!Auth::guard('admin')->check()) {
        //         //         if ($setting->under_maintaince == 1) {
        //         //             $seo = Seo::where('page', '=', 'under_maintaince')->where('num', '=', 0)->first();
        //         //             if ($seo === null) {
        //         //                 $seo = new Seo();
        //         //                 $seo->page = 'under_maintaince';
        //         //                 $seo->name = 'تحت الصيانة';
        //         //                 $seo->title = 'المكتبة الزيدية';
        //         //                 $seo->ar_title = 'المكتبة الزيدية';
        //         //                 $seo->num = 0;
        //         //                 $seo->save();
        //         //             }
        //         //             return response()->view('under_maintaince', compact('seo'));
        //         //         }
        //         //     }
        //         // }

        //         $hijri_date = Hijri::Date('l، j/ F/ Y');
        //         View::share('hijri_date', $hijri_date);

        //         $active_page = null;
        //         View::share('active_page', $active_page);

        //         $socials = [];
        //         $socials[] = ['name' => 'telegram', 'link' => 'https://t.me/ziydia', 'icon' => 'fab fa-telegram-plane', 'followers' => 3612];
        //         $socials[] = ['name' => 'youtube', 'link' => 'https://www.youtube.com/@المكتبةالزيدية', 'icon' => 'fab fa-youtube', 'followers' => 169];
        //         View::share('socials', $socials);

        //         $book_page = false;
        //         View::share('book_page', $book_page);

        //         $stores_links = [
        //             'google_play' => 'https://play.google.com/store/apps/details?id=com.ziydia.app',
        //             'app_store' => 'https://apps.apple.com/sa/app/%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%D8%A9-%D8%A7%D9%84%D8%B2%D9%8A%D8%AF%D9%8A%D8%A9/id1557676924?l=ar',
        //             'app_gallery' => 'https://appgallery.huawei.com/#/app/C104084455',
        //         ];
        //         View::share('stores_links', $stores_links);


        $hijri_date = Hijri::Date('l، j/ F/ Y');
        View::share('hijri_date', $hijri_date);

        
        $socialMediaStatsController = new SocialMediaStatsController();

        $telegramStats = $socialMediaStatsController->getTelegramStats();
        $youtubeStats = $socialMediaStatsController->getYouTubeStats();

        $data['telegram'] = $telegramStats['subscribers'];
        $data['youtube'] = $youtubeStats['subscribers'];

        View::share('social', $data);

        return $next($request);
    }
}
