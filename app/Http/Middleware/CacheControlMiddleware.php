<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CacheControlMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Skip caching for authenticated requests and POST/PUT/DELETE requests
        if ($request->user() || in_array($request->method(), ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
            return $response;
        }
        
        // Set different cache times based on content type
        if ($response->headers->get('Content-Type')) {
            $contentType = $response->headers->get('Content-Type');
            
            // Cache static assets longer
            if (strpos($contentType, 'image/') === 0 || 
                strpos($contentType, 'font/') === 0 || 
                strpos($contentType, 'application/font') === 0) {
                // Cache images and fonts for 1 week
                $response->headers->set('Cache-Control', 'public, max-age=604800');
            } else if (strpos($contentType, 'text/css') === 0 || 
                      strpos($contentType, 'application/javascript') === 0 ||
                      strpos($contentType, 'text/javascript') === 0) {
                // Cache CSS and JS for 1 day
                $response->headers->set('Cache-Control', 'public, max-age=86400');
            } else if (strpos($contentType, 'text/html') === 0) {
                // Cache HTML responses for 5 minutes
                $response->headers->set('Cache-Control', 'public, max-age=300');
            }
        }
        
        // Add other performance headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        
        return $response;
    }
}