<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ImageOptimizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Only process HTML responses
        if (!$this->isHtml($response)) {
            return $response;
        }
        
        $content = $response->getContent();
        
        // Add lazy loading to images
        $content = preg_replace_callback('/<img\s(?![^>]*\sloading=)[^>]*>/', function($matches) {
            // Skip if already has loading attribute or is a tiny image (e.g., icon)
            if (strpos($matches[0], 'loading=') !== false || strpos($matches[0], 'width="1"') !== false) {
                return $matches[0];
            }
            
            // Add loading="lazy" attribute to image tags
            return str_replace('<img ', '<img loading="lazy" ', $matches[0]);
        }, $content);
        
        // Add responsive image attributes if not already present
        $content = preg_replace_callback('/<img(?![^>]*\ssrcset=)[^>]*src=["\']([^"\']*)["\'][^>]*>/', function($matches) {
            // Skip SVG and tiny images
            if (strpos($matches[0], '.svg') !== false || strpos($matches[0], 'width="1"') !== false) {
                return $matches[0];
            }
            
            // Add sizes attribute if not already present
            if (strpos($matches[0], 'sizes=') === false) {
                $withSizes = str_replace('<img ', '<img sizes="(max-width: 768px) 100vw, 50vw" ', $matches[0]);
                return $withSizes;
            }
            
            return $matches[0];
        }, $content);
        
        $response->setContent($content);
        return $response;
    }
    
    /**
     * Determine if the response is HTML.
     *
     * @param  \Symfony\Component\HttpFoundation\Response  $response
     * @return bool
     */
    protected function isHtml(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type');
        return $contentType && strpos($contentType, 'text/html') !== false;
    }
}