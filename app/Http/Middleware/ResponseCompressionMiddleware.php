<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ResponseCompressionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Check if $response is a string, and if so, create a Response object
        if (is_string($response)) {
            $response = new Response($response);
        }
        
        // Don't compress already compressed responses or binary files
        if (in_array($response->headers->get('Content-Encoding'), ['gzip', 'deflate', 'br'])) {
            return $response;
        }
        
        // Don't compress certain content types
        $contentType = $response->headers->get('Content-Type');
        if ($this->shouldNotCompress($contentType)) {
            return $response;
        }
        
        // Check if client accepts gzip encoding
        if (strpos($request->header('Accept-Encoding'), 'gzip') !== false) {
            // Get the response content
            $content = $response->getContent();
            
            // Only compress if content size is above threshold (1kb)
            if (strlen($content) > 1024) {
                $compressed = gzencode($content, 9);
                
                // If compression worked and saved space, use the compressed version
                if ($compressed !== false && strlen($compressed) < strlen($content)) {
                    $response->setContent($compressed);
                    $response->headers->set('Content-Encoding', 'gzip');
                    $response->headers->set('Content-Length', strlen($compressed));
                    $response->headers->set('Vary', 'Accept-Encoding');
                }
            }
        }
        
        return $response;
    }
    
    /**
     * Determine if content should not be compressed.
     *
     * @param string|null $contentType
     * @return bool
     */
    protected function shouldNotCompress(?string $contentType): bool
    {
        if ($contentType === null) {
            return true;
        }
        
        // Don't compress binary data, already compressed data, or images
        $nonCompressibleTypes = [
            'image/', 
            'video/',
            'audio/',
            'application/zip',
            'application/x-zip',
            'application/x-zip-compressed',
            'application/x-gzip',
            'application/x-bzip2',
            'application/x-rar-compressed',
            'application/pdf',
            'application/octet-stream',
        ];
        
        foreach ($nonCompressibleTypes as $type) {
            if (strpos($contentType, $type) === 0) {
                return true;
            }
        }
        
        return false;
    }
}