<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

/**
 * Middleware to convert JWT Bearer tokens to Sanctum tokens for authentication
 * Allows backward compatibility with old authentication system
 */
class ConvertJwtToSanctum
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
       

        // Check for Bearer token
        $bearerToken = $request->bearerToken();
        if (!$bearerToken) {
            return json_encode(['success' => false, 'code' => '401', 'message' => 'UnAuthorized']);
        }

        // Check if this is a JWT token (not a Sanctum token)
        if ($this->looksLikeJwt($bearerToken)) {
            try {
                // Decode JWT and get the token ID
                $tokenId = $this::decodeToken($bearerToken);

                if (!$tokenId) {
                    // Invalid JWT token
                    return json_encode(['success' => false, 'code' => '401', 'message' => 'UnAuthorized']);
                }

                $hashedToken = hash('sha256', $tokenId);

                // Find the user by the token ID in Sanctum tokens
                $sanctumToken = PersonalAccessToken::where('token', hash('sha256', $tokenId))->first();


                // If no token found, check if we have a token with this ID as name
                if (!$sanctumToken) {
                    $sanctumToken = PersonalAccessToken::where('name', 'manual-token')->where('token', $tokenId)->first();
                }


                if($sanctumToken == null || empty($sanctumToken) || $sanctumToken == ''){
                    return json_encode(['success' => false, 'code' => '401', 'message' => 'UnAuthorized']);
                }

                if ($sanctumToken) {

                    // Authenticate the user
                    $user = $sanctumToken->tokenable;

                    if ($user instanceof User) {
                        // Set as the authenticated user for this request
                        $request->setUserResolver(function () use ($user) {
                            return $user;
                        });
                    }
                }

                if ($user) {
                    Auth::login($user);
                } else {
                    return json_encode(['success' => false, 'code' => '401', 'message' => 'UnAuthorized']);
                }
            } catch (Exception $e) {
                return json_encode(['success' => false, 'code' => '401', 'message' => 'UnAuthorized']);
            }
        }else{
            //*try login using sanctum
            $user = PersonalAccessToken::findToken($bearerToken);
            if ($user) {
                // Authenticate the user
                $user = $user->tokenable;
                if ($user instanceof User) {
                    // Set as the authenticated user for this request
                    $request->setUserResolver(function () use ($user) {
                        return $user;
                    });
                }
            }
            if ($user) {
                Auth::login($user);
            } else {
                return response()->json(['success'=>false, 'code'=>'401', 'message'=>'UnAuthorized']);

            }
        }

        return $next($request);
    }

    /**
     * Determines if the given token looks like a JWT token
     *
     * @param  string  $token
     * @return bool
     */
    private function looksLikeJwt(string $token): bool
    {
        // JWT tokens typically have 3 parts separated by dots
        $segments = explode('.', $token);
        return count($segments) === 3;
    }

    public static function decodeToken(string $jwtToken)
    {
        $privateKey = "-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3K7RPUUiiO27hNjBW4y1
+FplEVQjerB7tzI774/lgXEQQcEVxfUualpkHcZb+Ntu7W3nrujpb8Ls6LK+IWae
4SHxr6UyoqkrTY0Ee5ilM78dbRTiVBeToU+PNnPtN6ko+JzlbRsq8CEpBY3pxlQt
zbgjc8CHHImkxSigunDGLONpCCRltjWD68l9rMNMxY5oz+0OFX4V8IvguqhyE2L3
CIPFQCzy1amAQZ+HXMmQy1Dg1yIoctWWxivsTDl/pYK/WyniQJJRRcUU1yUhkDsf
keENwkSwL2QntSn9KUS3BX7igzgs3Wy35/c/qrms6Bfy6G3D1X9ed+TrdgU4pjVK
Cn5DRn+jaxULAWw96dupn7TuwdwiDtUzASamr8Y8EE3qXJL5chqdwhcbvI/CRQiQ
E4lfJmQlpgZbXhGB9ruL0wBm10XN441caBcLUjISr07nt17FB3wDBInPvm5VCiYu
yIp8qFbeho/0RxI9OoKyCFtxfGA3XKx4XpNCSHAHMgmliKKmwfYrTgBZGXEBDbVq
GY4TgTKnb10VPZpZQgfrh2wLxgriYQ+ax14bvMPIYn9nPKML7uVmQht5goqHBO2O
6V0ppokHXL//a7HKaAhGMy0pCXtriatbDkVUHsmYteFBxzr5YRgnLfOEaDBfJqME
MfUfJR2D+fRhN6OyavryGUsCAwEAAQ==
-----END PUBLIC KEY-----";
        try {
            $decoded = JWT::decode($jwtToken, new Key($privateKey, 'RS256'));
            return $decoded->jti;
        } catch (\Exception $e) {
            return null;
        }
    }
}
