<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasPermissionTo('book_create');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'title' => 'required|string',
            'image_path' => 'required|string',
            'author_id' => 'required|exists:authors,id',
            'summary' => 'required|string',
            'file_path' => 'sometimes',
            'website' => 'sometimes',
            'application' => 'sometimes',
            'categories' => 'required|array',
            'private_users' => 'sometimes',
            'private_users_switch' => 'sometimes',
            'enable_update_date' => 'sometimes',
            'display_on_home' => 'sometimes',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'العنوان مطلوب',
            'image_path.required' => 'الصورة مطلوبة',
            'author_id.required' => 'المؤلف مطلوب',
            'author_id.exists' => 'المؤلف غير موجود',
            'summary.required' => 'الملخص مطلوب',
            'file_path.required' => 'الملف مطلوب',
            'application.required' => 'عرض على التطبيق مطلوب',
            'website.required' =>  'عرض على الموقع مطلوب',
            'categories.required' => 'التصنيفات مطلوبة',
        ];
    }
}
