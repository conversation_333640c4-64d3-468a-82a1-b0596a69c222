<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAppNotificationRequest extends FormRequest
{
    public function authorize()
    {
        return  auth()->user()->hasPermissionTo('app_notification_create');
    }

    public function rules()
    {
        return [
            'title'   => 'required|string|max:255',
            'body' => 'required|string',
            'data' => 'nullable|json',
            'sent_at' => 'nullable|date',
            'platform' => 'sometimes|in:all,android,ios',
            'type'    => 'sometimes|in:info,warning,error',
            'image' => 'sometimes|nullable|file|image|max:10000',
        ];
    }
}
