<?php

namespace App\Models;

use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Request;

class Book extends Model
{
    use HasFactory;
    use WithHashId;
    use SoftDeletes;

    public $timestamps = false;

    //* TODO remove id from fillable
    protected $fillable = ['id', 'title', 'image', 'author_id', 'added_by', 'summary', 'sqlite', 'sqlite_size', 'download_count', 'application', 'website', 'display_on_home', 'created_at', 'updated_at', 'deleted_at', 'private_users'];

    //* On create save hashID
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {});
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });

        // Apply the website filter only for website requests, not for dashboard
        static::addGlobalScope('website_filter', function (Builder $builder) {
            $path = Request::path();
            $isApi = str_contains($path, 'api');
            if (!$isApi) {
                $user = auth()->user();
                if ($user != null && $user->isSuperAdmin()) {
                    return;
                } else {
                    $builder->where('website', 1)->orWhere('application', 1);
                }
            }
        });
    }

    public function canView(): bool
    {
        $user = auth()->user();
        if ($user != null && $user->isSuperAdmin()) {
            return true;
        }
        return $this->website == 1 || $this->application == 1;
    }
    public function canViewPdf(): bool
    {
        $user = auth()->user();
        if ($user != null && $user->isSuperAdmin()) {
            return true;
        }
        return $this->website == 1;
    }

    public function getCreatedAtAttribute($value)
    {
        if ($value == null || $value == '') {
            return null;
        }
        if (is_string($value)) {
            return \Carbon\Carbon::parse($value);
        }
        return $value;
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == null || $value == '') {
            return null;
        }
        if (is_string($value)) {
            return \Carbon\Carbon::parse($value);
        }
        return $value;
    }

    public function getImageAttribute($value)
    {
        if ($value == null || $value == '') {
            return asset('assets/img/logo.png');
        }
        // check if image is url
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }
        return asset('storage/' . $value);
    }
    public function author()
    {
        return $this->belongsTo(Author::class);
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'book_category');
    }
    public function category()
    {
        $categories = $this->belongsToMany(Category::class, 'book_category');
        return $categories->first();
    }

    public function titles()
    {
        return $this->hasMany(BookTitle::class);
    }

    public function contents()
    {
        return $this->hasMany(BookContent::class, 'book_id');
    }

    public function hasContent()
    {
        return $this->contents()->exists();
    }

    public function files()
    {
        return $this->hasMany(BookFiles::class);
    }
    public function pdfs()
    {
        return $this->hasMany(BookFiles::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('title', 'like', '%' . $search . '%')->orWhere('summary', 'like', '%' . $search . '%');
    }

    public function getPageContent($page, $part = null)
    {
        // Assuming each page content is stored in a related model called BookContent
        return $this->contents()->where('page', $page)->where('part', $part)->first();
    }

    public function totalPages()
    {
        return $this->contents()->distinct('page_id')->count('page_id');
    }
    public function lastPageId($part = null)
    {
        if ($part == null) {
            return $this->contents()->max('page_id');
        } else {
            return $this->contents()->where('part', $part)->max('page_id');
        }
    }
    public function pageByPageId($pageId)
    {
        $content = $this->contents()->where('page_id', $pageId)->first();
        if($content == null){
            return null;
        }
        return $content->page;
    }
    public function pageIdByPage($page, $part = null)
    {
        if ($part == null) {
            $content = $this->contents()->where('page', $page)->first();
            if($content == null){
                return null;
            }
            return $content->page_id;
        }
        $content = $this->contents()->where('page', $page)->where('part', $part)->first();
        if($content == null){
            return null;
        }
        return $content->page_id;
    }

    public function firstPage($part = null)
    {
        if ($part == null) {
            return $this->contents()->min('page');
        } else {
            return $this->contents()->where('part', $part)->min('page');
        }
    }
    public function firstPageId($part = null)
    {
        if ($part == null) {
            return $this->contents()->min('page_id');
        } else {
            return $this->contents()->where('part', $part)->min('page_id');
        }
    }

    public function pPage($pageId)
    {
        return $this->contents()->where('page_id', $pageId)->first()->pPage;
    }
    public function previousPageId($pageId)
    {
        return $this->contents()->where('page_id', '<', $pageId)->max('page_id');
    }
    public function nextPageId($pageId)
    {
        return $this->contents()->where('page_id', '>', $pageId)->min('page_id');
    }
    public function firstPart()
    {
        $minPart = $this->contents()->min('part');
        $maxPart = $this->contents()->max('part');
        if ($minPart == $maxPart) {
            return null;
        }
        return $minPart;
    }
    public function lastPart()
    {
        return $this->contents()->max('part');
    }

    public function searchContent($term)
    {
        return $this->contents()
            ->where('content', 'LIKE', '%' . $term . '%')
            ->get();
    }

    public function nextPageContent($page, $part): ?BookContent
    {
        if ($this->contents()->where('page', '>', $page)->exists()) {
            return $this->contents()->where('page', '>', $page)->first();
        } else {
            return null;
        }
    }

    public function previousPageContent($page, $part = null): ?BookContent
    {
        if ($part == null) {
            if ($this->contents()->where('page', '<', $page)->exists()) {
                //*loop through each page -1 until page 1 if found break and return
                for ($i = $page - 1; $i > 0; $i--) {
                    $bookContent = $this->contents()->where('page', $i)->first();
                    if ($bookContent != null) {
                        return $bookContent;
                    }
                }
            } else {
                return null;
            }
        } else {
            if ($this->contents()->where('page', '<', $page)->where('part', $part)->exists()) {
                //*loop through each page -1 until page 1 if found break and return
                for ($i = $page - 1; $i > 0; $i--) {
                    $bookContent = $this->contents()->where('page', $i)->where('part', $part)->first();
                    if ($bookContent != null) {
                        return $bookContent;
                    }
                }
            } else {
                return null;
            }
        }
        return null;
    }

    public function nextPartContent($page, $part): ?BookContent
    {
        if ($this->contents()->where('part', '>', $part)->exists()) {
            //*loop through each part +1 until part max if found break and return
            for ($i = $part + 1; $i <= $this->contents()->max('part'); $i++) {
                $bookContent = $this->contents()->where('part', $i)->first();
                if ($bookContent != null) {
                    return $bookContent;
                }
            }
        } else {
            return null;
        }
        return null;
    }

    public function previousPartContent($page, $part): ?BookContent
    {
        if ($this->contents()->where('part', '<', $part)->exists()) {
            //*loop through each part -1 until part 1 if found break and return
            for ($i = $part - 1; $i > 0; $i--) {
                $bookContent = $this->contents()->where('part', $i)->first();
                if ($bookContent != null) {
                    return $bookContent;
                }
            }
        } else {
            return null;
        }
        return null;
    }

    public function bookReports()
    {
        return $this->hasMany(BookReport::class);
    }
    public function pdfDownloadsCount()
    {
        //! old database sum(download_book_counters.pdfs) + books.download as download_count
        return ($this->bookReports()->where('type', 'pdf')->first()->count ?? 0) + $this->download_count;
    }

    public function sqliteDownloadsCount()
    {
        return $this->bookReports()->where('type', 'sqlite')->first()->count ?? 0;
    }

    public function webViewCount(): int
    {
        // public function ip_fount ()
        // {
        //     return $this->hasMany('App\IpAddress', 'book');
        // }
        return $this->bookReports()->where('type', 'web_view')->first()->count ?? 0;
    }

    /**
     * Get the number of content records for this book
     *
     * @return int
     */
    public function contentRecordsCount(): int
    {
        return BookContent::countRecordsForBook($this->id);
    }

    /**
     * Get the number of search records for this book
     *
     * @return int
     */
    public function searchRecordsCount(): int
    {
        return BookSearch::countRecordsForBook($this->id);
    }

    /**
     * Get the total number of records (content + search) for this book
     *
     * @return int
     */
    public function totalRecordsCount(): int
    {
        return $this->contentRecordsCount() + $this->searchRecordsCount();
    }
}
