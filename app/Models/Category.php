<?php

namespace App\Models;

use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use <PERSON><PERSON>\Sluggable\SlugOptions;

class Category extends Model
{
    //* TODO remove id from fillable
    protected $fillable = ['id','title', 'description', 'image', 'icon', 'order', 'slug', 'parent_id', 'created_by', 'updated_by', 'deleted_by'];

    protected $hidden = ['created_by', 'updated_by', 'deleted_by'];

    use HasFactory;
    use WithHashId;
    use HasSlug;
    use SoftDeletes;

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()->generateSlugsFrom('title')->saveSlugsTo('slug')->doNotGenerateSlugsOnUpdate();
    }
    //* On create save hashID
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {});
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function books()
    {
        return $this->belongsToMany(Book::class, 'book_category');
    }
    public function availableBooks()
    {
        return  $this->belongsToMany(Book::class, 'book_category');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function deletedBy()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }
    public function scopeSearch($query, $search)
    {
        return $query->where('title', 'like', '%' . $search . '%');
    }
}
