<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class SearchHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'search_term',
        'search_type',
        'ip_address',
    ];

    protected $casts = [
        'search_term' => 'string',
    ];

    protected static function boot()
    {
        parent::boot();
        
        // Truncate search term to 255 characters
        static::creating(function ($model) {
            $model->search_term = substr($model->search_term, 0, 255);
        });
    }

    /**
     * Get the user that owns the search history.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Record a new search in the history
     */
    public static function recordSearch($searchTerm, $searchType = 'all', $ipAddress = null)
    {
        $userId = Auth::id();
        
        // Create the search history record
        $searchHistory = self::create([
            'user_id' => $userId,
            'search_term' => $searchTerm,
            'search_type' => $searchType,
            'ip_address' => $ipAddress,
        ]);

        // Update popular searches
        PopularSearch::updateSearchCount($searchTerm);
        
        // Clear user's search history cache
        if ($userId) {
            Cache::forget("user_{$userId}_recent_searches");
        }
        
        return $searchHistory;
    }

    /**
     * Get recent searches for a user
     */
    public static function getRecentSearches($limit = 5)
    {
        $userId = Auth::id();
        
        // If user is logged in, get their searches
        if ($userId) {
            // Try to get from cache first
            return Cache::remember("user_{$userId}_recent_searches", 60 * 24, function () use ($userId, $limit) {
                return self::where('user_id', $userId)
                    ->orderBy('created_at', 'desc')
                    ->take($limit)
                    ->pluck('search_term')
                    ->unique()
                    ->values()
                    ->toArray();
            });
        }
        
        // For guests, return empty array
        return [];
    }

    /**
     * Get the latest search term for a user
     */
    public static function getLatestSearchTerm()
    {
        $userId = Auth::id();
        
        if ($userId) {
            $latestSearch = self::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->first();
                
            return $latestSearch ? $latestSearch->search_term : '';
        }
        
        return '';
    }
}
