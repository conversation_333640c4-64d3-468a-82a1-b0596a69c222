<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserInfo extends Model
{
    use HasFactory;

    protected $table= 'user_infos';
    protected $fillable = [
        'device',
        'device_details',
        'user_id',
        'brand',
        'is_active',
    ];

    public function user()
    {
        return $this->belongsToOne(User::class);
    }
  
}
