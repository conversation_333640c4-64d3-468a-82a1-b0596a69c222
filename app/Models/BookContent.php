<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BookContent extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Count the number of content records for a specific book
     *
     * @param int $bookId
     * @return int
     */
    public static function countRecordsForBook($bookId)
    {
        return self::where('book_id', $bookId)->count();
    }
}
