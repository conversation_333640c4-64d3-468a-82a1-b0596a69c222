<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PopularSearch extends Model
{
    use HasFactory;

    protected $fillable = [
        'search_term',
        'click_count',
        'is_featured',
        'is_active',
    ];

    /**
     * Update the click count for a search term
     */
    public static function updateSearchCount($searchTerm)
    {
        // Normalize the search term
        $searchTerm = trim($searchTerm);
        
        if (empty($searchTerm)) {
            return null;
        }
        
        // Find or create the popular search record
        $popularSearch = self::firstOrCreate(
            ['search_term' => $searchTerm],
            ['click_count' => 0, 'is_active' => true]
        );
        
        // Increment the click count
        $popularSearch->increment('click_count');
        
        // Clear the popular searches cache
        Cache::forget('popular_searches');
        
        return $popularSearch;
    }

    /**
     * Get the most popular searches
     */
    public static function getPopularSearches($limit = 5)
    {
        // return Cache::remember('popular_searches', 60 * 24, function () use ($limit) {
        //     return self::where('is_active', true)
        //         ->orderBy('is_featured', 'desc')
        //         ->orderBy('click_count', 'desc')
        //         ->take($limit)
        //         ->get(['search_term', 'click_count']);
        // });
        
        return PopularSearch::where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('click_count', 'desc')
            ->take($limit)
            ->get(['search_term']);
    }

    /**
     * Scope a query to only include active searches.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured searches.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
