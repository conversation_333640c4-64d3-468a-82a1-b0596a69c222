<?php

namespace App\Models;

use App\Scopes\ExcludeSpecialAuthorsScope;
use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Request;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Builder;
class Author extends Model implements HasMedia
{
    use HasFactory;
    use WithHashId;
    use InteractsWithMedia;
    use SoftDeletes;

    //* TODO remove id from fillable
    protected $fillable = ['id', 'name', 'birth_date', 'death_date', 'description', 'image', 'nickname'];

    //* On create save hashID
    protected static function boot()
    {
        parent::boot();

        // Apply global scope to exclude authors with name "-"
        static::addGlobalScope(new ExcludeSpecialAuthorsScope());

        static::creating(function ($model) {});
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });

        //*Scope
        // Apply the website filter only for website requests, not for dashboard
        static::addGlobalScope('website_filter', function (Builder $builder) {
            $path = Request::path();
            $isApi = str_contains($path, 'api');
            if (!$isApi) {
                $user = auth()->user();
                if ($user != null && $user->isSuperAdmin()) {
                    return;
                } else {
                    $builder->whereHas('books', function ($query) {
                        $query->where('website', 1)->orWhere('application', 1);
                    });
                }
            }
        });
    }

    // death_date manipulation
    public function getDeathDateAttribute($value)
    {
        $isDieDateNumber = is_numeric($value);
        if ($isDieDateNumber && $value > 9998) {
            return 'معاصر';
        }
        return $value;
    }

    // death_date manipulation
    public function formattedDeathDate()
    {
        $isDieDateNumber = is_numeric($this->death_date);
        if ($isDieDateNumber && $this->death_date > 9998) {
            return 'معاصر';
        }
        return $this->death_date . ' هـ';
    }

    // death_date manipulation
    public function formattedDeathDate2()
    {
        $isDieDateNumber = is_numeric($this->death_date);
        if ($isDieDateNumber && $this->death_date > 9998) {
            return '(معاصر)';
        }
        if ($this->death_date == 'معاصر') {
            return '(معاصر)';
        }
        return '(المتوفى: ' . $this->death_date . ' هـ)';
    }

    public function scopeSearch($query, $search)
    {
        return $query
            ->where('name', 'like', '%' . $search . '%')
            ->orWhere('death_date', 'like', '%' . $search . '%')
            ->orWhere('nickname', 'like', '%' . $search . '%');
    }

    public function books()
    {
        return $this->hasMany(Book::class);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaCollection('authors')->useDisk('authors')->singleFile();
        $this->addMediaConversion('preview')->fit(Fit::Contain, 300, 300)->nonQueued();
    }
}
