<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SearchFtsModel extends Model
{
    public $search;
    public $searchInBooksType;
    public $limit;
    public $sortBy;
    public $sortAsc;
    public $enableSearchInFooter;
    public $idsToExclude;
    public $bookIdsToInclude;
    public $bookIdsToExclude;
    public $offset;

    public function __construct($search, $searchInBooksType, $limit, $sortBy, $enableSearchInFooter, $sortAsc = true, $offset = 0)
    {
        $this->search = $search;
        $this->searchInBooksType = $searchInBooksType;
        $this->limit = $limit;
        $this->sortBy = $sortBy;
        $this->sortAsc = $sortAsc;
        $this->enableSearchInFooter = $enableSearchInFooter;
        $this->idsToExclude = [];
        $this->bookIdsToInclude = [];
        $this->bookIdsToExclude = [];
        $this->offset = $offset;
    }
}
