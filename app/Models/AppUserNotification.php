<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppUserNotification extends Model
{
    use SoftDeletes;
    protected $fillable = [
        'app_notification_id',
        'user_id',
        'read_at',
    ];

    public function notification()
    {
        return $this->belongsTo(AppNotification::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
