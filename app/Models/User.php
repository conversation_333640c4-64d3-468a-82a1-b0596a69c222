<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\RolesEnum;
use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use WithHashId;
    use HasFactory, Notifiable, HasApiTokens;
    use HasRoles;
    use SoftDeletes;

    //* TODO remove id from fillable
    protected $guarded = [];

    protected $hidden = ['password', 'remember_token'];

    public function isSuperAdmin()
    {
        return $this->hasRole(RolesEnum::SUPER_ADMIN);
    }

    public function userInfo()
    {
        return $this->hasOne(UserInfo::class);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    //* override insert function to hash password
    public function insert(array $attributes = [])
    {
        parent::insert($attributes);
    }

    //* On create save hashID
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {});
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')->orWhere('email', 'like', '%' . $search . '%');
    }

    public function dataSyncs()
    {
        return $this->hasMany(DataSync::class);
    }

    public function userNotifications()
    {
        return $this->belongsToMany(AppNotification::class, 'app_user_notifications');
    }
}
