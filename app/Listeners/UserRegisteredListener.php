<?php

namespace App\Listeners;

use App\Events\UserRegisteredEvent;
use App\Models\User;
use App\Notifications\UserRegisteredNotification;
use App\RolesEnum;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Role;

class UserRegisteredListener
{
    public function __construct()
    {
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisteredEvent $event): void
    {
        // Send notification
        $admins = [];
        Role::where('name', RolesEnum::SUPER_ADMIN)
            ->first()
            ->users->each(function ($user) use (&$admins) {
                $admins[] = $user;
            });
        Notification::send($admins, new UserRegisteredNotification($event->user));
    }
}
