<?php

namespace App\Providers;

use App\Helpers\QueryCacheHelper;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\ServiceProvider;

class CacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Add a cache macro to the query builder
        Builder::macro('cache', function (string $key = null, int $ttl = 3600, array $tags = []) {
            $query = $this;
            $key = $key ?: QueryCacheHelper::generateQueryCacheKey($query);
            
            return QueryCacheHelper::remember($query, $key, $ttl, $tags);
        });

        // Add a cached pagination macro
        Builder::macro('cachedPaginate', function (int $perPage = 15, string $keyPrefix = null, int $ttl = 3600, array $tags = []) {
            $query = $this;
            $page = request()->input('page', 1);
            $keyPrefix = $keyPrefix ?: QueryCacheHelper::generateQueryCacheKey($query, 'paginate');
            
            return QueryCacheHelper::rememberPaginated($query, $keyPrefix, $page, $perPage, $ttl, $tags);
        });
    }
}