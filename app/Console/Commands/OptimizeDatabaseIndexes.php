<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OptimizeDatabaseIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:optimize-indexes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database tables and analyze indexes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Only supported for MySQL databases
        if (config('database.default') !== 'mysql') {
            $this->error('This command only supports MySQL databases.');
            return;
        }

        $tables = $this->getTables();

        foreach ($tables as $table) {
            $this->info("Optimizing table: {$table}");
            
            // Optimize table
            DB::statement("OPTIMIZE TABLE `{$table}`");
            
            // Analyze table to update index statistics
            DB::statement("ANALYZE TABLE `{$table}`");
        }

        $this->info('Database optimization completed successfully.');
    }

    /**
     * Get all tables in the database.
     *
     * @return array
     */
    protected function getTables(): array
    {
        $tables = [];
        $results = DB::select('SHOW TABLES');
        
        foreach ($results as $result) {
            $tables[] = reset($result);
        }
        
        return $tables;
    }
}