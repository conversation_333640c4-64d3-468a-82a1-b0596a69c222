<?php

namespace App\Policies;

use App\Models\Faqs;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class FaqsPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Faqs $faqs): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Faqs $faqs): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Faqs $faqs): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Faqs $faqs): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Faqs $faqs): bool
    {
        return $user->hasRole('admin')  || $user->hasRole('super_admin');
    }
}
