<?php

namespace App\Jobs;

use App\Helpers\TextHelper;
use App\Models\Book;
use App\Models\BookSearch;
use App\Models\GlobalNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class ProcessBookSearch implements ShouldQueue
{
    public $timeout = 600; // Set timeout to 10 minutes
    use Queueable;
    protected Book $book;
    protected string $downloadUrl;

    public function __construct(Book $book, string $downloadUrl)
    {
        $this->book = $book;
        $this->downloadUrl = $downloadUrl;
    }

    public function handle()
    {
        $uuid = $this->job->uuid();
        $book = $this->book;

        if (empty($this->downloadUrl)) {
            $this->failProcess($uuid, 'فشلت عملية التحميل', 'Download URL is empty.');
        }

        // Download the zip file
        $localZipPath = $this->downloadBookZip($book, $this->downloadUrl, $uuid);

        // Extract the zip file
        $this->unzipFile($localZipPath, $book, $uuid);

        // Rename files if needed
        $fileName = pathinfo($localZipPath, PATHINFO_FILENAME);
        $this->renameSqliteFileIfNeeded($book, $fileName);
        $this->renameDbFileIfNeeded($book, $fileName);

        // Check if database files exist
        $dbPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
        if (!file_exists($dbPath)) {
            $this->failProcess($uuid, 'فشلت عملية تحويل الكتاب', 'Database file does not exist.');
        }

        // Process the database files
        $db = new \SQLite3($dbPath);
        $this->addContentToBookSearch($db, $book, $uuid, $book->id);
        $db->close();

        // Clean up local files
        $this->cleanupLocalFiles($book, $localZipPath);

        GlobalNotification::updateNotification('تمت العملية بنجاح', 'تمت عملية معالجة الكتاب ' . $book->title, 'success', 100, $uuid);
    }

    private function downloadBookZip($book, $downloadUrl, $uuid)
    {
        GlobalNotification::updateNotification('جاري تحميل الكتاب', 'يتم الان تحميل ملف الكتاب من الرابط المحدد', 'info', 10, $uuid);

        try {
            Log::info('Starting download from URL: ' . $downloadUrl);

            // Create a temporary filename for the download
            $fileName = $book->id . '_' . time() . '.zip';
            $localPath = Storage::disk('public')->path($fileName);

            // Download the file using Laravel Http facade
            $response = Http::timeout(300)->get($downloadUrl);

            if (!$response->successful()) {
                $this->failProcess($uuid, 'فشلت عملية التحميل', 'Failed to download file from URL: ' . $downloadUrl);
            }

            // Save the downloaded content to local storage
            file_put_contents($localPath, $response->body());

            Log::info('Downloaded file saved to: ' . $localPath);

            // Verify the file is a valid zip
            if (!file_exists($localPath) || pathinfo($localPath, PATHINFO_EXTENSION) !== 'zip') {
                $this->failProcess($uuid, 'فشلت عملية التحميل', 'Downloaded file is not a valid zip file.');
            }

            GlobalNotification::updateNotification('تم تحميل الكتاب', 'تم تحميل ملف الكتاب بنجاح', 'info', 20, $uuid);

            return $localPath;

        } catch (\Exception $e) {
            Log::error('Error downloading file: ' . $e->getMessage());
            $this->failProcess($uuid, 'فشلت عملية التحميل', 'Error downloading file: ' . $e->getMessage());
        }
    }

    private function unzipFile($fileInStorage, $book, $uuid)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان فك الضغط عن الكتاب', 'info', 30, $uuid);
        sleep(1);

        Log::info('fileInStorage: ' . $fileInStorage);
        if (!file_exists($fileInStorage) || pathinfo($fileInStorage, PATHINFO_EXTENSION) !== 'zip') {
            $this->failProcess($uuid, 'فشلت عملية فك الضغط', 'File does not exist or is not a zip file.');
        }

        //*create directory if not exists
        if (!Storage::disk('public')->exists($book->id)) {
            Storage::disk('public')->makeDirectory($book->id);
        }

        $zip = new ZipArchive();
        if ($zip->open($fileInStorage) === true) {
            $zip->extractTo(Storage::disk('public')->path($book->id));
            $zip->close();
        } else {
            $this->failProcess($uuid, 'فشلت عملية فك الضغط', 'Failed to unzip file.');
        }

        //* check if there is sqlite file in the folder and rename it to $book->id.sqlite
        $files = Storage::disk('public')->files($book->id);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'sqlite') {
                $oldPath = Storage::disk('public')->path($book->id) . '/' . pathinfo($file, PATHINFO_BASENAME);
                $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
                rename($oldPath, $newPath);
            }
        }
        //* check if there is db file in the folder and rename it to $book->id.db
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'db') {
                $oldPath = Storage::disk('public')->path($book->id) . '/' . pathinfo($file, PATHINFO_BASENAME);
                $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.db';
                rename($oldPath, $newPath);
            }
        }
    }

    private function renameSqliteFileIfNeeded($book, $fileName)
    {
        $checkIfExit = Storage::disk('public')->exists($book->id . '/' . $book->id . '.sqlite');
        $checkIfExitOrignal = Storage::disk('public')->exists($book->id . '/' . $fileName . '.sqlite');
        if (!$checkIfExit && $checkIfExitOrignal) {
            $oldPath = Storage::disk('public')->path($book->id) . '/' . $fileName . '.sqlite';
            $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
            rename($oldPath, $newPath);
        }
    }

    private function renameDbFileIfNeeded($book, $fileName)
    {
        $checkIfExit = Storage::disk('public')->exists($book->id . '/' . $book->id . '.db');
        $checkIfExitOrignal = Storage::disk('public')->exists($book->id . '/' . $fileName . '.db');
        if (!$checkIfExit && $checkIfExitOrignal) {
            $oldPath = Storage::disk('public')->path($book->id) . '/' . $fileName . '.db';
            $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.db';
            rename($oldPath, $newPath);
        }
    }

    private function addContentToBookSearch($db, $book, $uuid, $fileName)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'جاري حذف محتوي الكتاب القديم من قاعدة بيانات [FTS]', 'info', 50, $uuid);
        Log::info('addContentToBookSearch delete old content from FTS: ' . $book->id);
        try {
            BookSearch::where('book_id', $book->id)->chunkById(100, function ($records) {
                $ids = $records->pluck('id');
                BookSearch::whereIn('id', $ids)->delete();
                usleep(100);
            });
        } catch (\Exception $e) {
            Log::error('Error deleting old content from FTS: ' . $e->getMessage());
            $this->failProcess($uuid, 'فشلت عملية حذف محتوي الكتاب القديم', $e->getMessage());
        }
        Log::info('addContentToBookSearch end delete old content from FTS: ' . $book->id);

        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان اضافة محتوي الكتاب الى قاعدة بيانات [FTS]', 'info', 70, $uuid);
        sleep(1);
        $folderPath = Storage::disk('public')->path($book->id);
        $sqliteFilePath = $folderPath . '/' . $fileName . '.sqlite';

        try {
            Log::info('addContentToBookSearch create new sqlite file: ' . $sqliteFilePath);
            // Create the FTS database and zip file
            TextHelper::createNewSqliteFile($sqliteFilePath);
            Log::info('addContentToBookSearch create new sqlite file end: ' . $sqliteFilePath);
            // Create a new SQLite file with cleaned text
            TextHelper::createDbForFtsUseAndZipFile($folderPath, $sqliteFilePath, $book->hashId);
        } catch (\Exception $e) {
            Log::error('Error creating new SQLite file: ' . $e->getMessage());
            $this->failProcess($uuid, 'فشلت عملية تحويل الكتاب', $e->getMessage());
        }

        $dbPathSearch = Storage::disk('public')->path($book->id) . '/' . $fileName . '.db';
        $dbSearch = new \SQLite3($dbPathSearch);
        $resultSearch = $dbSearch->query('SELECT * FROM book');

        $batchSize = 100;
        $batch = [];
        $i = 0;
        while ($row = $resultSearch->fetchArray(SQLITE3_ASSOC)) {
            $i++;
            $batch[] = [
                'book_id' => $book->id,
                'book_title' => $book->title,
                'page' => $row['page'],
                'page_id' => $row['id'],
                'page_title' => $row['tit'] ?? null,
                'part' => $row['part'],
                'search' => $row['nass'] ?? '',
                'search_root' => $row['nass_root'] ?? null,
                'search_footer' => $row['nass_footer'] ?? ($row['nass_footnote'] ?? null),
                'search_footer_root' => $row['nass_footer_root'] ?? ($row['nass_footnote_root'] ?? null),
                'hno' => $row['hno'] ?? ($row['Hno'] ?? null),
                'mno' => $row['mno'] ?? ($row['Mno'] ?? null),
            ];

            if (count($batch) >= $batchSize) {
                GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان اضافة محتوي الكتاب الى قاعدة بيانات [FTS] ' . $i, 'info', 80, $uuid);
                BookSearch::insert($batch);
                usleep(30);
                $batch = [];
            }
        }

        if (!empty($batch)) {
            BookSearch::insert($batch);
        }

        $dbSearch->close();
    }

    private function cleanupLocalFiles($book, $localZipPath)
    {
        // Delete the downloaded zip file
        if (file_exists($localZipPath)) {
            unlink($localZipPath);
            Log::info('Deleted downloaded zip file: ' . $localZipPath);
        }

        // Delete the extracted directory and its contents
        $folderPath = Storage::disk('public')->path($book->id);
        if (is_dir($folderPath)) {
            Storage::disk('public')->deleteDirectory($book->id);
            Log::info('Deleted extracted directory: ' . $folderPath);
        }
    }

    private function failProcess($uuid, $title, $message)
    {
        GlobalNotification::updateNotification($title, $message, 'danger', 0, $uuid);
        throw new \Exception($message);
    }

    public function failed(\Exception $e)
    {
        if ($this->job != null) {
            GlobalNotification::updateNotification('حدث خطأ', $e->getMessage(), 'danger', 0, $this->job->uuid());
        }
    }
}
