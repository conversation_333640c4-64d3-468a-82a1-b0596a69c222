<?php

namespace App\Jobs;

use App\Helpers\BookContentStyleHelper;
use App\Helpers\TextHelper;
use App\Http\Controllers\CachePurgeController;
use App\Http\Controllers\Front\BookPageController;
use App\Models\Book;
use App\Models\BookContent;
use App\Models\BookSearch;
use App\Models\BookTitle;
use App\Models\GlobalNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class ProcessBook implements ShouldQueue
{
    public $timeout = 600; // Set timeout to 10 minutes
    use Queueable;
    protected Book $book;
    protected $enableUpdateDate;

    public function __construct(Book $book, $enableUpdateDate = false)
    {
        $this->book = $book;
        $this->enableUpdateDate = $enableUpdateDate;
    }

    public function handle()
    {
        $uuid = $this->job->uuid();
        $book = $this->book;
        $filePath = $book->sqlite ?? '';
        if (empty($filePath)) {
            $this->failProcess($uuid, 'فشلت عملية فك الضغط', 'File path is empty.');
        }
        $zipFileNameWithExtension = pathinfo($filePath, PATHINFO_BASENAME);
        $fileName = pathinfo($filePath, PATHINFO_FILENAME);
        $fileInStorage = Storage::disk('public')->path($zipFileNameWithExtension);

        $this->unzipFile($fileInStorage, $book, $uuid);

        $this->renameSqliteFileIfNeeded($book, $fileName);
        $this->renameDbFileIfNeeded($book, $fileName);

        $dbPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
        if (!file_exists($dbPath)) {
            $this->failProcess($uuid, 'فشلت عملية تحويل الكتاب', 'Database file does not exist.');
        }

        $db = new \SQLite3($dbPath);
        $this->checkRequiredColumns($db, $book, $uuid);

        $this->insertBookContent($db, $book, $uuid);

        $this->addContentToBookSearch($db, $book, $uuid, $book->id);

        $db->close();
        GlobalNotification::updateNotification('جاري رفع الملفات', 'يتم الان رفع الملفات الى السيرفر', 'info', 90, $uuid);
        $this->moveFilesToSpace($book, $zipFileNameWithExtension);

        //*clear any cache for the book
        //* clear cache for book reads page
        BookPageController::clearSpecificBookCache($book->hashId);
        //* clear/purge cache from CDN
        $cachePurgeController = new CachePurgeController();
        $cachePurgeController->purgeBookCache($book->id);

        GlobalNotification::updateNotification('تمت العملية بنجاح', 'تمت عملية معالجة الكتاب ' . $book->title, 'success', 100, $uuid);
    }

    private function unzipFile($fileInStorage, $book, $uuid)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان فك الضغط عن الكتاب', 'info', 10, $uuid);
        sleep(1);

        Log::info('fileInStorage: ' . $fileInStorage);
        if (!file_exists($fileInStorage) || pathinfo($fileInStorage, PATHINFO_EXTENSION) !== 'zip') {
            $this->failProcess($uuid, 'فشلت عملية فك الضغط', 'File does not exist or is not a zip file.');
        }

        //*create directory if not exists
        if (!Storage::disk('public')->exists($book->id)) {
            Storage::disk('public')->makeDirectory($book->id);
        }

        $zip = new ZipArchive();
        if ($zip->open($fileInStorage) === true) {
            $zip->extractTo(Storage::disk('public')->path($book->id));
            $zip->close();
        } else {
            $this->failProcess($uuid, 'فشلت عملية فك الضغط', 'Failed to unzip file.');
        }

        //* check if there is sqlite file in the folder and rename it to $book->id.sqlite
        $files = Storage::disk('public')->files($book->id);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'sqlite') {
                $oldPath = Storage::disk('public')->path($book->id) . '/' . pathinfo($file, PATHINFO_BASENAME);
                $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
                rename($oldPath, $newPath);
            }
        }
        //* check if there is db file in the folder and rename it to $book->id.db
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'db') {
                $oldPath = Storage::disk('public')->path($book->id) . '/' . pathinfo($file, PATHINFO_BASENAME);
                $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.db';
                rename($oldPath, $newPath);
            }
        }
    }

    private function renameSqliteFileIfNeeded($book, $fileName)
    {
        $checkIfExit = Storage::disk('public')->exists($book->id . '/' . $book->id . '.sqlite');
        $checkIfExitOrignal = Storage::disk('public')->exists($book->id . '/' . $fileName . '.sqlite');
        if (!$checkIfExit && $checkIfExitOrignal) {
            $oldPath = Storage::disk('public')->path($book->id) . '/' . $fileName . '.sqlite';
            $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.sqlite';
            rename($oldPath, $newPath);
        }
    }

    private function renameDbFileIfNeeded($book, $fileName)
    {
        $checkIfExit = Storage::disk('public')->exists($book->id . '/' . $book->id . '.db');
        $checkIfExitOrignal = Storage::disk('public')->exists($book->id . '/' . $fileName . '.db');
        if (!$checkIfExit && $checkIfExitOrignal) {
            $oldPath = Storage::disk('public')->path($book->id) . '/' . $fileName . '.db';
            $newPath = Storage::disk('public')->path($book->id) . '/' . $book->id . '.db';
            rename($oldPath, $newPath);
        }
    }

    private function checkRequiredColumns($db, $book, $uuid)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان تاكد من وجود الاعمدة اللازمة', 'info', 30, $uuid);
        sleep(1);

        $requiredColumnsForBookTable = ['id', 'nass', 'page', 'part'];
        $requiredColumnsForTitleTable = ['id', 'tit', 'lvl'];

        $this->checkTableColumns($db, 'book', $requiredColumnsForBookTable, $book, $uuid);
        $this->checkTableColumns($db, 'title', $requiredColumnsForTitleTable, $book, $uuid);
    }

    private function checkTableColumns($db, $tableName, $requiredColumns, $book, $uuid)
    {
        $result = $db->query("PRAGMA table_info($tableName)");
        $columns = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $columns[] = $row['name'];
        }

        if (count(array_diff($requiredColumns, $columns)) > 0) {
            $this->failProcess($uuid, 'فشلت عملية تحويل الكتاب', "Required columns do not exist in the $tableName table.");
        }
    }

    private function insertBookContent($db, $book, $uuid)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان اضافة محتوي الكتاب الى قاعدة بيانات الكتب المجمعة', 'info', 60, $uuid);

        BookContent::where('book_id', $book->id)->delete();
        BookTitle::where('book_id', $book->id)->delete();

        $this->insertTitles($db, $book);
        $this->insertContents($db, $book);
    }

    private function insertTitles($db, $book)
    {
        $batch = [];
        $batchSize = 100;
        $titlesResult = $db->query('SELECT * FROM title');
        $titles = [];

        while ($row = $titlesResult->fetchArray(SQLITE3_ASSOC)) {
            $batch[] = [
                'title_id' => $row['id'],
                'book_id' => $book->id,
                'title' => TextHelper::cleanText($row['tit']),
                'level' => $row['lvl'],
                'mno' => $row['mno'] ?? ($row['Mno'] ?? null),
                'hno' => $row['hno'] ?? ($row['Hno'] ?? null),
            ];

            if (count($batch) >= $batchSize) {
                BookTitle::insert($batch);
                $batch = [];
            }

            $titles[] = [
                'title_id' => $row['id'],
                'title' => $row['tit'],
                'level' => $row['lvl'],
            ];
        }

        if (!empty($batch)) {
            BookTitle::insert($batch);
        }
    }

    private function insertContents($db, $book)
    {
        $batch = [];
        $batchSize = 100;
        $titles = BookTitle::where('book_id', $book->id)->get();
        $result = $db->query('SELECT * FROM book');
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $data = (object) [
                'nass' => TextHelper::cleanText($row['nass']),
                'page_id' => $row['id'],
            ];

            $batch[] = [
                'book_id' => $book->id,
                'page_id' => $row['id'],
                'page' => $row['page'],
                'part' => $row['part'],
                'pPage' => $row['ppage'] ?? null,
                'pPart' => $row['ppart'] ?? null,
                'nass' => $row['nass'] == null ? '' : TextHelper::cleanText($row['nass']),
                'html' => BookContentStyleHelper::applyStyle($data, $titles),
                'hno' => $row['hno'] ?? ($row['Hno'] ?? null),
                'mno' => $row['mno'] ?? ($row['Mno'] ?? null),
            ];

            if (count($batch) >= $batchSize) {
                BookContent::insert($batch);
                $batch = [];
            }
        }

        if (!empty($batch)) {
            BookContent::insert($batch);
        }
    }

    private function addContentToBookSearch($db, $book, $uuid, $fileName)
    {
        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'جاري حذف محتوي الكتاب القديم من قاعدة بيانات [FTS]', 'info', 70, $uuid);
        Log::info('addContentToBookSearch delete old content from FTS: ' . $book->id);
        try {
            BookSearch::where('book_id', $book->id)->chunkById(100, function ($records) {
                $ids = $records->pluck('id');
                BookSearch::whereIn('id', $ids)->delete();
                usleep(100);
            });
        } catch (\Exception $e) {
            Log::error('Error deleting old content from FTS: ' . $e->getMessage());
            $this->failProcess($uuid, 'فشلت عملية حذف محتوي الكتاب القديم', $e->getMessage());
        }
        Log::info('addContentToBookSearch end delete old content from FTS: ' . $book->id);

        GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان اضافة محتوي الكتاب الى قاعدة بيانات [FTS]', 'info', 80, $uuid);
        sleep(1);
        $folderPath = Storage::disk('public')->path($book->id);
        $sqliteFilePath = $folderPath . '/' . $fileName . '.sqlite';

        try {
            Log::info('addContentToBookSearch create new sqlite file: ' . $sqliteFilePath);
            // Create the FTS database and zip file
            TextHelper::createNewSqliteFile($sqliteFilePath);
            Log::info('addContentToBookSearch create new sqlite file end: ' . $sqliteFilePath);
            // Create a new SQLite file with cleaned text
            TextHelper::createDbForFtsUseAndZipFile($folderPath, $sqliteFilePath, $book->hashId);
        } catch (\Exception $e) {
            Log::error('Error creating new SQLite file: ' . $e->getMessage());
            $this->failProcess($uuid, 'فشلت عملية تحويل الكتاب', $e->getMessage());
        }

        $dbPathSearch = Storage::disk('public')->path($book->id) . '/' . $fileName . '.db';
        $dbSearch = new \SQLite3($dbPathSearch);
        $resultSearch = $dbSearch->query('SELECT * FROM book');

        $batchSize = 100;
        $batch = [];
        $i = 0;
        while ($row = $resultSearch->fetchArray(SQLITE3_ASSOC)) {
            $i++;
            $batch[] = [
                'book_id' => $book->id,
                'book_title' => $book->title,
                'page' => $row['page'],
                'page_id' => $row['id'],
                'page_title' => $row['tit'] ?? null,
                'part' => $row['part'],
                'search' => $row['nass'] ?? '',
                'search_root' => $row['nass_root'] ?? null,
                'search_footer' => $row['nass_footer'] ?? ($row['nass_footnote'] ?? null),
                'search_footer_root' => $row['nass_footer_root'] ?? ($row['nass_footnote_root'] ?? null),
                'hno' => $row['hno'] ?? ($row['Hno'] ?? null),
                'mno' => $row['mno'] ?? ($row['Mno'] ?? null),
            ];

            if (count($batch) >= $batchSize) {
                GlobalNotification::updateNotification('جاري معالجة الكتاب', 'يتم الان اضافة محتوي الكتاب الى قاعدة بيانات [FTS] ' . $i, 'info', 80, $uuid);
                BookSearch::insert($batch);
                usleep(30);
                $batch = [];
            }
        }

        if (!empty($batch)) {
            BookSearch::insert($batch);
        }
    }

    private function moveFilesToSpace($book, $zipFileNameWithExtension)
    {
        $folderPath = Storage::disk('public')->path($book->id);
        //* remove .sqlite && .db as not needed
        if (file_exists($folderPath . '/' . $book->id . '.sqlite')) {
            unlink($folderPath . '/' . $book->id . '.sqlite');
        }
        if (file_exists($folderPath . '/' . $book->id . '.db')) {
            unlink($folderPath . '/' . $book->id . '.db');
        }

        $files = Storage::disk('public')->files($book->id);

        //* delete files .zip .sqlite .db
        $filesInSpaces = Storage::disk('spaces')->files('books/' . $book->id);
        foreach ($filesInSpaces as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'zip' || pathinfo($file, PATHINFO_EXTENSION) === 'sqlite' || pathinfo($file, PATHINFO_EXTENSION) === 'db') {
                Storage::disk('spaces')->delete($file);
            }
        }

        //* purge cache from CDN
        $cachePurgeController = new CachePurgeController();
        $cachePurgeController->purgeBookCache($book->id);

        foreach ($files as $file) {
            $fileName = pathinfo($file, PATHINFO_BASENAME);
            Storage::disk('spaces')->putFileAs('books/' . $book->id, $folderPath . '/' . $fileName, $fileName);
        }

        Storage::disk('public')->deleteDirectory($book->id);
        //*delete zip file
        Storage::disk('public')->delete($zipFileNameWithExtension);
        $random = rand(1, 99999);
        Book::where('id', $book->id)->update([
            'sqlite' => config('app.url') . '/api/download/book/' . $book->hashId . '/' . $random,
        ]);
        if ($this->enableUpdateDate) {
            Book::where('id', $book->id)->update([
                'updated_at' => now(),
            ]);
        }
    }

    private function failProcess($uuid, $title, $message)
    {
        GlobalNotification::updateNotification($title, $message, 'danger', 0, $uuid);
        throw new \Exception($message);
    }

    public function failed(\Exception $e)
    {
        if ($this->job != null) {
            GlobalNotification::updateNotification('حدث خطأ', $e->getMessage(), 'danger', 0, $this->job->uuid());
        }
    }
}
